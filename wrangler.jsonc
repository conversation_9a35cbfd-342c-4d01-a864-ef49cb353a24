{"$schema": "node_modules/wrangler/config-schema.json", "name": "finserv-web", "assets": {"directory": "dist", "not_found_handling": "single-page-application"}, "compatibility_flags": ["nodejs_compat"], "compatibility_date": "2025-07-30", "observability": {"enabled": true, "head_sampling_rate": 1, "logs": {"enabled": true, "head_sampling_rate": 1}}, "placement": {"mode": "smart"}, "routes": [{"pattern": "next-staging.stablemoney.in", "custom_domain": true}], "upload_source_maps": true, "env": {"production": {"routes": [{"pattern": "next.stablemoney.in", "custom_domain": true}]}}}