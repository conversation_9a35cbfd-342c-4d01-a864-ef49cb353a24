{"name": "finserv-web", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && tsc --noEmit", "start": "vite start", "lint": "eslint . --fix", "preview": "vite preview", "generate:identity-proto": "npx buf generate --template buf.gen-identity.yaml", "generate:business-proto": "npx buf generate --template buf.gen-business.yaml", "generate:openapi": "orval --prettier --config orval.config.cjs"}, "dependencies": {"@bufbuild/buf": "^1.55.1", "@bufbuild/protobuf": "^2.6.2", "@bufbuild/protoc-gen-es": "^2.6.2", "@eslint/js": "^9.32.0", "@lottiefiles/dotlottie-react": "^0.17.1", "@react-hook/resize-observer": "^2.0.2", "@sentry/react": "^10.5.0", "@sentry/vite-plugin": "^4.1.1", "@tanstack/query-sync-storage-persister": "^5.83.0", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-router": "^1.130.2", "@tanstack/react-router-devtools": "^1.130.2", "@tanstack/react-router-with-query": "^1.130.2", "@xstate/react": "^6.0.0", "@zag-js/accordion": "^1.21.1", "@zag-js/dialog": "^1.21.1", "@zag-js/react": "^1.21.1", "@zag-js/toast": "^1.21.1", "@zag-js/toggle-group": "^1.21.1", "change-case": "^5.4.4", "dayjs": "^1.11.13", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "js-cookie": "^3.0.5", "mixpanel-browser": "^2.67.0", "my-ua-parser": "^2.0.4", "node-forge": "^1.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0", "redaxios": "^0.5.1", "tailwind-merge": "^2.6.0", "typescript-eslint": "^8.38.0", "vaul": "^1.1.2", "wrangler": "^4.26.1", "xstate": "^5.20.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@tanstack/router-plugin": "^1.131.27", "@types/js-cookie": "^3.0.6", "@types/node": "^22.5.4", "@types/node-forge": "^1.3.13", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "orval": "^7.11.2", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.11", "tailwindcss-safe-area": "^1.0.0", "typescript": "^5.7.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}