/// <reference types="vite/client" />
import {
  HeadContent,
  Outlet,
  createRootRouteWithContext,
} from "@tanstack/react-router";
import * as React from "react";
import type { QueryClient } from "@tanstack/react-query";
import { LanguageProvider } from "@/contexts/language-context";
import { usePageViewTracking } from "@/hooks/page-view-tracking";
import Toaster from "@/components/ui/toast/toaster";
import { useUserQuery } from "@/hooks/user";
import { useUpdateChecker } from "@/machines/update-checker";
import { useBodyClasses, useFloatingFooter } from "@/hooks/body-classes";
import { useStatusBar } from "@/hooks/status-bar";

export const Route = createRootRouteWithContext<{
  queryClient: QueryClient;
}>()({
  component: RootComponent,
});

function RootComponent() {
  return (
    <RootDocument>
      <LanguageProvider>
        <Outlet />
      </LanguageProvider>
    </RootDocument>
  );
}

function RootDocument({ children }: { children: React.ReactNode }) {
  useUpdateChecker();
  useUserQuery();
  useFloatingFooter();
  useBodyClasses();
  usePageViewTracking();
  useStatusBar(false);
  return (
    <>
      <HeadContent />
      {children}
      <Toaster />
    </>
  );
}
