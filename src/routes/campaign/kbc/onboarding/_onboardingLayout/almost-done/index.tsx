import {
  LanguageCode,
  Step,
  UserQuizAnswerSchema,
} from "@/clients/gen/business/campaign_pb";
import { OnboardingToggleGroup } from "@/components/campaign/functional/onboarding-toggle-group/onboarding-toggle-group";
import { toaster } from "@/components/ui/toast/store";
import { getQuizStatusQueryOptions, quizOnboardingSubmit } from "@/queries/kbc";
import { getErrorMessage } from "@/utils/errors";
import { create } from "@bufbuild/protobuf";
import { useMutation, useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useMemo, useState } from "react";
import { lastQuestionOptions } from "../-onboarding-data/onboarding-data";
import { useLanguage } from "@/contexts/language-context";
import { queryClient } from "@/queries/client";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { closeWebview } from "@/utils/routing";

type OnboardingSearch = {
  answers?: string[];
};

export const Route = createFileRoute(
  "/campaign/kbc/onboarding/_onboardingLayout/almost-done/"
)({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): OnboardingSearch => {
    return {
      answers: searchParams.answers as string[],
    };
  },
});

function RouteComponent() {
  const { answers = [] } = Route.useSearch();
  const { language } = useLanguage();
  const questionNumber = useMemo(() => {
    if (!answers || answers.length === 0) {
      return 0;
    }
    const lastAnswer = answers[answers.length - 1];
    const selectedOptionId = lastAnswer.split(":")[1];
    return lastAnswer && selectedOptionId.includes("A") ? 0 : 1;
  }, [answers]);
  const [isNavigating, setIsNavigating] = useState(false);
  const navigate = useNavigate();
  useEffect(() => {
    trackEvent("kbc_onb_question_viewed", {
      question_number: `5${questionNumber === 0 ? "a" : "b"}`,
      language: getLanguageFromEnum(language),
    });
  });
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  if (
    quizStatus?.nextStep?.nextStep === Step.POLL ||
    quizStatus?.nextStep?.nextStep === Step.QUIZ
  ) {
    closeWebview();
  }
  const onboardingSubmitMutation = useMutation({
    mutationFn: quizOnboardingSubmit,
    onSuccess: (_, onboardingAnswers) => {
      trackEvent("kbc_onb_questions_submitted", {
        answers: onboardingAnswers,
        language: getLanguageFromEnum(language),
        user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      });
      queryClient.invalidateQueries(getQuizStatusQueryOptions()).then(() => {
        trackEvent("kbc_onb_completed", {
          language: getLanguageFromEnum(language),
          user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
        });
      });
      navigate({
        to: "/campaign/kbc/onboarding/persona",
        search: { type: questionNumber === 0 ? "save" : "growth" },
      });
    },
    onError: async (error) => {
      setIsNavigating(false);
      toaster.create({
        type: "error",
        description: await getErrorMessage(error),
      });
    },
  });
  const handleLastQuestion = (value: string[]) => {
    setIsNavigating(true);
    const currentQuestion = lastQuestionOptions[language][questionNumber];
    const selectedOption = currentQuestion.options.find(
      (opt) => opt.value === value[0]
    );
    trackEvent("kbc_onb_option_tapped", {
      question_number: `5${questionNumber === 0 ? "a" : "b"}`,
      option_selected: selectedOption?.label,
      count_selected: 1,
      language_name: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    const currentAnswer = `${currentQuestion.id}:${value[0]}:${selectedOption?.label || ""}`;
    const onboardingAnswers = [...answers, currentAnswer].map((answer) => {
      const [questionId, selectedOptionId, selectedOptionText] =
        answer.split(":");
      return create(UserQuizAnswerSchema, {
        questionId,
        selectedOptionId,
        selectedOptionText: selectedOptionText || "",
      });
    });
    onboardingSubmitMutation.mutate(onboardingAnswers);
  };

  return (
    <div className="flex flex-col gap-6 pt-7">
      <div className="flex flex-col gap-4 text-white items-center">
        <div className="flex items-center justify-center gap-2">
          <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50" />
          {language === LanguageCode.EN ? (
            <p className="uppercase text-title-all-caps font-medium">
              Almost done
            </p>
          ) : (
            <p> प्रोफ़ाइल 5/5</p>
          )}
          <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF] to-[#FFFFFF00] opacity-50" />
        </div>

        <p className="text-heading2 text-center space-y-1.5">
          {lastQuestionOptions[language][questionNumber].question}{" "}
          {lastQuestionOptions[language][questionNumber].multiple &&
            "(Select all that apply)"}
        </p>
      </div>

      <OnboardingToggleGroup
        key={lastQuestionOptions[language][questionNumber].id}
        options={lastQuestionOptions[language][questionNumber].options}
        multiple={lastQuestionOptions[language][questionNumber].multiple}
        deselectable={lastQuestionOptions[language][questionNumber].multiple}
        disabled={isNavigating}
        onChange={(details) => handleLastQuestion(details.value)}
      />
    </div>
  );
}
