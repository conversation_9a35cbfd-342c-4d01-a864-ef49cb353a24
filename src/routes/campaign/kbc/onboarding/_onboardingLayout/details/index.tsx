import { OnboardingToggleGroup } from "@/components/campaign/functional/onboarding-toggle-group/onboarding-toggle-group";
import FloatingGoldButton from "@/components/campaign/ui/floating-gold-button";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { onboardingQuestionsOptions } from "../-onboarding-data/onboarding-data";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { useSuspenseQuery } from "@tanstack/react-query";

type OnboardingSearch = {
  question?: number;
  answers?: string[];
};

export const Route = createFileRoute(
  "/campaign/kbc/onboarding/_onboardingLayout/details/"
)({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): OnboardingSearch => {
    return {
      question: Number(searchParams?.question ?? 0),
      answers: searchParams.answers as string[],
    };
  },
});

function RouteComponent() {
  useEffect(() => {
    trackEvent("kbc_onb_question_viewed", {
      question_number: question + 1,
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  });
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { language } = useLanguage();
  const { question = 0, answers = [] } = Route.useSearch();
  const navigate = useNavigate();
  const [selectedAnswers, setSelectedAnswers] = useState<string[]>([]);
  const [showConfirm, setShowConfirm] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const handleChange = (value: string[]) => {
    setSelectedAnswers(value);
    const currentQuestion = onboardingQuestionsOptions[language][question];
    if (currentQuestion.multiple) {
      setShowConfirm(value.length > 0);
    } else {
      setIsNavigating(true);
      const selectedOption = currentQuestion.options.find(
        (opt) => opt.value === value[0]
      );
      trackEvent("kbc_onb_option_tapped", {
        question_number: question + 1,
        option_selected: selectedOption?.label,
        count_selected: 1,
        language_name: getLanguageFromEnum(language),
        user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      });
      setTimeout(() => {
        const newAnswer = `${currentQuestion.id}:${value[0]}:${selectedOption?.label || ""}`;
        const newAnswers = [...answers, newAnswer];
        navigate({
          to: "/campaign/kbc/onboarding/details",
          search: { question: question + 1, answers: newAnswers },
        });
        setIsNavigating(false);
      }, 500);
    }
  };

  const handleConfirm = () => {
    setIsNavigating(true);
    const currentQuestion = onboardingQuestionsOptions[language][question];
    const selectedLabels = selectedAnswers.map(
      (value) =>
        currentQuestion.options.find((opt) => opt.value === value)?.label || ""
    );
    const answerString = selectedAnswers.join(",");
    const labelString = selectedLabels.join(",");
    const newAnswer = `${currentQuestion.id}:${answerString}:${labelString}`;
    const newAnswers = [...answers, newAnswer];
    trackEvent("kbc_onb_option_tapped", {
      question_number: question + 1,
      option_selected: labelString,
      count_selected: selectedAnswers.length,
      language_name: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    navigate({
      to: "/campaign/kbc/onboarding/almost-done",
      search: { answers: newAnswers },
    });
  };

  return (
    <div className="flex flex-col gap-6 mb-safe-offset-8 pt-7">
      <div className="flex flex-col gap-3 text-white items-center">
        <div className="flex items-center justify-center gap-2">
          <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50" />
          {language === LanguageCode.EN ? (
            <p className="text-title-all-caps font-medium">
              Profile {question + 1}/
              {onboardingQuestionsOptions[language].length + 1}
            </p>
          ) : (
            <p>
              प्रोफ़ाइल {question + 1}/
              {onboardingQuestionsOptions[language].length + 1}
            </p>
          )}
          <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF] to-[#FFFFFF00] opacity-50" />
        </div>

        <div className="text-heading2 text-center space-y-1.5">
          <p>{onboardingQuestionsOptions[language][question].question}</p>
          {onboardingQuestionsOptions[language][question].multiple && (
            <p className="text-heading4">
              {" "}
              {language === LanguageCode.EN
                ? "(Select all that apply)"
                : "(एक से अधिक विकल्प चुन सकते हैं)"}
            </p>
          )}
        </div>
      </div>

      <OnboardingToggleGroup
        key={onboardingQuestionsOptions[language][question].id}
        options={onboardingQuestionsOptions[language][question].options}
        multiple={onboardingQuestionsOptions[language][question].multiple}
        deselectable={onboardingQuestionsOptions[language][question].multiple}
        disabled={isNavigating}
        onChange={(details) => handleChange(details.value)}
      />
      {onboardingQuestionsOptions[language][question].multiple &&
        showConfirm && (
          <FloatingGoldButton onClick={handleConfirm} style={"primary"}>
            <p className="text-heading4  text-black-80"> Confirm</p>
          </FloatingGoldButton>
        )}
    </div>
  );
}
