import savePersona from "@/assets/icons/personas/save-persona.svg";
import growthPersona from "@/assets/icons/personas/growth-persona.svg";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";

export const personaList = {
  [LanguageCode.EN]: [
    {
      // If user selects FD + any other instrument like MF, Gold etc.
      title: "The Strategic Saver",
      description:
        "You prefer FD's stability with growth and that's what makes you a strategic saver.",
      segue:
        "Play Stable Money KBC FD contest now and check your knowledge about Fixed Deposits.",
      type: "strategic",
      icon: savePersona,
    },
    {
      // If user does not select FD as one of the options
      title: "The Growth Chaser",
      description:
        "While FD can give you stability, you prefer to take risks and choose options that give you higher returns.",
      segue:
        "Play Stable Money KBC FD contest now and check your knowledge about Fixed Deposits",
      type: "growth",
      icon: growthPersona,
    },
  ],
  [LanguageCode.HI]: [
    {
      title: "स्ट्रैटेजिक सेवर",
      description: "FD की स्थिरता और बढ़त बनाती है आपको एक स्ट्रैटेजिक सेवर",
      segue: "खेलें Stable Money KBC FD कॉन्टेस्ट और परखें अपनी FD नॉलेज",
      type: "strategic",
      icon: savePersona,
    },
    {
      // If user does not select FD as one of the options
      title: "ग्रोथ चेज़र",
      description:
        "आप थोड़े रिस्क के साथ ऊँचे रिटर्न का रास्ता चुनते हैं। FD आपकी ग्रोथ में स्थिरता जोड़ सकती है।",
      segue: "खेलें Stable Money KBC FD कॉन्टेस्ट और परखें अपनी FD नॉलेज",
      type: "growth",
      icon: growthPersona,
    },
  ],
  [LanguageCode.UNKNOWN_LANGUAGE_CODE]: [],
};
