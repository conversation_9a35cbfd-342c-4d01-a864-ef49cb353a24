import ProgressLoader from "@/components/campaign/functional/progress-loader";
import {
  createFileRoute,
  useLocation,
  useNavigate,
} from "@tanstack/react-router";
import goldTick from "@/assets/icons/gold-tick.svg";
import FloatingGoldButton from "@/components/campaign/ui/floating-gold-button";
import cloverDivider from "@/assets/illustrations/clover-divider.svg";
import { personaList } from "./-personas/personas";
import { useLanguage } from "@/contexts/language-context";
import { useUserQuery } from "@/hooks/user";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { useEffect } from "react";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { getUserProfileQueryOptions } from "@/queries/profile";

type PersonaSearch = {
  type: "save" | "growth";
};

const PendingComponent = () => {
  const { language } = useLanguage();
  const userProfile = useSuspenseQuery(getUserProfileQueryOptions());
  return (
    <ProgressLoader>
      <div className="text-heading2 text-white text-center flex flex-col items-center gap-4 px-5">
        <img src={goldTick} alt="Gold Tick" className="w-12 h-12" />
        {language === LanguageCode.EN ? (
          <div>
            <p>
              {" "}
              Thanks for sharing,{" "}
              {userProfile.data?.data?.firstName.toLowerCase()}!
            </p>
            <p>
              {" "}
              Let’s see what kind of investor <br />
              you are
            </p>
          </div>
        ) : (
          <div>
            <p> धन्यवाद {userProfile.data?.data?.firstName}! चलिए पता करते</p>
            <p>हैं कि आप किस प्रकार के निवेशक ह</p>
          </div>
        )}
      </div>
    </ProgressLoader>
  );
};

export const Route = createFileRoute(
  "/campaign/kbc/onboarding/_onboardingLayout/persona/"
)({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): PersonaSearch => {
    return {
      type: searchParams.type as PersonaSearch["type"],
    };
  },
  loader: async () => {
    await new Promise((resolve) => setTimeout(resolve, 3000));
  },
  pendingComponent: PendingComponent,
});

function RouteComponent() {
  const { type } = Route.useSearch();
  const { language } = useLanguage();
  const navigate = useNavigate();
  const { profileQuery } = useUserQuery();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const location = useLocation();
  const handleQuizSubmit = () => {
    navigate({
      to: "/campaign/kbc/quiz-loader",
      replace: true,
    });
    trackEvent("kbc_start_fdquiz_button_clicked", {
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  };
  useEffect(() => {
    trackEvent("kbc_persona_screen_viewed", {
      persona_type: type,
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      screen_name: location.pathname,
    });
  });
  const persona =
    personaList[language].find((persona) => persona.type === type) ||
    personaList[language][0];
  return (
    <div className="flex flex-col items-center text-white pt-7 gap-15">
      <div className="flex items-center justify-center gap-2">
        <div className="h-[1px] w-[40px] bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50" />
        <p className=" text-center">
          {language === LanguageCode.EN ? (
            <span className="text-title-all-caps uppercase">
              {profileQuery.data?.data?.firstName}'s Investor Persona
            </span>
          ) : (
            <span> {profileQuery.data?.data?.firstName} आपका निवेशक रूप</span>
          )}
        </p>
        <div className="h-[1px] w-[40px] bg-linear-to-r from-[#FFFFFF] to-[#FFFFFF00] opacity-50" />
      </div>
      <div className="flex flex-col items-center gap-8">
        <div className="flex flex-col items-center gap-6">
          <img src={persona.icon} alt="Persona Icon" className="size-24" />
          <div className="space-y-2 text-center ">
            <p className="text-heading2 ">{persona.title}</p>
            <p className="text-body1">{persona.description}</p>
          </div>
          <img src={cloverDivider} alt="Clover Divider" />
        </div>
        <p className="text-body1 text-center">{persona.segue}</p>
      </div>
      <FloatingGoldButton onClick={handleQuizSubmit}>
        <p className="text-hading4 font-medium text-black-80">
          {language === LanguageCode.EN
            ? "Start FD contest now"
            : "KBC कॉन्टेस्ट शुरू करें"}
        </p>
      </FloatingGoldButton>
    </div>
  );
}
