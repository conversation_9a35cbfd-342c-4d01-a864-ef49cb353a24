import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { useLanguage } from "@/contexts/language-context";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import FloatingGoldButton from "@/components/campaign/ui/floating-gold-button";
import engTick from "@/assets/illustrations/language-switch-button/english-tick.svg";
import hindiTick from "@/assets/illustrations/language-switch-button/hindi-tick.svg";
import english from "@/assets/illustrations/language-switch-button/english.svg";
import hindi from "@/assets/illustrations/language-switch-button/hindi.svg";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";

export const Route = createFileRoute(
  "/campaign/kbc/onboarding/_onboardingLayout/know-you/"
)({
  component: RouteComponent,
});

function RouteComponent() {
  const { language, setLanguage } = useLanguage();
  const navigate = useNavigate();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const handleStartOnboarding = () => {
    trackEvent("kbc_language_pre_selection", {
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    navigate({
      to: "/campaign/kbc/onboarding/details",
      replace: true,
    });
  };
  return (
    <>
      <div className="flex flex-col items-center justify-center gap-3 ">
        <div className="text-heading1 text-white text-center flex flex-col items-center justify-center absolute inset-0 top-1/2 -translate-y-1/2">
          {language === LanguageCode.EN ? (
            <>
              <p> Let’s get to know you a little </p>
              <p> before the contest begins!</p>
            </>
          ) : (
            <>
              <p>प्रतियोगिता शुरू होने से पहले आइए </p>
              <p> आपके बारे में थोड़ा जान लें!</p>
            </>
          )}
        </div>
        <div className="flex flex-col gap-4 items-center text-white absolute bottom-39 ">
          <div className="flex items-center justify-center gap-2">
            <div className="h-[1px] flex-1 bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50" />
            <p className=" text-center flex-shrink-0">
              {language === LanguageCode.EN ? (
                <span className="uppercase text-title-all-caps">
                  Select your preferred Language
                </span>
              ) : (
                "भाषा का चयन करें"
              )}
            </p>
            <div className="h-[1px] flex-1 w-[48px] bg-linear-to-r from-[#FFFFFF] to-[#FFFFFF00] opacity-50" />
          </div>
          <div className="flex justify-center items-center gap-1">
            <button onClick={() => setLanguage(LanguageCode.HI)}>
              <img
                src={language === LanguageCode.HI ? hindiTick : hindi}
                alt="Hindi"
              />
            </button>
            <button onClick={() => setLanguage(LanguageCode.EN)}>
              <img
                src={language === LanguageCode.EN ? engTick : english}
                alt="English"
              />
            </button>
          </div>
        </div>
      </div>
      <FloatingGoldButton onClick={handleStartOnboarding}>
        <span>{language === LanguageCode.EN ? "Continue" : "आगे बढ़ें"}</span>
      </FloatingGoldButton>
    </>
  );
}
