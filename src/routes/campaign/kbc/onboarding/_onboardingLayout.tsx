import { createFileRoute, Outlet, useLocation } from "@tanstack/react-router";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import LanguageToggle from "@/components/campaign/functional/language-toggle";
import { useBodyBackground } from "@/hooks/body-classes";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import { useLanguage } from "@/contexts/language-context";
import ProgressLoader from "@/components/campaign/functional/progress-loader";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import backgroundImage from "@/assets/illustrations/background-image.webp";

const PendingComponent = () => {
  const { language } = useLanguage();
  return (
    <ProgressLoader>
      <div className="text-heading1 text-white text-center">
        {language === LanguageCode.EN ? (
          <>
            <p> Let’s get to know you a little </p>
            <p> before the contest begins!</p>
          </>
        ) : (
          <>
            <p>प्रतियोगिता शुरू होने से पहल</p>
            <p> आइए आपको थोड़ा जान लें!</p>
          </>
        )}
      </div>
    </ProgressLoader>
  );
};
export const Route = createFileRoute(
  "/campaign/kbc/onboarding/_onboardingLayout"
)({
  component: RouteComponent,
  loader: async () => {
    await new Promise((resolve) => setTimeout(resolve, 3000));
  },
  pendingComponent: PendingComponent,
});

function RouteComponent() {
  useBodyBackground(backgroundImage);
  const location = useLocation();
  return (
    <>
      {location.pathname === "/campaign/kbc/onboarding/know-you" ? (
        <header
          className={"border-b-black-10 pt-safe sticky top-0 z-30 border-solid"}
        >
          <div className="flex items-center justify-center gap-4 px-5 py-4">
            <img src={appBarLogo} alt="" width={92} />
          </div>
        </header>
      ) : (
        <CampaignAppBar
          left={<BackAppbarAction />}
          right={<LanguageToggle />}
          scrollStyleOptions={
            location.pathname === "/campaign/kbc/onboarding/details"
              ? {
                  offsetStyles: {
                    backgroundColor: {
                      threshold: 5,
                      before: "transparent",
                      after: "#292114",
                    },
                    borderBottomWidth: {
                      threshold: 5,
                      before: 0,
                      after: "var(--border-w-sm)",
                    },
                  },
                }
              : {
                  offsetStyles: {
                    backgroundColor: {
                      threshold: 0,
                      before: "transparent",
                      after: "transparent",
                    },
                    borderBottomWidth: {
                      threshold: 0,
                      before: 0,
                      after: 0,
                    },
                  },
                }
          }
        >
          <img src={appBarLogo} alt="" width={92} />
        </CampaignAppBar>
      )}
      <div className="px-5">
        <Outlet />
      </div>
    </>
  );
}
