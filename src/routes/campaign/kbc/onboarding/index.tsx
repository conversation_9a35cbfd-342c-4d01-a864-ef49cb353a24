import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import FaqSupport from "@/components/campaign/functional/faq";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import LanguageToggle from "@/components/campaign/functional/language-toggle";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode, Step } from "@/clients/gen/business/campaign_pb";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import { trackEvent } from "@/utils/analytics";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import amitabh from "@/assets/illustrations/amitabh-onboarding.webp";
import amitabhHindi from "@/assets/illustrations/hindi/amitabh-onboarding.webp";
import prizesTop from "@/assets/illustrations/prizes-top.webp";
import prizesBottom from "@/assets/illustrations/prizes-bottom.webp";
import prizesTopHindi from "@/assets/illustrations/hindi/prizes-top.webp";
import prizesBottomHindi from "@/assets/illustrations/hindi/prizes-bottom.webp";
import winnersTop from "@/assets/illustrations/winners-top.webp";
import winnersTopHindi from "@/assets/illustrations/hindi/winners-top.webp";
import { useEffect } from "react";
import { closeWebview, openTNC } from "@/utils/routing";

export const Route = createFileRoute("/campaign/kbc/onboarding/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  if (
    quizStatus?.nextStep?.nextStep === Step.POLL ||
    quizStatus?.nextStep?.nextStep === Step.QUIZ
  ) {
    closeWebview();
  }
  useEffect(() => {
    trackEvent("kbc_intro_page_viewed", {
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  }, []);
  const handleOpenPdf = () => {
    openTNC();
  };
  return (
    <>
      <CampaignAppBar
        left={<BackAppbarAction opaque />}
        right={<LanguageToggle />}
      />
      <div className="bg-[#141009] body-behind-appbar">
        <img
          src={language === LanguageCode.EN ? amitabh : amitabhHindi}
          alt="Amitabh Loader"
        />
        <div className="floating-footer-padding">
          <div className="flex flex-col items-center">
            <img
              src={language === LanguageCode.EN ? prizesTop : prizesTopHindi}
              alt="Prizes top"
            />
            <img
              src={
                language === LanguageCode.EN ? prizesBottom : prizesBottomHindi
              }
              alt="Prizes bottom"
            />
            <Link
              onClick={() => {
                trackEvent("kbc_check_winner_clicked", {
                  language: getLanguageFromEnum(language),
                  user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
                  screen_name: location.pathname,
                });
              }}
              to="/campaign/kbc/leaderboard"
            >
              <img
                src={
                  language === LanguageCode.EN ? winnersTop : winnersTopHindi
                }
                alt="Winners top"
              />
            </Link>
            <FaqSupport />
          </div>
          <FloatingFooterContent>
            <div className="flex flex-col  gap-3">
              {language === LanguageCode.EN ? (
                <p className="text-body2 text-white-50 text-center">
                  By playing you hereby agree to the{" "}
                  <button className="text-white-80" onClick={handleOpenPdf}>
                    TnCs
                  </button>
                </p>
              ) : (
                <p className="text-body2 text-white-50 text-center">
                  खेल में भाग लेने पर, आप{" "}
                  <button className="text-white-80" onClick={handleOpenPdf}>
                    नियम और शर्तों
                  </button>
                  <span> से सहमत हैं</span>
                </p>
              )}
              <Link
                to="/campaign/kbc/onboarding/details"
                onClick={() => {
                  trackEvent("kbc_intropage_button_clicked", {
                    language: getLanguageFromEnum(language),
                    user_status: getKBCUserStatus(
                      quizStatus?.nextStep?.nextStep
                    ),
                  });
                }}
                className="w-full"
              >
                <button className="rounded-xl text-center bg-linear-to-tr from-[#E0B96C] to-[#FFEAAF] py-3 w-full font-medium">
                  {language === LanguageCode.EN
                    ? "Join KBC contest and win"
                    : "KBC कॉंटेस्ट खेलें और इनाम जीतें"}
                </button>
              </Link>
            </div>
          </FloatingFooterContent>
        </div>
      </div>
    </>
  );
}
