import { PendingComponent } from "@/components/campaign/functional/amitabh-pending-component";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect } from "react";

export const Route = createFileRoute("/campaign/kbc/quiz-loader/")({
  component: RouteComponent,
});

function RouteComponent() {
  const navigate = useNavigate();
  useEffect(() => {
    const handleQuizNavigation = () => {
      navigate({
        to: "/campaign/kbc/quiz",
        replace: true,
      });
    };
    const timer = setTimeout(handleQuizNavigation, 1500);
    return () => clearTimeout(timer);
  }, [navigate]);
  return <PendingComponent />;
}
