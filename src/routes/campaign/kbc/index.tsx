import { Step } from "@/clients/gen/business/campaign_pb";
import { PendingComponent } from "@/components/campaign/functional/amitabh-pending-component";
import { queryClient } from "@/queries/client";
import {
  getQuizQuestionsQueryOptions,
  getQuizStatusQueryOptions,
} from "@/queries/kbc";
import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute, Navigate } from "@tanstack/react-router";
import backgroundImage from "@/assets/illustrations/background-image.webp";
import amitabh from "@/assets/illustrations/amitabh-onboarding.webp";
import amitabhHindi from "@/assets/illustrations/hindi/amitabh-onboarding.webp";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import Back from "@/assets/icons/back.webp";
import whiteBack from "@/assets/icons/back-white.svg";
import { preload } from "react-dom";
import prizesTop from "@/assets/illustrations/prizes-top.webp";
import prizesBottom from "@/assets/illustrations/prizes-bottom.webp";
import prizesTopHindi from "@/assets/illustrations/hindi/prizes-top.webp";
import prizesBottomHindi from "@/assets/illustrations/hindi/prizes-bottom.webp";
import pollHeader from "@/assets/illustrations/poll/poll-header.webp";
import pollHeaderHindi from "@/assets/illustrations/hindi/poll-header.webp";
import answerPollButton from "@/assets/illustrations/poll/answer-poll-button.svg";
import answerPollButtonHindi from "@/assets/illustrations/hindi/answer-poll-button.svg";
import startButtonHindi from "@/assets/illustrations/hindi/start-button.svg";
import startButton from "@/assets/illustrations/start-button.svg";
import scoreLoader from "@/assets/illustrations/score/score-loader.mp4";
import PollToday from "@/assets/illustrations/poll/attempt/poll-today.webp";
import PollTodayHindi from "@/assets/illustrations/poll/attempt/poll-today-hi.webp";
import PollTomorrow from "@/assets/illustrations/poll/attempt/poll-tmmrw.webp";
import PollTomorrowHindi from "@/assets/illustrations/poll/attempt/poll-tmmrw-hi.webp";
import increase from "@/assets/illustrations/increase.svg";
import inviteFlow from "@/assets/illustrations/invite-flow.webp";
import amitabhHeader from "@/assets/illustrations/amitabh-birthday-header.webp";
import amitbahBottom from "@/assets/illustrations/amitabh-birthday-bottom.webp";
import referralPrizes from "@/assets/illustrations/referral-prizes.lottie?url";
import { getUserContextQueryOptions } from "@/queries/context";

export const Route = createFileRoute("/campaign/kbc/")({
  component: RouteComponent,
  loader: async () => {
    await Promise.all([
      queryClient.ensureQueryData(getQuizStatusQueryOptions()),
      queryClient.ensureQueryData(getQuizQuestionsQueryOptions()),
      queryClient.ensureQueryData(getUserContextQueryOptions()),
      new Promise((resolve) => setTimeout(resolve, 1500)),
    ]);
  },
  pendingComponent: PendingComponent,
});

function RouteComponent() {
  preload(amitabh, { as: "image" });
  preload(amitabhHindi, { as: "image" });
  preload(backgroundImage, { as: "image" });
  preload(appBarLogo, { as: "image" });
  preload(Back, { as: "image" });
  preload(prizesTop, { as: "image" });
  preload(prizesBottom, { as: "image" });
  preload(prizesTopHindi, { as: "image" });
  preload(prizesBottomHindi, { as: "image" });
  preload(pollHeader, { as: "image" });
  preload(pollHeaderHindi, { as: "image" });
  preload(whiteBack, { as: "image" });
  preload(startButton, { as: "image" });
  preload(startButtonHindi, { as: "image" });
  preload(answerPollButton, { as: "image" });
  preload(answerPollButtonHindi, { as: "image" });
  preload(scoreLoader, { as: "video", type: "video/mp4" });
  preload(PollToday, { as: "image" });
  preload(PollTodayHindi, { as: "image" });
  preload(PollTomorrow, { as: "image" });
  preload(PollTomorrowHindi, { as: "image" });
  preload(increase, { as: "image" });
  preload(inviteFlow, { as: "image" });
  preload(referralPrizes, {
    as: "fetch",
    type: "application/json",
  });
  preload(amitabhHeader, { as: "image" });
  preload(amitbahBottom, { as: "image" });

  const { data: status } = useSuspenseQuery(getQuizStatusQueryOptions());
  switch (status?.nextStep?.nextStep) {
    case Step.ONBOARDING:
      return <Navigate to="/campaign/kbc/onboarding" replace />;
    case Step.QUIZ:
      return <Navigate to="/campaign/kbc/quiz" replace />;
    case Step.POLL:
      return <Navigate to="/campaign/kbc/poll" replace />;
  }
  return null;
}
