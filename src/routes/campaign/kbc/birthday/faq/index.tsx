import { createFileRoute, useLocation } from "@tanstack/react-router";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import SupportAppbarAction from "@/components/functional/support-appbar-action";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import { useEffect } from "react";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useLanguage } from "@/contexts/language-context";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { useSuspenseQuery } from "@tanstack/react-query";
import Surface from "@/components/campaign/ui/surface/surface";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import faqText from "@/assets/illustrations/faq/faq-text.svg";
import faqTextHindi from "@/assets/illustrations/faq/hindi/faq-text.svg";
import AllBirthdayFaqs from "@/components/campaign/functional/all-birthday-faqs";

export const Route = createFileRoute("/campaign/kbc/birthday/faq/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const location = useLocation();
  useEffect(() => {
    trackEvent("kbc_viewall_faq_viewed", {
      screen_name: location.pathname,
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  });
  return (
    <div>
      <CampaignAppBar
        left={<BackAppbarAction />}
        right={<SupportAppbarAction />}
      >
        <img src={appBarLogo} alt="" width={92} />
      </CampaignAppBar>
      <div className="px-5 pt-7 flex items-center flex-col gap-5">
        {language === LanguageCode.EN ? (
          <img src={faqText} alt="FAQ Text" />
        ) : (
          <img src={faqTextHindi} alt="FAQ Text" />
        )}
        <Surface>
          <AllBirthdayFaqs />
        </Surface>
      </div>
    </div>
  );
}
