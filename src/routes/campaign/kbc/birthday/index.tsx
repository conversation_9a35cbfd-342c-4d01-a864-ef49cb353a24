import amitabhHeader from "@/assets/illustrations/amitabh-birthday-header.webp";
import amitbahBottom from "@/assets/illustrations/amitabh-birthday-bottom.webp";
import { createFileRoute, useLocation } from "@tanstack/react-router";
import FloatingGoldButton from "@/components/campaign/ui/floating-gold-button";
import { trackEvent } from "@/utils/analytics";

import { useLanguage } from "@/contexts/language-context";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import AppBar from "@/components/ui/app-bar/app-bar";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import BirthdayFaqSupport from "@/components/campaign/functional/birthday-faq";

export const Route = createFileRoute("/campaign/kbc/birthday/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const location = useLocation();
  const handleBirthdayContest = () => {
    trackEvent("kbc_participate_contest_clicked", {
      language: getLanguageFromEnum(language),
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });

    const googleFormUrl = "https://forms.gle/XnqnWmytJjrihn7o6";
    window.open(googleFormUrl, "_blank");
  };
  return (
    <>
      <AppBar left={<BackAppbarAction opaque />} />
      <div className="floating-footer-padding body-behind-appbar">
        <img src={amitabhHeader} alt="Amitabh Header" />
        <img src={amitbahBottom} alt="Amitabh Bottom" />
        <BirthdayFaqSupport />
        <FloatingGoldButton style="primary" onClick={handleBirthdayContest}>
          <p className="text-body1 text-black-80 font-medium">
            Participate in birthday contest
          </p>
        </FloatingGoldButton>
      </div>
    </>
  );
}
