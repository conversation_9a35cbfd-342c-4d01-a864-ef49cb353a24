import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import increase from "@/assets/illustrations/increase.svg";
import inviteFlow from "@/assets/illustrations/invite-flow.webp";
import referralPrizes from "@/assets/illustrations/referral-prizes.lottie?url";
import { createFileRoute } from "@tanstack/react-router";
import Animation from "@/components/ui/animation/animation";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { queryClient } from "@/queries/client";
import { personalizationPageQuery } from "@/queries/profile";
import { useEffect, useState } from "react";
import type { KbcReferralCampaignMetadata } from "@/clients/gen/identity/Campaign_pb";
import { getCampaign } from "@/queries/identity";
import { trackEvent } from "@/utils/analytics";
import * as native from "@/utils/native-integration";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useLanguage } from "@/contexts/language-context";

export const Route = createFileRoute("/campaign/kbc/referral/")({
  component: RouteComponent,
  loader: async () => {
    await Promise.all([
      queryClient.ensureQueryData(
        personalizationPageQuery("bonds-referral-page")
      ),
      queryClient.ensureQueryData(getQuizStatusQueryOptions()),
    ]);
  },
});

function RouteComponent() {
  const [campaign, setCampaign] = useState<KbcReferralCampaignMetadata | null>(
    null
  );

  useEffect(() => {
    getCampaign("KBC_REFERRAL_CAMPAIGN_TYPE").then(setCampaign);
  }, []);
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());

  const handleShare = () => {
    if (!campaign) return;

    const { shareText, campaignReferralLink } = campaign;
    const shareContent = `${shareText} ${campaignReferralLink}`;

    trackEvent("kbc_referral_cta_clicked", {
      screen_name: location.pathname,
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });

    // Check if native sharing is available (for mobile apps)
    if (native.share.isSupported()) {
      return native.share.shareText(shareContent);
    }

    // Fallback to Web Share API
    if (navigator.share) {
      navigator.share({
        text: shareContent,
      });
    } else {
      // Fallback to copying to clipboard
      navigator.clipboard?.writeText(shareContent);
    }
  };

  return (
    <>
      <CampaignAppBar
        left={<BackAppbarAction />}
        scrollStyleOptions={{
          offsetStyles: {
            backgroundColor: {
              threshold: 5,
              before: "transparent",
              after: "#141009",
            },
            borderBottomWidth: {
              threshold: 5,
              before: 0,
              after: "var(--border-w-sm)",
            },
          },
        }}
      ></CampaignAppBar>
      <div className="flex flex-col items-center floating-footer-padding gap-18 mt-5">
        <div className="flex flex-col gap-8 items-center">
          <div className="flex flex-col items-center gap-2">
            <img src={increase} alt="Increase" />
            <p className="text-heading4 text-white text-center">
              Invite your friends and family, if they win <br /> you will win
              too!
            </p>
          </div>
          <div className="px-5">
            <img src={inviteFlow} alt="Invite Flow" />
          </div>
        </div>
        <Animation
          src={referralPrizes}
          type="lottie"
          loop
          autoplay
          aspectRatio={360 / 219}
        />
      </div>
      <FloatingFooterContent>
        <button
          onClick={handleShare}
          className="rounded-xl text-center bg-linear-to-tr from-[#E0B96C] to-[#FFEAAF] py-3 w-full font-medium"
        >
          Share special invite
        </button>
      </FloatingFooterContent>
    </>
  );
}
