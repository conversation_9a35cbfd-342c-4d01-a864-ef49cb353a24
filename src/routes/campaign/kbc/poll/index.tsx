import BackAppbarAction from "@/components/functional/back-appbar-action";
import { createFileRoute, Link, useLocation } from "@tanstack/react-router";
import pollHeader from "@/assets/illustrations/poll/poll-header.webp";
import pollHeaderHindi from "@/assets/illustrations/hindi/poll-header.webp";
import answerPollButton from "@/assets/illustrations/poll/answer-poll-button.svg";
import answerPollButtonHindi from "@/assets/illustrations/hindi/answer-poll-button.svg";
import FaqSupport from "@/components/campaign/functional/faq";
import PollTimer from "@/components/campaign/functional/poll-timer";
import winnerTouchpoint from "@/assets/illustrations/poll/winner-touchpoint.webp";
import winnerTouchpointHindi from "@/assets/illustrations/hindi/winner-touchpoint.webp";
import {
  openAmazonVoucher,
  openHighInterestFDPage,
  openSMS,
} from "@/utils/routing";
import { useEffect } from "react";
import {
  getDailyPollQueryOptions,
  getQuizStatusQueryOptions,
} from "@/queries/kbc";
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import prizesTop from "@/assets/illustrations/prizes-top.webp";
import prizesBottom from "@/assets/illustrations/prizes-bottom-poll.webp";
import prizesTopHindi from "@/assets/illustrations/hindi/prizes-top.webp";
import prizesBottomHindi from "@/assets/illustrations/hindi/prizes-bottom-poll.webp";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import LanguageToggle from "@/components/campaign/functional/language-toggle";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import exploreMyself from "@/assets/illustrations/score/explore-button.svg";
import exploreMyselfHindi from "@/assets/illustrations/hindi/explore-button.svg";
import requestCallHindi from "@/assets/illustrations/hindi/request-call-back-score.svg";
import requestCall from "@/assets/illustrations/score/request-call-back-score.svg";
import voucher from "@/assets/illustrations/voucher.webp";
import voucherHindi from "@/assets/illustrations/hindi/voucher.webp";
import referralEntry from "@/assets/illustrations/referral-entry.webp";
import { supportLink } from "@/config/support";
import { toaster } from "@/components/ui/toast/store";
import { withInCallWindow } from "@/utils/time";
import LinkButton from "@/components/ui/button/link-button";
import { getUserContextQueryOptions } from "@/queries/context";
import pollToday from "@/assets/illustrations/poll/attempt/poll-today.webp";
import pollTodayHindi from "@/assets/illustrations/poll/attempt/poll-today-hi.webp";
import pollTomorrow from "@/assets/illustrations/poll/attempt/poll-tmmrw.webp";
import pollTomorrowHindi from "@/assets/illustrations/poll/attempt/poll-tmmrw-hi.webp";
import amithabhBirthdayEntry from "@/assets/illustrations/amitabh-birthday-entry.webp";
import clsx from "clsx";

type pollLandingSearch = {
  cameFromPollAttempt?: boolean;
};

export const Route = createFileRoute("/campaign/kbc/poll/")({
  component: RouteComponent,
  validateSearch: (
    searchParams: Record<string, unknown>
  ): pollLandingSearch => {
    return {
      cameFromPollAttempt: Boolean(searchParams.cameFromPollAttempt),
    };
  },
});

function RouteComponent() {
  const { data: status } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { data: pollQuestionsData } = useQuery(
    getDailyPollQueryOptions({ isEligible: status?.nextStep?.isPollEligible })
  );
  const { data: contextQuery } = useSuspenseQuery(getUserContextQueryOptions());
  const isRepeatUser = contextQuery?.props?.["invested"] === "true";
  const { cameFromPollAttempt } = Route.useSearch();
  const isEligible =
    status?.nextStep?.isPollEligible && pollQuestionsData?.questions;
  useEffect(() => {
    trackEvent("kbc_landing_page_viewed", {
      language: getLanguageFromEnum(language),
      poll_active: isEligible ? "Yes" : "No",
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  });
  const { language } = useLanguage();

  const istParts = new Intl.DateTimeFormat("en-US", {
    timeZone: "Asia/Kolkata",
    hour12: false,
    hour: "2-digit",
  }).formatToParts(new Date());
  const istHour = Number(istParts.find((p) => p.type === "hour")?.value ?? "0");
  const showToday = istHour >= 0 && istHour < 21;

  const handleRequestCallBack = () => {
    const withinCallWindow = withInCallWindow();
    trackEvent("kbc_expert_call clicked", {
      language: getLanguageFromEnum(language),
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      call_mode: withinCallWindow ? "dial" : "callback",
    });

    if (withinCallWindow) {
      window.location.href = supportLink(isRepeatUser);
      return;
    }

    toaster.create({
      description:
        language === LanguageCode.EN
          ? "We've got your request. Our team will reach out by 9:00 AM."
          : "हमारी टीम सुबह 9:00 बजे तक आपसे संपर्क करेगी।",
      type: "info",
      duration: 3000,
    });
  };

  const location = useLocation();
  return (
    <>
      <CampaignAppBar
        left={<BackAppbarAction opaque />}
        right={<LanguageToggle />}
      />
      <div className="bg-[#141009] body-behind-appbar floating-footer-padding">
        {cameFromPollAttempt ? (
          <img
            src={
              language === LanguageCode.EN
                ? showToday
                  ? pollToday
                  : pollTomorrow
                : showToday
                  ? pollTodayHindi
                  : pollTomorrowHindi
            }
            alt="Poll Header"
          />
        ) : (
          <img
            src={language === LanguageCode.EN ? pollHeader : pollHeaderHindi}
            alt="Poll Header"
          />
        )}
        <div className="flex flex-col items-center">
          <div className="flex flex-col gap-6 items-center">
            {(isEligible || (!isEligible && !cameFromPollAttempt)) && (
              <div className="flex flex-col items-center gap-4">
                {isEligible && (
                  <p className="px-7 text-white text-heading3 text-center mb-6">
                    {language === LanguageCode.EN
                      ? "Answer today’s audience poll to win additional gift hampers"
                      : "आज के ऑडियंस पोल का उत्तर दें और अतिरिक्त गिफ्ट हैम्पर्स जीतें"}
                  </p>
                )}
                {!isEligible &&
                  !cameFromPollAttempt &&
                  (language === LanguageCode.EN ? (
                    <div className="flex flex-col gap-1 items-center">
                      <p className="text-title-all-caps font-medium text-white">
                        What’s next?
                      </p>
                      <p className="px-8.5 text-white text-heading3 text-center">
                        Come back {showToday ? "today" : "tomorrow"} at 9:00 PM
                        to play audience poll and win gift hampers
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col gap-1 items-center text-white">
                      <p className="font-medium"> अगला कदम?</p>
                      <p className="px-8.5 text-white text-heading3 text-center">
                        {showToday ? "आज" : "कल"} रात 9:00 बजे वापस आकर देखें कि
                        आपने हैम्पर जीता है या नहीं।
                      </p>
                    </div>
                  ))}
              </div>
            )}
            {!isEligible && (
              <div
                className={clsx("flex flex-col items-center gap-13", {
                  "mt-3": !cameFromPollAttempt,
                })}
              >
                <div className="flex flex-col items-center gap-4 ">
                  <p className=" font-medium text-white">
                    {language === LanguageCode.EN ? (
                      <span className="text-title-all-caps uppercase">
                        Next Poll in
                      </span>
                    ) : (
                      <span> अगला पोल </span>
                    )}
                  </p>
                  <div className="flex gap-2 justify-center">
                    <div className="h-[1px] w-[54px] bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50 mt-8.5" />
                    <PollTimer />
                    <div className="h-[1px] w-[54px] bg-linear-to-r to-[#FFFFFF00] from-[#FFFFFF] opacity-50 mt-8.5" />
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="space-y-11">
            <div className="flex flex-col items-center gap-3">
              {isEligible ? (
                <>
                  <div className="flex flex-col items-center gap-3 px-5 w-full">
                    <Link
                      to="/campaign/kbc/poll/attempt"
                      onClick={() => {
                        trackEvent("kbc_start_poll_clicked", {
                          language: getLanguageFromEnum(language),
                          user_status: getKBCUserStatus(
                            quizStatus?.nextStep?.nextStep
                          ),
                          screen_name: location.pathname,
                        });
                      }}
                      replace
                      className="w-full"
                    >
                      <img
                        src={
                          language === LanguageCode.EN
                            ? answerPollButton
                            : answerPollButtonHindi
                        }
                        className="w-full"
                        alt="Answer Poll Button"
                      />
                    </Link>
                    <LinkButton
                      darkMode
                      href="/campaign/kbc/leaderboard"
                      onClick={() => {
                        trackEvent("kbc_check_winner_clicked", {
                          language: getLanguageFromEnum(language),
                          user_status: getKBCUserStatus(
                            quizStatus?.nextStep?.nextStep
                          ),
                          screen_name: location.pathname,
                        });
                      }}
                    >
                      {language === LanguageCode.EN
                        ? "Check the winners till now"
                        : "अब तक के विजेता"}
                    </LinkButton>
                  </div>
                  <Link
                    onClick={() => {
                      trackEvent("kbc_amitabh_birthday_entry_clicked", {
                        language: getLanguageFromEnum(language),
                        user_status: getKBCUserStatus(
                          quizStatus?.nextStep?.nextStep
                        ),
                        screen_name: location.pathname,
                      });
                    }}
                    to="/campaign/kbc/birthday"
                    className="px-5 mt-9"
                  >
                    <img
                      src={amithabhBirthdayEntry}
                      alt="Amitabh Birthday Entry"
                    />
                  </Link>
                  {isRepeatUser ? (
                    <Link
                      onClick={() => {
                        trackEvent("kbc_referral_entry_clicked", {
                          language: getLanguageFromEnum(language),
                          user_status: getKBCUserStatus(
                            quizStatus?.nextStep?.nextStep
                          ),
                          screen_name: location.pathname,
                        });
                      }}
                      className="px-5 mt-9"
                      to="/campaign/kbc/referral"
                    >
                      <img src={referralEntry} alt="Referral Entry" />
                    </Link>
                  ) : (
                    <button
                      onClick={() => {
                        trackEvent("kbc_voucher_clicked", {
                          language: getLanguageFromEnum(language),
                          user_status: getKBCUserStatus(
                            quizStatus?.nextStep?.nextStep
                          ),
                          screen_name: location.pathname,
                        });
                        openAmazonVoucher();
                      }}
                      className="px-5 mt-9"
                    >
                      <img
                        src={
                          language === LanguageCode.EN ? voucher : voucherHindi
                        }
                        alt="Voucher"
                      />
                    </button>
                  )}
                </>
              ) : (
                <div className="flex flex-col items-center gap-5 mt-5">
                  <Link
                    onClick={() => {
                      trackEvent("kbc_amitabh_birthday_entry_clicked", {
                        language: getLanguageFromEnum(language),
                        user_status: getKBCUserStatus(
                          quizStatus?.nextStep?.nextStep
                        ),
                        screen_name: location.pathname,
                      });
                    }}
                    to="/campaign/kbc/birthday"
                    className="px-5 mt-5"
                  >
                    <img
                      src={amithabhBirthdayEntry}
                      alt="Amitabh Birthday Entry"
                    />
                  </Link>
                  {isRepeatUser ? (
                    <Link
                      onClick={() => {
                        trackEvent("kbc_referral_entry_clicked", {
                          language: getLanguageFromEnum(language),
                          user_status: getKBCUserStatus(
                            quizStatus?.nextStep?.nextStep
                          ),
                          screen_name: location.pathname,
                        });
                      }}
                      className="px-5"
                      to="/campaign/kbc/referral"
                    >
                      <img src={referralEntry} alt="Referral Entry" />
                    </Link>
                  ) : (
                    <button
                      onClick={() => {
                        trackEvent("kbc_voucher_clicked", {
                          language: getLanguageFromEnum(language),
                          user_status: getKBCUserStatus(
                            quizStatus?.nextStep?.nextStep
                          ),
                          screen_name: location.pathname,
                        });
                        openAmazonVoucher();
                      }}
                      className="px-5"
                    >
                      <img
                        src={
                          language === LanguageCode.EN ? voucher : voucherHindi
                        }
                        alt="Voucher"
                      />
                    </button>
                  )}
                  <Link
                    onClick={() => {
                      trackEvent("kbc_check_winner_clicked", {
                        language: getLanguageFromEnum(language),
                        user_status: getKBCUserStatus(
                          quizStatus?.nextStep?.nextStep
                        ),
                        screen_name: location.pathname,
                      });
                    }}
                    to="/campaign/kbc/leaderboard"
                  >
                    <img
                      src={
                        language === LanguageCode.EN
                          ? winnerTouchpoint
                          : winnerTouchpointHindi
                      }
                      alt="Explore Winner page"
                      className="w-full "
                    />
                  </Link>
                </div>
              )}
            </div>
            <div>
              <img
                src={language === LanguageCode.EN ? prizesTop : prizesTopHindi}
                alt="Prizes top"
              />
              <img
                src={
                  language === LanguageCode.EN
                    ? prizesBottom
                    : prizesBottomHindi
                }
                alt="Prizes bottom"
              />
              <FaqSupport />
            </div>
          </div>
        </div>
      </div>
      {!isEligible && (
        <FloatingFooterContent>
          {isRepeatUser ? (
            <div className="flex flex-col items-center justify-center gap-1.5 text-center">
              <p className="text-body2 text-white-60">
                {language === LanguageCode.EN ? (
                  <>
                    Learn more about high-return FDs
                    <span className="text-white"> up to 8.40% p.a.</span>
                  </>
                ) : (
                  <>
                    हाई-रिटर्न एफडी के बारे में और जानें,
                    <span className="text-white"> जो 8.40% प्रति वर्ष </span>तक
                    रिटर्न देती हैं।
                  </>
                )}
              </p>

              <button
                className="rounded-xl text-center bg-linear-to-tr from-[#E0B96C] to-[#FFEAAF] text-heading4 w-full font-medium flex items-center justify-center gap-1 py-3"
                onClick={() => {
                  trackEvent("kbc_explore_high_return_fds_clicked", {
                    language: getLanguageFromEnum(language),
                    user_status: getKBCUserStatus(
                      quizStatus?.nextStep?.nextStep
                    ),
                    screen_name: location.pathname,
                  });
                  openHighInterestFDPage();
                }}
              >
                <span className="text-black-80">
                  {language === LanguageCode.EN
                    ? "Explore high-return FDs"
                    : "हाई रिटर्न FD एक्सप्लोर करें"}
                </span>
              </button>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center gap-4">
              <div className="text-white text-center space-y-1">
                <p className="text-body2 text-white-60">
                  {language === LanguageCode.EN ? (
                    <>
                      Learn more about high-return FDs
                      <span className="text-white"> up to 8.40% p.a.</span>
                    </>
                  ) : (
                    <>
                      हाई-रिटर्न एफडी के बारे में और जानें,
                      <span className="text-white"> जो 8.40% प्रति वर्ष </span>
                      तक रिटर्न देती हैं।
                    </>
                  )}
                </p>
              </div>
              <div className="flex items-center justify-center gap-2">
                <button
                  onClick={() => {
                    trackEvent("kbc_explore_sms_button_clicked", {
                      language: getLanguageFromEnum(language),
                      user_status: getKBCUserStatus(
                        quizStatus?.nextStep?.nextStep
                      ),
                      screen_name: location.pathname,
                    });
                    openSMS();
                  }}
                >
                  <img
                    src={
                      language === LanguageCode.EN
                        ? exploreMyself
                        : exploreMyselfHindi
                    }
                    alt="Explore Myself"
                    className="h-12"
                  />
                </button>
                <button onClick={handleRequestCallBack}>
                  <img
                    src={
                      language === LanguageCode.EN
                        ? requestCall
                        : requestCallHindi
                    }
                    alt="Request Call"
                    className="h-12"
                  />
                </button>
              </div>
            </div>
          )}
        </FloatingFooterContent>
      )}
    </>
  );
}
