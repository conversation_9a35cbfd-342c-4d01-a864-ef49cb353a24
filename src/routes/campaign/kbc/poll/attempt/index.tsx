import {
  createFileRoute,
  useLocation,
  useNavigate,
} from "@tanstack/react-router";
import FloatingGoldButton from "@/components/campaign/ui/floating-gold-button";
import { useEffect, useState } from "react";
import { PollToggleGroup } from "@/components/campaign/functional/poll-toggle-group/poll-toggle-group";
import { useMutation, useSuspenseQuery } from "@tanstack/react-query";
import {
  getDailyPollQueryOptions,
  getQuizStatusQueryOptions,
} from "@/queries/kbc";
import { submitPollQuestion } from "@/clients/gen/personalization_api";
import { getErrorMessage } from "@/utils/errors";
import { toaster } from "@/components/ui/toast/store";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import LanguageToggle from "@/components/campaign/functional/language-toggle";
import { useLanguage } from "@/contexts/language-context";
import { useBodyBackground } from "@/hooks/body-classes";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { trackEvent } from "@/utils/analytics";
import backgroundImage from "@/assets/illustrations/background-image.webp";

export const Route = createFileRoute("/campaign/kbc/poll/attempt/")({
  component: RouteComponent,
});

function RouteComponent() {
  const [isNavigating, setIsNavigating] = useState(false);
  const navigate = useNavigate();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { data: pollQuestionsData } = useSuspenseQuery(
    getDailyPollQueryOptions({
      isEligible: quizStatus?.nextStep?.isPollEligible,
    })
  );
  const [percentage, setPercentage] = useState(0);
  const { language } = useLanguage();
  const location = useLocation();
  useBodyBackground(backgroundImage);
  useEffect(() => {
    trackEvent("kbc_poll_question_viewed", {
      language: getLanguageFromEnum(language),
      question_number: 1,
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  });
  const submitPollMutation = useMutation({
    mutationFn: (data: {
      pollId: string;
      questionId: string;
      optionId: string;
    }) =>
      submitPollQuestion(data.pollId, data.questionId, {
        selectedOptions: [data.optionId],
      }),
    onSuccess: (_, data) => {
      trackEvent("kbc_poll_question_submitted", {
        language: getLanguageFromEnum(language),
        question_number: 1,
        option_selected: data.optionId,
        screen_name: location.pathname,
        user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      });
      setPercentage(
        pollQuestionsData.optionsPercentage?.[groupID!]?.[data.optionId] ?? 0
      );
      setIsNavigating(true);
    },
    onError: async (error) => {
      setIsNavigating(false);
      toaster.create({
        type: "error",
        description: await getErrorMessage(error),
      });
    },
  });

  if (
    !pollQuestionsData.pollId ||
    !pollQuestionsData.questions ||
    !pollQuestionsData.questions[0] ||
    !pollQuestionsData.questions[0].questionByLanguage ||
    !pollQuestionsData.questions[0].questionByLanguage[
      getLanguageFromEnum(language)
    ] ||
    !pollQuestionsData.questions[0].questionByLanguage[
      getLanguageFromEnum(language)
    ][0] ||
    !pollQuestionsData.questions[0].questionByLanguage[
      getLanguageFromEnum(language)
    ][0].questionId ||
    !pollQuestionsData.questions[0].questionByLanguage[
      getLanguageFromEnum(language)
    ][0].options
  ) {
    navigate({ to: "/campaign/kbc/poll", replace: true });
    return null;
  }

  const currentQuestion =
    pollQuestionsData.questions[0].questionByLanguage[
      getLanguageFromEnum(language)
    ][0];

  const groupID = pollQuestionsData.questions[0].groupId;

  const handleChange = (value: string[]) => {
    trackEvent("kbc_poll_option_tapped", {
      language: getLanguageFromEnum(language),
      question_number: 1,
      option_selected: value[0],
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    submitPollMutation.mutate({
      pollId: pollQuestionsData.pollId!,
      questionId: currentQuestion.questionId!,
      optionId: value[0],
    });
  };

  return (
    <>
      <CampaignAppBar right={<LanguageToggle />}>
        <img src={appBarLogo} alt="" width={92} />
      </CampaignAppBar>
      <div className="px-5">
        <div className="flex flex-col gap-6 pt-7">
          <div className="flex flex-col gap-3 text-white items-center">
            <div className="flex items-center justify-center gap-2">
              <p className="text-title-all-caps font-medium">
                AUDIENCE Poll of the day
              </p>
            </div>

            <div className="text-heading2 text-center space-y-1.5">
              <p>{currentQuestion.question}</p>
            </div>
          </div>
          {currentQuestion.options && (
            <PollToggleGroup
              key={currentQuestion.questionId}
              options={currentQuestion.options!.map((option) => ({
                label: option.text ?? "Unknown",
                value: option.optionId ?? "Unknown",
                percentage:
                  pollQuestionsData.optionsPercentage?.[groupID!]?.[
                    option.optionId!
                  ] ?? 0,
              }))}
              disabled={isNavigating}
              onChange={(details) => handleChange(details.value)}
            />
          )}
          {isNavigating && (
            <FloatingGoldButton
              onClick={() =>
                navigate({
                  to: "/campaign/kbc/poll/attempt-loader",
                  search: { percentage },
                  replace: true,
                })
              }
            >
              <p className="text-hading4 font-medium text-black-80"> Proceed</p>
            </FloatingGoldButton>
          )}
        </div>
      </div>
    </>
  );
}
