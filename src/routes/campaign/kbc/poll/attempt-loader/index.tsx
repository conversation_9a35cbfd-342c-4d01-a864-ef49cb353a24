import { createFileRoute, useNavigate } from "@tanstack/react-router";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import ProgressLoader from "@/components/campaign/functional/progress-loader";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { useEffect } from "react";
import goldTick from "@/assets/icons/gold-tick.svg";

type PollSearch = {
  percentage: number;
};

export const Route = createFileRoute("/campaign/kbc/poll/attempt-loader/")({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): PollSearch => {
    return {
      percentage: Number(searchParams.percentage),
    };
  },
});

function RouteComponent() {
  const { percentage } = Route.useSearch();
  const { language } = useLanguage();
  const navigate = useNavigate();
  useEffect(() => {
    const handleQuizNavigation = () => {
      navigate({
        to: "/campaign/kbc/poll",
        search: { cameFromPollAttempt: true },
        replace: true,
      });
    };
    const timer = setTimeout(handleQuizNavigation, 3000);
    return () => clearTimeout(timer);
  }, [navigate]);
  return (
    <>
      <header
        className={"border-b-black-10 pt-safe sticky top-0 z-30 border-solid"}
      >
        <div className="flex items-center justify-center gap-4 px-5 py-4">
          <img src={appBarLogo} alt="" width={92} />
        </div>
      </header>
      <ProgressLoader>
        <div className="text-heading2 text-white text-center flex flex-col items-center gap-4 px-5">
          <img src={goldTick} alt="Gold Tick" className="w-12 h-12" />
          {language === LanguageCode.EN ? (
            percentage === 0 ? (
              <p>
                Congrats! You are unique <br /> in your thinking
              </p>
            ) : (
              <p>
                {percentage?.toFixed(0)}% people in India <br /> think like you
              </p>
            )
          ) : percentage === 0 ? (
            <p>
              बधाई हो! आपकी सोच वाकई <br /> खास है
            </p>
          ) : (
            <p>
              भारत में {percentage?.toFixed(0)}% लोग <br /> आपके जैसा सोचते हैं
            </p>
          )}
        </div>
      </ProgressLoader>
    </>
  );
}
