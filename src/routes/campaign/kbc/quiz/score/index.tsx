import { createFileRoute } from "@tanstack/react-router";
import scoreZero from "@/assets/illustrations/score/score-zero.webp";
import scoreOne from "@/assets/illustrations/score/score-one.webp";
import scoreTwo from "@/assets/illustrations/score/score-two.webp";
import scoreThree from "@/assets/illustrations/score/score-three.webp";
import scoreFour from "@/assets/illustrations/score/score-four.webp";
import scoreFive from "@/assets/illustrations/score/score-five.webp";
import scoreSix from "@/assets/illustrations/score/score-six.webp";
import scoreSeven from "@/assets/illustrations/score/score-seven.webp";
import leftParticle from "@/assets/illustrations/score/left-particle.webp";
import rightParticle from "@/assets/illustrations/score/right-particle.webp";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { useBodyBackground } from "@/hooks/body-classes";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import { useEffect } from "react";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { openAmazonVoucher, openSMS, openSuryoday } from "@/utils/routing";
import { toaster } from "@/components/ui/toast/store";
import Toaster from "@/components/ui/toast/toaster";
import { supportLink } from "@/config/support";
import { withInCallWindow } from "@/utils/time";
import { getUserContextQueryOptions } from "@/queries/context";
import ScorePollTimer from "@/components/campaign/functional/score-poll-timer";
import voucher from "@/assets/illustrations/voucher.webp";
import voucherHindi from "@/assets/illustrations/hindi/voucher.webp";
import bankRedirect from "@/assets/illustrations/bank-redirect.webp";
import bankRedirectHindi from "@/assets/illustrations/hindi/bank-redirect.webp";
import backgroundImage from "@/assets/illustrations/background-image.webp";
import exploreMyself from "@/assets/illustrations/score/explore-button.svg";
import exploreMyselfHindi from "@/assets/illustrations/hindi/explore-button.svg";
import requestCallHindi from "@/assets/illustrations/hindi/request-call-back-score.svg";
import requestCall from "@/assets/illustrations/score/request-call-back-score.svg";

type QuizSearch = {
  score: number;
};

export const Route = createFileRoute("/campaign/kbc/quiz/score/")({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): QuizSearch => {
    return {
      score: Number(searchParams.score),
    };
  },
});

function RouteComponent() {
  const { score } = Route.useSearch();
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { data: contextQuery } = useSuspenseQuery(getUserContextQueryOptions());
  const isRepeatUser = contextQuery?.props?.["invested"] === "true";
  useBodyBackground(backgroundImage);
  const scoreImages = [
    scoreZero,
    scoreOne,
    scoreTwo,
    scoreThree,
    scoreFour,
    scoreFive,
    scoreSix,
    scoreSeven,
  ];

  useEffect(() => {
    trackEvent("kbc_score_screen_viewed", {
      language: getLanguageFromEnum(language),
      score: score,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  }, []);

  const handleRequestCallBack = () => {
    const withinCallWindow = withInCallWindow();
    trackEvent("kbc_expert_call clicked", {
      language: getLanguageFromEnum(language),
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      call_mode: withinCallWindow ? "dial" : "callback",
    });

    if (withinCallWindow) {
      // During 8:00 AM - 11:59 PM IST, trigger phone dialer
      window.location.href = supportLink(isRepeatUser);
      return;
    }

    // Outside call window (12:00 AM - 7:59 AM IST), keep existing callback flow
    toaster.create({
      description:
        language === LanguageCode.EN
          ? "We've got your request. Our team will reach out by 9:00 AM."
          : "हमारी टीम सुबह 9:00 बजे तक आपसे संपर्क करेगी।",
      type: "info",
      duration: 3000,
    });
    setTimeout(() => {
      openSMS();
    }, 4000);
  };

  return (
    <>
      <CampaignAppBar
        left={<BackAppbarAction />}
        scrollStyleOptions={{
          offsetStyles: {
            backgroundColor: {
              threshold: 5,
              before: "transparent",
              after: "#292114",
            },
            borderBottomWidth: {
              threshold: 5,
              before: 0,
              after: "var(--border-w-sm)",
            },
          },
        }}
      >
        <img src={appBarLogo} alt="" width={92} />
      </CampaignAppBar>
      <div className="text-white flex flex-col gap-8 floating-footer-padding mb-safe-offset-4">
        <div className="flex flex-col items-center relative text-white mt-7">
          <div className="flex flex-col items-center gap-1">
            <div className="flex flex-col items-center gap-3">
              <img src={scoreImages[score]} alt="" className="w-49" />
              <div className="text-center text-white text-heading2">
                {score > 2 ? (
                  <>
                    <p>
                      {language === LanguageCode.EN
                        ? "Congrats, you’re now eligible"
                        : "बधाई हो, अब आप KBC"}
                    </p>
                    <p>
                      {language === LanguageCode.EN
                        ? "for the KBC contest prizes"
                        : "कॉन्टेस्ट के इनाम के लिए पात्र हैं।"}
                    </p>
                  </>
                ) : (
                  <>
                    <p>
                      {language === LanguageCode.EN
                        ? "Good news! you’re still eligible"
                        : "बधाई हो, अब भी आप KBC"}
                    </p>
                    <p>
                      {language === LanguageCode.EN
                        ? "for the KBC contest prizes"
                        : "कॉन्टेस्ट के इनामों के लिए पात्र हैं।"}
                    </p>
                  </>
                )}
              </div>
            </div>
            <p className="text-body1 text-white-60">
              {language === LanguageCode.EN
                ? "Winners announced weekly at 9 PM"
                : "विजेताओं की घोषणा हर हफ़्ते रात 9 बजे की जाती है"}
            </p>
          </div>
          <img
            src={leftParticle}
            alt="particles"
            className="absolute -top-4 left-0 w-22"
          />
          <img
            src={rightParticle}
            alt="particles"
            className="absolute -top-4 right-0  w-22"
          />
        </div>
        <div className="flex flex-col gap-4 items-center">
          <div className="flex items-center justify-center gap-2">
            <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50" />
            {language === LanguageCode.EN ? (
              <p className="text-title-all-caps font-medium uppercase">
                What’s next
              </p>
            ) : (
              <p>अगला कदम</p>
            )}
            <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF] to-[#FFFFFF00] opacity-50" />
          </div>
          <div className=" px-5 w-full space-y-3">
            <div className="rounded-xl border border-white bg-linear-to-br from-white-20 to-white-0 backdrop-blur-xs  text-white p-4 pr-3">
              <div className="flex gap-3 justify-between w-full items-center">
                <div className="flex flex-col gap-0.5 text-body1 text-left">
                  {language === LanguageCode.EN ? (
                    <p className="text-left"> Daily audience poll</p>
                  ) : (
                    <p className="text-left"> डेली ऑडियंस पोल</p>
                  )}
                  {language === LanguageCode.EN ? (
                    <p className="text-[11px] text-white-60">
                      Win hampers • Daily at 9 PM
                    </p>
                  ) : (
                    <p className="text-[11px] text-white-60">
                      गिफ्ट हैम्पर्स जीतें . रोज़ रात 9 बजे
                    </p>
                  )}
                </div>
                <ScorePollTimer />
              </div>
            </div>
            {isRepeatUser ? (
              <button
                onClick={() => {
                  trackEvent("kbc_suryoday_redirect", {
                    language: getLanguageFromEnum(language),
                    user_status: getKBCUserStatus(
                      quizStatus?.nextStep?.nextStep
                    ),
                    screen_name: location.pathname,
                  });
                  openSuryoday();
                }}
              >
                <img
                  src={
                    language === LanguageCode.EN
                      ? bankRedirect
                      : bankRedirectHindi
                  }
                  alt="Suryoday"
                />
              </button>
            ) : (
              <button
                onClick={() => {
                  trackEvent("kbc_voucher_clicked", {
                    language: getLanguageFromEnum(language),
                    user_status: getKBCUserStatus(
                      quizStatus?.nextStep?.nextStep
                    ),
                    screen_name: location.pathname,
                  });
                  openAmazonVoucher();
                }}
              >
                <img
                  src={language === LanguageCode.EN ? voucher : voucherHindi}
                  alt="Voucher"
                />
              </button>
            )}
          </div>
        </div>
      </div>
      <Toaster />
      <FloatingFooterContent>
        {isRepeatUser ? (
          <div className="flex flex-col items-center justify-center gap-1.5 text-center">
            <p className="text-body2 text-white-60">
              {language === LanguageCode.EN ? (
                <>
                  Learn more about high-return FDs
                  <span className="text-white"> up to 8.40% p.a.</span>
                </>
              ) : (
                <>
                  हाई-रिटर्न एफडी के बारे में और जानें,
                  <span className="text-white"> जो 8.40% प्रति वर्ष </span>तक
                  रिटर्न देती हैं।
                </>
              )}
            </p>

            <button
              className="rounded-xl text-center bg-linear-to-tr from-[#E0B96C] to-[#FFEAAF] text-heading4 w-full font-medium flex items-center justify-center gap-1 py-3"
              onClick={handleRequestCallBack}
            >
              <svg
                width="17"
                height="17"
                viewBox="0 0 17 17"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.11379 1.62702C9.11379 1.46099 9.17974 1.30176 9.29714 1.18436C9.41455 1.06696 9.57378 1.001 9.73981 1.001C11.3996 1.00282 12.9908 1.66296 14.1644 2.83658C15.338 4.0102 15.9982 5.60145 16 7.26119C16 7.42722 15.934 7.58645 15.8166 7.70386C15.6992 7.82126 15.54 7.88721 15.374 7.88721C15.2079 7.88721 15.0487 7.82126 14.9313 7.70386C14.8139 7.58645 14.748 7.42722 14.748 7.26119C14.7465 5.93341 14.2183 4.66043 13.2795 3.72154C12.3406 2.78265 11.0676 2.25453 9.73981 2.25304C9.57378 2.25304 9.41455 2.18708 9.29714 2.06968C9.17974 1.95228 9.11379 1.79305 9.11379 1.62702ZM9.73981 4.75712C10.4039 4.75712 11.0409 5.02094 11.5105 5.49054C11.9801 5.96015 12.2439 6.59707 12.2439 7.26119C12.2439 7.42722 12.3098 7.58645 12.4272 7.70386C12.5446 7.82126 12.7039 7.88721 12.8699 7.88721C13.0359 7.88721 13.1952 7.82126 13.3126 7.70386C13.43 7.58645 13.4959 7.42722 13.4959 7.26119C13.4949 6.26531 13.0989 5.31051 12.3947 4.60632C11.6905 3.90212 10.7357 3.50607 9.73981 3.50508C9.57378 3.50508 9.41455 3.57103 9.29714 3.68843C9.17974 3.80584 9.11379 3.96507 9.11379 4.1311C9.11379 4.29713 9.17974 4.45636 9.29714 4.57376C9.41455 4.69116 9.57378 4.75712 9.73981 4.75712ZM15.4322 11.4799C15.795 11.8437 15.9987 12.3365 15.9987 12.8503C15.9987 13.3641 15.795 13.8569 15.4322 14.2207L14.8625 14.8773C9.73542 19.786 -2.74114 7.31253 2.09173 2.16915L2.81165 1.54313C3.17585 1.19048 3.66425 0.995396 4.17118 1.00008C4.67811 1.00477 5.16282 1.20885 5.52044 1.56817C5.53984 1.58758 6.69986 3.09441 6.69986 3.09441C7.04406 3.45601 7.23567 3.93636 7.23485 4.43559C7.23404 4.93483 7.04086 5.41454 6.69548 5.77502L5.97055 6.68651C6.37173 7.66129 6.96157 8.54719 7.70619 9.29331C8.45081 10.0394 9.33551 10.6311 10.3095 11.0342L11.2266 10.3049C11.5871 9.95978 12.0668 9.76683 12.5658 9.76613C13.0649 9.76543 13.5451 9.95704 13.9066 10.3011C13.9066 10.3011 15.4128 11.4605 15.4322 11.4799ZM14.5708 12.3902C14.5708 12.3902 13.0727 11.2377 13.0533 11.2183C12.9244 11.0904 12.7501 11.0186 12.5685 11.0186C12.3869 11.0186 12.2126 11.0904 12.0836 11.2183C12.0667 11.2358 10.804 12.2418 10.804 12.2418C10.719 12.3095 10.6177 12.3539 10.5102 12.3706C10.4027 12.3873 10.2928 12.3757 10.1912 12.337C8.9294 11.8672 7.78333 11.1317 6.83059 10.1804C5.87785 9.22904 5.1407 8.08405 4.66905 6.82298C4.62723 6.71997 4.6136 6.60768 4.62955 6.49766C4.6455 6.38764 4.69047 6.28385 4.75982 6.19696C4.75982 6.19696 5.76584 4.93365 5.78274 4.91738C5.91061 4.78841 5.98236 4.61414 5.98236 4.43252C5.98236 4.25091 5.91061 4.07664 5.78274 3.94767C5.76333 3.92889 4.61083 2.42958 4.61083 2.42958C4.47993 2.3122 4.30909 2.24934 4.13333 2.25388C3.95758 2.25842 3.79021 2.33002 3.66554 2.45399L2.94562 3.08001C-0.586381 7.32693 10.2256 17.5392 13.9473 14.0222L14.5176 13.3649C14.6512 13.2411 14.7315 13.0702 14.7414 12.8883C14.7514 12.7064 14.6902 12.5278 14.5708 12.3902Z"
                  fill="black"
                  fill-opacity="0.8"
                />
                <path
                  d="M4.17188 0.953125C4.69103 0.957993 5.18746 1.16716 5.55371 1.53516L5.57324 1.55957C5.58286 1.57174 5.59657 1.58893 5.61328 1.61035C5.64691 1.65348 5.69425 1.71412 5.75 1.78613C5.86144 1.93007 6.00817 2.12012 6.1543 2.30957C6.30052 2.49915 6.44661 2.68851 6.55566 2.83008C6.61015 2.90081 6.65576 2.95976 6.6875 3.00098C6.70323 3.0214 6.71516 3.03782 6.72363 3.04883C6.72783 3.05428 6.7312 3.05867 6.7334 3.06152C6.73445 3.06289 6.73577 3.06373 6.73633 3.06445V3.06543C7.08702 3.43539 7.28205 3.92568 7.28125 4.43555C7.28042 4.94545 7.08336 5.43487 6.73145 5.80371L6.02441 6.69336C6.42297 7.65254 7.00545 8.52441 7.73926 9.25977C8.4729 9.99489 9.34371 10.579 10.3018 10.9795L11.1973 10.2686C11.5661 9.91689 12.0557 9.72053 12.5654 9.71973C13.0747 9.71901 13.5648 9.91367 13.9346 10.2637L13.9365 10.2646C13.9372 10.2652 13.9381 10.2665 13.9395 10.2676C13.9423 10.2698 13.9467 10.2731 13.9521 10.2773C13.9632 10.2858 13.9796 10.2977 14 10.3135C14.0412 10.3452 14.1002 10.3908 14.1709 10.4453C14.3123 10.5542 14.5011 10.6997 14.6904 10.8457C14.8799 10.9918 15.0699 11.1395 15.2139 11.251C15.2858 11.3066 15.3466 11.3531 15.3896 11.3867C15.4112 11.4035 15.4292 11.4171 15.4414 11.4268C15.4474 11.4315 15.4525 11.4356 15.4561 11.4385C15.4585 11.4404 15.4631 11.4446 15.4658 11.4473C15.8371 11.8198 16.0459 12.3246 16.0459 12.8506C16.0458 13.3766 15.8372 13.8814 15.4658 14.2539L15.4648 14.2529L14.8975 14.9082L14.8955 14.9111H14.8945C13.5962 16.154 11.8354 16.2929 10.0107 15.7305C8.18639 15.1682 6.29066 13.9037 4.70898 12.3262C3.12733 10.7486 1.85529 8.85435 1.28223 7.0293C0.709103 5.2039 0.833662 3.43931 2.05762 2.13672L2.06055 2.13379L2.78125 1.50781C3.15409 1.14779 3.65353 0.948333 4.17188 0.953125ZM4.13477 2.30078C3.97097 2.30501 3.81442 2.37177 3.69824 2.4873L3.69629 2.48926L2.97949 3.11133C2.11358 4.1542 2.12346 5.56833 2.69531 7.05957C3.2673 8.55088 4.39895 10.1099 5.75879 11.4287C7.11884 12.7477 8.70396 13.8233 10.1777 14.3496C11.6527 14.8762 13.0038 14.8493 13.915 13.9883L14.4824 13.334L14.4854 13.3301C14.6101 13.2146 14.6851 13.0555 14.6943 12.8857C14.7036 12.7164 14.647 12.5502 14.5361 12.4219C14.5335 12.4198 14.5296 12.4173 14.5254 12.4141C14.5144 12.4056 14.4979 12.3936 14.4775 12.3779C14.4365 12.3464 14.3779 12.3012 14.3076 12.2471C14.1669 12.1388 13.9794 11.9938 13.791 11.8486C13.6025 11.7034 13.4127 11.5571 13.2695 11.4463C13.1984 11.3912 13.1385 11.3449 13.0957 11.3115C13.0744 11.295 13.0571 11.2811 13.0449 11.2715C13.039 11.2668 13.0339 11.2627 13.0303 11.2598C13.0281 11.258 13.0234 11.2547 13.0205 11.252C12.9003 11.1328 12.7376 11.0654 12.5684 11.0654C12.3991 11.0655 12.2364 11.1328 12.1162 11.252C12.1136 11.2545 12.11 11.2574 12.1084 11.2588C12.1053 11.2614 12.1007 11.2644 12.0957 11.2686C12.0855 11.2769 12.0716 11.2893 12.0537 11.3037C12.0176 11.3329 11.9665 11.3736 11.9062 11.4219C11.7855 11.5187 11.6257 11.6466 11.4668 11.7734C11.308 11.9002 11.1499 12.0265 11.0312 12.1211C10.9719 12.1684 10.9223 12.2078 10.8877 12.2354C10.8704 12.2491 10.8569 12.2602 10.8477 12.2676C10.8431 12.2712 10.8393 12.2735 10.8369 12.2754C10.8358 12.2763 10.8346 12.2769 10.834 12.2773L10.833 12.2783C10.7416 12.3511 10.633 12.399 10.5176 12.417C10.4309 12.4305 10.3426 12.4265 10.2578 12.4062L10.1748 12.3809C8.9069 11.9088 7.75527 11.1698 6.79785 10.2139C5.84044 9.25787 5.099 8.10707 4.625 6.83984V6.83887C4.58063 6.72871 4.56601 6.60877 4.58301 6.49121C4.60016 6.37295 4.64909 6.26136 4.72363 6.16797V6.16699C4.72409 6.16642 4.7247 6.16517 4.72559 6.16406C4.72749 6.16167 4.73069 6.15795 4.73438 6.15332C4.74175 6.14407 4.75288 6.1305 4.7666 6.11328C4.79414 6.07871 4.83365 6.02897 4.88086 5.96973C4.97541 5.85107 5.10182 5.69297 5.22852 5.53418C5.35532 5.37525 5.48239 5.21548 5.5791 5.09473C5.62744 5.03437 5.66809 4.98342 5.69727 4.94727C5.71181 4.92924 5.72404 4.91454 5.73242 4.9043C5.73638 4.89946 5.73971 4.89555 5.74219 4.89258C5.74366 4.89082 5.74721 4.88652 5.75 4.88379C5.8688 4.76368 5.93552 4.60157 5.93555 4.43262C5.93555 4.26336 5.86819 4.10066 5.74902 3.98047V3.97949C5.74632 3.97664 5.74276 3.9736 5.74121 3.97168C5.73831 3.96811 5.73422 3.963 5.72949 3.95703C5.71989 3.94489 5.70614 3.92765 5.68945 3.90625C5.65607 3.86344 5.60999 3.80293 5.55469 3.73145C5.44388 3.5882 5.29762 3.39856 5.15234 3.20996C5.0071 3.02141 4.86223 2.8332 4.75391 2.69238C4.69983 2.62208 4.65456 2.56344 4.62305 2.52246C4.60734 2.50203 4.59534 2.48556 4.58691 2.47461C4.58384 2.47061 4.5811 2.46744 4.5791 2.46484L4.48145 2.39258C4.37757 2.33016 4.25757 2.29765 4.13477 2.30078ZM9.74023 3.45801C10.7484 3.45912 11.7149 3.86037 12.4277 4.57324C13.1406 5.28612 13.5419 6.25259 13.543 7.26074C13.543 7.4392 13.4719 7.61111 13.3457 7.7373C13.2196 7.86335 13.0484 7.93451 12.8701 7.93457C12.6918 7.93457 12.5207 7.86327 12.3945 7.7373C12.2683 7.61111 12.1973 7.4392 12.1973 7.26074C12.1971 6.60921 11.9382 5.98415 11.4775 5.52344C11.0168 5.06273 10.3918 4.80382 9.74023 4.80371C9.56177 4.80371 9.38986 4.73264 9.26367 4.60645C9.13773 4.48029 9.06738 4.30914 9.06738 4.13086C9.06745 3.95256 9.13763 3.78139 9.26367 3.65527C9.38986 3.52908 9.56177 3.45801 9.74023 3.45801ZM9.74023 0.954102C11.4123 0.956037 13.015 1.62141 14.1973 2.80371C15.3796 3.98601 16.0449 5.58873 16.0469 7.26074L16.0342 7.39355C16.0083 7.52275 15.9442 7.64275 15.8496 7.7373C15.7234 7.86334 15.5524 7.93456 15.374 7.93457C15.1957 7.93457 15.0246 7.86332 14.8984 7.7373C14.7724 7.61124 14.7013 7.43999 14.7012 7.26172L14.6943 7.01562C14.6323 5.78973 14.1181 4.62693 13.2461 3.75488C12.3161 2.82489 11.0554 2.30141 9.74023 2.2998C9.56177 2.2998 9.38986 2.22873 9.26367 2.10254C9.13767 1.97638 9.06738 1.80527 9.06738 1.62695C9.0674 1.44863 9.13765 1.27752 9.26367 1.15137C9.38986 1.02517 9.56177 0.954102 9.74023 0.954102Z"
                  stroke="black"
                  stroke-opacity="0.8"
                  stroke-width="0.09375"
                />
              </svg>

              <span className="text-black-80">
                {language === LanguageCode.EN
                  ? "Talk to FD expert"
                  : "एफडी एक्सपर्ट से बात करें"}
              </span>
            </button>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center gap-4">
            <div className="text-white text-center space-y-1">
              <p className="text-body2 text-white-60">
                {language === LanguageCode.EN ? (
                  <>
                    Learn more about high-return FDs
                    <span className="text-white"> up to 8.40% p.a.</span>
                  </>
                ) : (
                  <>
                    हाई-रिटर्न एफडी के बारे में और जानें,
                    <span className="text-white"> जो 8.40% प्रति वर्ष </span>तक
                    रिटर्न देती हैं।
                  </>
                )}
              </p>
            </div>
            <div className="flex items-center justify-center gap-2">
              <button
                onClick={() => {
                  trackEvent("kbc_explore_sms_button_clicked", {
                    language: getLanguageFromEnum(language),
                    user_status: getKBCUserStatus(
                      quizStatus?.nextStep?.nextStep
                    ),
                    screen_name: location.pathname,
                  });
                  openSMS();
                }}
              >
                <img
                  src={
                    language === LanguageCode.EN
                      ? exploreMyself
                      : exploreMyselfHindi
                  }
                  alt="Explore Myself"
                  className="h-12"
                />
              </button>
              <button onClick={handleRequestCallBack}>
                <img
                  src={
                    language === LanguageCode.EN
                      ? requestCall
                      : requestCallHindi
                  }
                  alt="Request Call"
                  className="h-12"
                />
              </button>
            </div>
          </div>
        )}
      </FloatingFooterContent>
    </>
  );
}
