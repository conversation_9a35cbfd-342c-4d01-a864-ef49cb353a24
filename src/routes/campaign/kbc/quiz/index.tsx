import { QuizToggleGroup } from "@/components/campaign/functional/quiz-toggle-group/quiz-toggle-group";
import { useLanguage } from "@/contexts/language-context";
import { Block, createFileRoute, useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { decryptAns } from "@/utils/decrypt";
import NextQuestionTimer from "@/components/campaign/functional/next-question-timer";
import { QuizBlocker } from "@/components/campaign/functional/quiz-blocker";
import { useMutation, useSuspenseQuery } from "@tanstack/react-query";
import {
  getQuizQuestionsQueryOptions,
  getQuizStatusQueryOptions,
  submitQuiz,
} from "@/queries/kbc";
import { toaster } from "@/components/ui/toast/store";
import { getErrorMessage } from "@/utils/errors";
import { create } from "@bufbuild/protobuf";
import {
  LanguageCode,
  Step,
  UserQuizAnswerSchema,
} from "@/clients/gen/business/campaign_pb";
import LanguageToggle from "@/components/campaign/functional/language-toggle";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import { useBodyBackground } from "@/hooks/body-classes";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import { closeWebview } from "@/utils/routing";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { trackEvent } from "@/utils/analytics";
import { queryClient } from "@/queries/client";
import backgroundImage from "@/assets/illustrations/background-image.webp";

type QuizSearch = {
  question?: number;
  answers?: string[];
};

export const Route = createFileRoute("/campaign/kbc/quiz/")({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): QuizSearch => {
    return {
      question: Number(searchParams?.question ?? 0),
      answers: searchParams.answers as string[],
    };
  },
});

function RouteComponent() {
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  if (quizStatus.nextStep?.nextStep === Step.POLL) {
    closeWebview();
  }
  const { data: quizQuestionsOptions } = useSuspenseQuery(
    getQuizQuestionsQueryOptions()
  );
  const { language } = useLanguage();
  const { question = 0, answers = [] } = Route.useSearch();
  const navigate = useNavigate();
  const [isNavigating, setIsNavigating] = useState(false);
  const [selectedNewAnswers, setSelectedNewAnswers] = useState<string[]>([]);
  const [selectedOptionId, setSelectedOptionId] = useState<string>("");
  useBodyBackground(backgroundImage);
  useEffect(() => {
    trackEvent("kbc_fdquiz_question_viewed", {
      question_number: question + 1,
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  });
  const quizSubmitMutation = useMutation({
    mutationFn: submitQuiz,
    onSuccess: (data) => {
      queryClient.invalidateQueries(getQuizStatusQueryOptions()).then(() => {
        trackEvent("kbc_fdquiz_completed", {
          language: getLanguageFromEnum(language),
          user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
        });
      });
      navigate({
        to: "/campaign/kbc/quiz/score-loader",
        search: {
          score: data.score,
        },
        replace: true,
      });
    },
    onError: async (error) => {
      setIsNavigating(false);
      toaster.create({
        type: "error",
        description: await getErrorMessage(error),
      });
    },
  });
  const currentQuestionGroup = quizQuestionsOptions.questionGroups[question];
  const currentQuestion = currentQuestionGroup.question[language];

  const handleChange = (value: string[]) => {
    setIsNavigating(true);
    const selectedOption = currentQuestion.options.find(
      (opt) => opt.id === value[0]
    );
    const newAnswer = `${currentQuestion.id}:${value[0]}:${selectedOption?.text || ""}`;
    trackEvent("kbc_fdquiz_option_tapped", {
      question_number: question + 1,
      option_selected: selectedOption?.text,
      language_name: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    const newAnswers = [...answers, newAnswer];
    setSelectedNewAnswers(newAnswers);
    setSelectedOptionId(value[0]);
  };

  const handleComplete = () => {
    if (question === quizQuestionsOptions.questionGroups.length - 1) {
      const userQuizAnswers = selectedNewAnswers.map((answer) => {
        const [questionID, selectedOptionId, selectedOptionText] =
          answer.split(":");
        return create(UserQuizAnswerSchema, {
          questionId: questionID,
          selectedOptionId,
          selectedOptionText: selectedOptionText || "",
        });
      });
      quizSubmitMutation.mutate(userQuizAnswers);
      return;
    }
    navigate({
      to: "/campaign/kbc/quiz",
      search: { question: question + 1, answers: selectedNewAnswers },
      replace: question !== 0,
    });
    setIsNavigating(false);
  };

  if (!currentQuestion) {
    return <div>Question not found</div>;
  }
  const decryptedCorrectOption = decryptAns(currentQuestion.correctOptionId);
  const options = currentQuestion.options.map((option) => ({
    label: option.text,
    value: option.id,
    verdict: option.id === decryptedCorrectOption,
  }));

  return (
    <Block
      shouldBlockFn={() => !isNavigating}
      enableBeforeUnload={!isNavigating}
      withResolver
    >
      {({ status, reset }) => (
        <>
          <CampaignAppBar right={<LanguageToggle />}>
            <img src={appBarLogo} alt="" width={92} />
          </CampaignAppBar>
          <div className="px-5">
            <div className="flex flex-col gap-6 pt-7">
              <div className="flex flex-col gap-3 text-white items-center">
                <div className="flex items-center justify-center gap-2">
                  <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50" />
                  {language === LanguageCode.EN ? (
                    <p className="text-title-all-caps font-medium">
                      FD Contest {question + 1}/
                      {quizQuestionsOptions.questionGroups.length}
                    </p>
                  ) : (
                    <p className="space-x-0.5">
                      <span className="text-title-all-caps font-medium">
                        FD
                      </span>
                      <span>
                        कॉन्टेस्ट {question + 1}/
                        {quizQuestionsOptions.questionGroups.length}
                      </span>
                    </p>
                  )}
                  <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF] to-[#FFFFFF00] opacity-50" />
                </div>
                <div className="text-heading2 text-center space-y-1.5">
                  <p>{currentQuestion.text}</p>
                </div>
              </div>

              <QuizToggleGroup
                key={currentQuestion.id}
                options={options}
                multiple={false}
                deselectable={false}
                disabled={isNavigating}
                showResults={isNavigating}
                onChange={(details) => handleChange(details.value)}
              />
              {isNavigating && (
                <div className="fixed bottom-5 left-5 right-5">
                  <NextQuestionTimer
                    onComplete={handleComplete}
                    startCountdown={isNavigating}
                    isCorrect={selectedOptionId === decryptedCorrectOption}
                    isNotLastQuestion={
                      question !==
                      quizQuestionsOptions.questionGroups.length - 1
                    }
                  >
                    {selectedOptionId === decryptedCorrectOption
                      ? currentQuestion.correctOptionExplanation
                      : currentQuestion.incorrectOptionExplanation}
                  </NextQuestionTimer>
                </div>
              )}
            </div>
          </div>
          {status === "blocked" && (
            <QuizBlocker reset={reset} question={question + 1} />
          )}
        </>
      )}
    </Block>
  );
}
