import { createFileRoute, useNavigate } from "@tanstack/react-router";
import ProgressLoader from "@/components/campaign/functional/progress-loader";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { useEffect, useRef, useState } from "react";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import scoreLoader from "@/assets/illustrations/score/score-loader.mp4";
type QuizSearch = {
  score: number;
};

export const Route = createFileRoute("/campaign/kbc/quiz/score-loader/")({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): QuizSearch => {
    return {
      score: Number(searchParams.score),
    };
  },
});

function RouteComponent() {
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { score } = Route.useSearch();
  const navigate = useNavigate();
  const videoRef = useRef<HTMLVideoElement>(null);
  const [loaderDuration, setLoaderDuration] = useState<number>(16000);
  const [isPlaying, setIsPlaying] = useState(false);

  useEffect(() => {
    trackEvent("kbc_video_screen_viewed", {
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    videoRef.current?.play();
  }, []);

  return (
    <ProgressLoader duration={loaderDuration}>
      <div className="flex flex-col gap-10 px-5 items-center text-white absolute top-10.5">
        <div className="px-5 text-center text-heading2">
          {language === LanguageCode.EN
            ? "Calculating your score..."
            : "आपका स्कोर कैलकुलेट हो रहा है..."}
        </div>

        <div className="max-w-[300px] flex-shrink-0 bg-white-20 p-2 rounded-xl">
          <div className="rounded-lg overflow-hidden relative">
            <video
              controlsList="nodownload nofullscreen noplaybackrate noremoteplayback noloop"
              autoPlay
              playsInline
              loop={false}
              ref={videoRef}
              preload="auto"
              onLoadedMetadata={() => {
                const dur = videoRef.current?.duration;
                if (dur && !Number.isNaN(dur)) {
                  setLoaderDuration(dur * 1000);
                }
              }}
              onPlay={() => {
                setIsPlaying(true);
                trackEvent("kbc_video_viewed", {
                  language: getLanguageFromEnum(language),
                  user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
                });
              }}
              onEnded={() => {
                navigate({
                  to: "/campaign/kbc/quiz/score",
                  search: { score },
                  replace: true,
                });
                setIsPlaying(false);
              }}
              className="relative z-10"
            >
              <source src={scoreLoader} type="video/mp4" />
            </video>
            {!isPlaying && (
              <div className="absolute inset-0 z-20 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-pulse pointer-events-none h-[460px]" />
            )}
          </div>
        </div>
      </div>
    </ProgressLoader>
  );
}
