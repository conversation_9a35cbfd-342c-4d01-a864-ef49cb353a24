import { createFileRoute, useLocation } from "@tanstack/react-router";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import appBarLogo from "@/assets/icons/appbar-logo.webp";
import SupportAppbarAction from "@/components/functional/support-appbar-action";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import { getKbcAllWinnerDates } from "@/utils/time";
import { useEffect } from "react";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useLanguage } from "@/contexts/language-context";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";

export const Route = createFileRoute("/campaign/kbc/leaderboard/hamper/")({
  component: RouteComponent,
});

function RouteComponent() {
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { language } = useLanguage();
  const location = useLocation();
  useEffect(() => {
    trackEvent("kbc_winner_page_viewed", {
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      screen_name: location.pathname,
    });
  });
  const winnerDates = getKbcAllWinnerDates().reverse();
  return (
    <>
      <CampaignAppBar
        left={<BackAppbarAction />}
        right={<SupportAppbarAction />}
        scrollStyleOptions={{
          offsetStyles: {
            backgroundColor: {
              threshold: 5,
              before: "transparent",
              after: "#141009",
            },
            borderBottomWidth: {
              threshold: 5,
              before: 0,
              after: "var(--border-w-sm)",
            },
          },
        }}
      >
        <img src={appBarLogo} alt="" width={92} />
      </CampaignAppBar>
      <div className="px-5">
        <div className="flex flex-col pt-2 gap-1 items-center text-center text-white">
          <p className="text-heading1"> Gift hamper winners</p>
          <p className="text-white-60 text-body1">
            Congratulations to all the winners! We'll reach <br />
            out to you over email for further details
          </p>
        </div>
      </div>
      <>
        {winnerDates.map((date) => (
          <img
            key={date}
            src={`https://assets.stablemoney.in/kbc/leaderboard/${date}/hamper_inventory_master.webp`}
            alt="Hamper"
          />
        ))}
      </>
    </>
  );
}
