import { Block, createFileRoute, useNavigate } from "@tanstack/react-router";
import backgroundImage from "@/assets/illustrations/mutual-funds/background-image.webp";
import { useBodyBackground } from "@/hooks/body-classes";
import CampaignAppBar from "@/components/campaign/ui/campaign-app-bar";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import SupportAppbarAction from "@/components/campaign/functional/mutual-funds-support-appbar-action";
import { useEffect, useState } from "react";
import { trackEvent } from "@/utils/analytics";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { MutualFundsBlocker } from "@/components/campaign/functional/mutual-funds-blocker";
import { goldSilverQuestions } from "./-quiz/quiz";
import { closeWebview } from "@/utils/routing";
import nextButton from "@/assets/illustrations/mutual-funds/next-button.svg";
import { BgImagePendingComponent } from "@/components/campaign/functional/mf-bg-pending-component";
import QuizExplanation from "./-quiz/explanation";
import { QuizToggleGroup } from "@/components/campaign/functional/mf-quiz-toggle-group/quiz-toggle-group";
import finishButton from "@/assets/illustrations/mutual-funds/finish-button.svg";

type QuizSearch = {
  question?: number;
};

type SelectedOption =
  | { label: string; value: string; explanation: string; verdict: boolean }
  | undefined;

export const Route = createFileRoute("/campaign/mutual-funds/quiz/")({
  component: RouteComponent,
  validateSearch: (searchParams: Record<string, unknown>): QuizSearch => {
    return {
      question: Number(searchParams?.question ?? 0),
    };
  },
  pendingComponent: BgImagePendingComponent,
});

function RouteComponent() {
  const { question = 0 } = Route.useSearch();
  const navigate = useNavigate();
  const [isSelected, setIsSelected] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);
  const [selectedOption, setSelectedOption] = useState<SelectedOption>();
  useBodyBackground(backgroundImage);
  useEffect(() => {
    trackEvent("mutual_funds_quiz_question_viewed", {
      question_number: question + 1,
    });
  }, []);

  const currentQuestion = goldSilverQuestions[question];

  const handleChange = (value: string[]) => {
    setIsSelected(true);
    const selectedOption = currentQuestion.options.find(
      (opt) => opt.value === value[0]
    );
    setSelectedOption(selectedOption);
    trackEvent("mutual_funds_quiz_option_tapped", {
      question_number: question + 1,
      option_selected: selectedOption?.label,
    });
  };

  const handleComplete = () => {
    setIsNavigating(true);
    setTimeout(() => {
      if (question === goldSilverQuestions.length - 1) {
        trackEvent("mutual_funds_quiz_completed");
        closeWebview();
        return;
      }
      navigate({
        to: "/campaign/mutual-funds/quiz",
        search: { question: question + 1 },
        replace: question !== 0,
      });
      setIsSelected(false);
      setIsNavigating(false);
    }, 100);
  };

  if (!currentQuestion) {
    return <div>Question not found</div>;
  }

  const options = currentQuestion.options.map((option) => ({
    label: option.label,
    value: option.value,
    verdict: option.verdict,
  }));

  return (
    <Block
      shouldBlockFn={() => !isNavigating}
      enableBeforeUnload={!isNavigating}
      withResolver
    >
      {({ status, reset }) => (
        <>
          <div
            className={
              status === "blocked" ? "pointer-events-none blur-sm" : ""
            }
            aria-hidden={status === "blocked"}
          >
            <CampaignAppBar
              left={<BackAppbarAction />}
              right={<SupportAppbarAction />}
            >
              <p className="text-title-all-caps font-medium text-white">
                {" "}
                Gold & Silver
              </p>
            </CampaignAppBar>
            <div className="px-5 space-y-10">
              <div className="flex flex-col gap-6 pt-7">
                <div className="flex flex-col gap-3 text-white items-center">
                  <div className="flex items-center justify-center gap-2">
                    <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF00] to-[#FFFFFF] opacity-50" />
                    <p className="text-title-all-caps font-medium">
                      {question + 1}/{goldSilverQuestions.length}
                    </p>
                    <div className="h-[1px] w-[48px] bg-linear-to-r from-[#FFFFFF] to-[#FFFFFF00] opacity-50" />
                  </div>
                  <div className="text-heading2 text-center space-y-1.5">
                    <p>{currentQuestion.question}</p>
                  </div>
                </div>

                <QuizToggleGroup
                  key={currentQuestion.id}
                  options={options}
                  multiple={false}
                  deselectable={false}
                  disabled={isSelected}
                  showResults={isSelected}
                  onChange={(details) => handleChange(details.value)}
                />

                <FloatingFooterContent style="transparent">
                  {isSelected ? (
                    question === goldSilverQuestions.length - 1 ? (
                      <button onClick={handleComplete} className="w-full">
                        <img src={finishButton} alt="Next" className="w-full" />
                      </button>
                    ) : (
                      <button onClick={handleComplete} className="w-full">
                        <img src={nextButton} alt="Next" className="w-full" />
                      </button>
                    )
                  ) : question === goldSilverQuestions.length - 1 ? (
                    <button className="w-full">
                      <img
                        src={finishButton}
                        alt="Next"
                        className="w-full opacity-40"
                      />
                    </button>
                  ) : (
                    <button className="w-full">
                      <img
                        src={nextButton}
                        alt="Next"
                        className="w-full opacity-40"
                      />
                    </button>
                  )}
                </FloatingFooterContent>
              </div>
              {isSelected && (
                <QuizExplanation
                  explanation={selectedOption?.explanation ?? ""}
                  verdict={selectedOption?.verdict ?? false}
                />
              )}
            </div>
          </div>
          {status === "blocked" && (
            <MutualFundsBlocker reset={reset} question={question + 1} />
          )}
        </>
      )}
    </Block>
  );
}
