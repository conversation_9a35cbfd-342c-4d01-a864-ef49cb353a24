import tick from "@/assets/icons/mutual-funds/tick.svg";
import error from "@/assets/icons/mutual-funds/error.svg";

type ExplanationProps = {
  explanation: string;
  verdict: boolean;
};

export default function QuizExplanation({
  explanation,
  verdict,
}: ExplanationProps) {
  return (
    <div className="flex flex-col gap-3 items-center">
      <img
        src={verdict ? tick : error}
        alt={verdict ? "tick" : "error"}
        className="size-8"
      />
      <p className="text-body1 text-white-60 text-center"> {explanation} </p>
    </div>
  );
}
