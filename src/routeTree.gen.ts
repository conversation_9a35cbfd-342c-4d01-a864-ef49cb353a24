/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as IndexRouteImport } from './routes/index'
import { Route as CampaignKbcIndexRouteImport } from './routes/campaign/kbc/index'
import { Route as CampaignMutualFundsQuizIndexRouteImport } from './routes/campaign/mutual-funds/quiz/index'
import { Route as CampaignKbcReferralIndexRouteImport } from './routes/campaign/kbc/referral/index'
import { Route as CampaignKbcQuizIndexRouteImport } from './routes/campaign/kbc/quiz/index'
import { Route as CampaignKbcQuizLoaderIndexRouteImport } from './routes/campaign/kbc/quiz-loader/index'
import { Route as CampaignKbcPollIndexRouteImport } from './routes/campaign/kbc/poll/index'
import { Route as CampaignKbcOnboardingIndexRouteImport } from './routes/campaign/kbc/onboarding/index'
import { Route as CampaignKbcLeaderboardIndexRouteImport } from './routes/campaign/kbc/leaderboard/index'
import { Route as CampaignKbcFaqIndexRouteImport } from './routes/campaign/kbc/faq/index'
import { Route as CampaignKbcBirthdayIndexRouteImport } from './routes/campaign/kbc/birthday/index'
import { Route as CampaignKbcOnboardingOnboardingLayoutRouteImport } from './routes/campaign/kbc/onboarding/_onboardingLayout'
import { Route as CampaignKbcQuizScoreIndexRouteImport } from './routes/campaign/kbc/quiz/score/index'
import { Route as CampaignKbcQuizScoreLoaderIndexRouteImport } from './routes/campaign/kbc/quiz/score-loader/index'
import { Route as CampaignKbcPollAttemptIndexRouteImport } from './routes/campaign/kbc/poll/attempt/index'
import { Route as CampaignKbcPollAttemptLoaderIndexRouteImport } from './routes/campaign/kbc/poll/attempt-loader/index'
import { Route as CampaignKbcLeaderboardHamperIndexRouteImport } from './routes/campaign/kbc/leaderboard/hamper/index'
import { Route as CampaignKbcBirthdayFaqIndexRouteImport } from './routes/campaign/kbc/birthday/faq/index'
import { Route as CampaignKbcOnboardingOnboardingLayoutPersonaIndexRouteImport } from './routes/campaign/kbc/onboarding/_onboardingLayout/persona/index'
import { Route as CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRouteImport } from './routes/campaign/kbc/onboarding/_onboardingLayout/know-you/index'
import { Route as CampaignKbcOnboardingOnboardingLayoutDetailsIndexRouteImport } from './routes/campaign/kbc/onboarding/_onboardingLayout/details/index'
import { Route as CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRouteImport } from './routes/campaign/kbc/onboarding/_onboardingLayout/almost-done/index'

const CampaignKbcOnboardingRouteImport = createFileRoute(
  '/campaign/kbc/onboarding',
)()

const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const CampaignKbcOnboardingRoute = CampaignKbcOnboardingRouteImport.update({
  id: '/campaign/kbc/onboarding',
  path: '/campaign/kbc/onboarding',
  getParentRoute: () => rootRouteImport,
} as any)
const CampaignKbcIndexRoute = CampaignKbcIndexRouteImport.update({
  id: '/campaign/kbc/',
  path: '/campaign/kbc/',
  getParentRoute: () => rootRouteImport,
} as any)
const CampaignMutualFundsQuizIndexRoute =
  CampaignMutualFundsQuizIndexRouteImport.update({
    id: '/campaign/mutual-funds/quiz/',
    path: '/campaign/mutual-funds/quiz/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcReferralIndexRoute =
  CampaignKbcReferralIndexRouteImport.update({
    id: '/campaign/kbc/referral/',
    path: '/campaign/kbc/referral/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcQuizIndexRoute = CampaignKbcQuizIndexRouteImport.update({
  id: '/campaign/kbc/quiz/',
  path: '/campaign/kbc/quiz/',
  getParentRoute: () => rootRouteImport,
} as any)
const CampaignKbcQuizLoaderIndexRoute =
  CampaignKbcQuizLoaderIndexRouteImport.update({
    id: '/campaign/kbc/quiz-loader/',
    path: '/campaign/kbc/quiz-loader/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcPollIndexRoute = CampaignKbcPollIndexRouteImport.update({
  id: '/campaign/kbc/poll/',
  path: '/campaign/kbc/poll/',
  getParentRoute: () => rootRouteImport,
} as any)
const CampaignKbcOnboardingIndexRoute =
  CampaignKbcOnboardingIndexRouteImport.update({
    id: '/',
    path: '/',
    getParentRoute: () => CampaignKbcOnboardingRoute,
  } as any)
const CampaignKbcLeaderboardIndexRoute =
  CampaignKbcLeaderboardIndexRouteImport.update({
    id: '/campaign/kbc/leaderboard/',
    path: '/campaign/kbc/leaderboard/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcFaqIndexRoute = CampaignKbcFaqIndexRouteImport.update({
  id: '/campaign/kbc/faq/',
  path: '/campaign/kbc/faq/',
  getParentRoute: () => rootRouteImport,
} as any)
const CampaignKbcBirthdayIndexRoute =
  CampaignKbcBirthdayIndexRouteImport.update({
    id: '/campaign/kbc/birthday/',
    path: '/campaign/kbc/birthday/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcOnboardingOnboardingLayoutRoute =
  CampaignKbcOnboardingOnboardingLayoutRouteImport.update({
    id: '/_onboardingLayout',
    getParentRoute: () => CampaignKbcOnboardingRoute,
  } as any)
const CampaignKbcQuizScoreIndexRoute =
  CampaignKbcQuizScoreIndexRouteImport.update({
    id: '/campaign/kbc/quiz/score/',
    path: '/campaign/kbc/quiz/score/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcQuizScoreLoaderIndexRoute =
  CampaignKbcQuizScoreLoaderIndexRouteImport.update({
    id: '/campaign/kbc/quiz/score-loader/',
    path: '/campaign/kbc/quiz/score-loader/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcPollAttemptIndexRoute =
  CampaignKbcPollAttemptIndexRouteImport.update({
    id: '/campaign/kbc/poll/attempt/',
    path: '/campaign/kbc/poll/attempt/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcPollAttemptLoaderIndexRoute =
  CampaignKbcPollAttemptLoaderIndexRouteImport.update({
    id: '/campaign/kbc/poll/attempt-loader/',
    path: '/campaign/kbc/poll/attempt-loader/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcLeaderboardHamperIndexRoute =
  CampaignKbcLeaderboardHamperIndexRouteImport.update({
    id: '/campaign/kbc/leaderboard/hamper/',
    path: '/campaign/kbc/leaderboard/hamper/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcBirthdayFaqIndexRoute =
  CampaignKbcBirthdayFaqIndexRouteImport.update({
    id: '/campaign/kbc/birthday/faq/',
    path: '/campaign/kbc/birthday/faq/',
    getParentRoute: () => rootRouteImport,
  } as any)
const CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute =
  CampaignKbcOnboardingOnboardingLayoutPersonaIndexRouteImport.update({
    id: '/persona/',
    path: '/persona/',
    getParentRoute: () => CampaignKbcOnboardingOnboardingLayoutRoute,
  } as any)
const CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute =
  CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRouteImport.update({
    id: '/know-you/',
    path: '/know-you/',
    getParentRoute: () => CampaignKbcOnboardingOnboardingLayoutRoute,
  } as any)
const CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute =
  CampaignKbcOnboardingOnboardingLayoutDetailsIndexRouteImport.update({
    id: '/details/',
    path: '/details/',
    getParentRoute: () => CampaignKbcOnboardingOnboardingLayoutRoute,
  } as any)
const CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute =
  CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRouteImport.update({
    id: '/almost-done/',
    path: '/almost-done/',
    getParentRoute: () => CampaignKbcOnboardingOnboardingLayoutRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/campaign/kbc': typeof CampaignKbcIndexRoute
  '/campaign/kbc/onboarding': typeof CampaignKbcOnboardingOnboardingLayoutRouteWithChildren
  '/campaign/kbc/birthday': typeof CampaignKbcBirthdayIndexRoute
  '/campaign/kbc/faq': typeof CampaignKbcFaqIndexRoute
  '/campaign/kbc/leaderboard': typeof CampaignKbcLeaderboardIndexRoute
  '/campaign/kbc/onboarding/': typeof CampaignKbcOnboardingIndexRoute
  '/campaign/kbc/poll': typeof CampaignKbcPollIndexRoute
  '/campaign/kbc/quiz-loader': typeof CampaignKbcQuizLoaderIndexRoute
  '/campaign/kbc/quiz': typeof CampaignKbcQuizIndexRoute
  '/campaign/kbc/referral': typeof CampaignKbcReferralIndexRoute
  '/campaign/mutual-funds/quiz': typeof CampaignMutualFundsQuizIndexRoute
  '/campaign/kbc/birthday/faq': typeof CampaignKbcBirthdayFaqIndexRoute
  '/campaign/kbc/leaderboard/hamper': typeof CampaignKbcLeaderboardHamperIndexRoute
  '/campaign/kbc/poll/attempt-loader': typeof CampaignKbcPollAttemptLoaderIndexRoute
  '/campaign/kbc/poll/attempt': typeof CampaignKbcPollAttemptIndexRoute
  '/campaign/kbc/quiz/score-loader': typeof CampaignKbcQuizScoreLoaderIndexRoute
  '/campaign/kbc/quiz/score': typeof CampaignKbcQuizScoreIndexRoute
  '/campaign/kbc/onboarding/almost-done': typeof CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute
  '/campaign/kbc/onboarding/details': typeof CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute
  '/campaign/kbc/onboarding/know-you': typeof CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute
  '/campaign/kbc/onboarding/persona': typeof CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/campaign/kbc': typeof CampaignKbcIndexRoute
  '/campaign/kbc/onboarding': typeof CampaignKbcOnboardingIndexRoute
  '/campaign/kbc/birthday': typeof CampaignKbcBirthdayIndexRoute
  '/campaign/kbc/faq': typeof CampaignKbcFaqIndexRoute
  '/campaign/kbc/leaderboard': typeof CampaignKbcLeaderboardIndexRoute
  '/campaign/kbc/poll': typeof CampaignKbcPollIndexRoute
  '/campaign/kbc/quiz-loader': typeof CampaignKbcQuizLoaderIndexRoute
  '/campaign/kbc/quiz': typeof CampaignKbcQuizIndexRoute
  '/campaign/kbc/referral': typeof CampaignKbcReferralIndexRoute
  '/campaign/mutual-funds/quiz': typeof CampaignMutualFundsQuizIndexRoute
  '/campaign/kbc/birthday/faq': typeof CampaignKbcBirthdayFaqIndexRoute
  '/campaign/kbc/leaderboard/hamper': typeof CampaignKbcLeaderboardHamperIndexRoute
  '/campaign/kbc/poll/attempt-loader': typeof CampaignKbcPollAttemptLoaderIndexRoute
  '/campaign/kbc/poll/attempt': typeof CampaignKbcPollAttemptIndexRoute
  '/campaign/kbc/quiz/score-loader': typeof CampaignKbcQuizScoreLoaderIndexRoute
  '/campaign/kbc/quiz/score': typeof CampaignKbcQuizScoreIndexRoute
  '/campaign/kbc/onboarding/almost-done': typeof CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute
  '/campaign/kbc/onboarding/details': typeof CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute
  '/campaign/kbc/onboarding/know-you': typeof CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute
  '/campaign/kbc/onboarding/persona': typeof CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/campaign/kbc/': typeof CampaignKbcIndexRoute
  '/campaign/kbc/onboarding': typeof CampaignKbcOnboardingRouteWithChildren
  '/campaign/kbc/onboarding/_onboardingLayout': typeof CampaignKbcOnboardingOnboardingLayoutRouteWithChildren
  '/campaign/kbc/birthday/': typeof CampaignKbcBirthdayIndexRoute
  '/campaign/kbc/faq/': typeof CampaignKbcFaqIndexRoute
  '/campaign/kbc/leaderboard/': typeof CampaignKbcLeaderboardIndexRoute
  '/campaign/kbc/onboarding/': typeof CampaignKbcOnboardingIndexRoute
  '/campaign/kbc/poll/': typeof CampaignKbcPollIndexRoute
  '/campaign/kbc/quiz-loader/': typeof CampaignKbcQuizLoaderIndexRoute
  '/campaign/kbc/quiz/': typeof CampaignKbcQuizIndexRoute
  '/campaign/kbc/referral/': typeof CampaignKbcReferralIndexRoute
  '/campaign/mutual-funds/quiz/': typeof CampaignMutualFundsQuizIndexRoute
  '/campaign/kbc/birthday/faq/': typeof CampaignKbcBirthdayFaqIndexRoute
  '/campaign/kbc/leaderboard/hamper/': typeof CampaignKbcLeaderboardHamperIndexRoute
  '/campaign/kbc/poll/attempt-loader/': typeof CampaignKbcPollAttemptLoaderIndexRoute
  '/campaign/kbc/poll/attempt/': typeof CampaignKbcPollAttemptIndexRoute
  '/campaign/kbc/quiz/score-loader/': typeof CampaignKbcQuizScoreLoaderIndexRoute
  '/campaign/kbc/quiz/score/': typeof CampaignKbcQuizScoreIndexRoute
  '/campaign/kbc/onboarding/_onboardingLayout/almost-done/': typeof CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute
  '/campaign/kbc/onboarding/_onboardingLayout/details/': typeof CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute
  '/campaign/kbc/onboarding/_onboardingLayout/know-you/': typeof CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute
  '/campaign/kbc/onboarding/_onboardingLayout/persona/': typeof CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/campaign/kbc'
    | '/campaign/kbc/onboarding'
    | '/campaign/kbc/birthday'
    | '/campaign/kbc/faq'
    | '/campaign/kbc/leaderboard'
    | '/campaign/kbc/onboarding/'
    | '/campaign/kbc/poll'
    | '/campaign/kbc/quiz-loader'
    | '/campaign/kbc/quiz'
    | '/campaign/kbc/referral'
    | '/campaign/mutual-funds/quiz'
    | '/campaign/kbc/birthday/faq'
    | '/campaign/kbc/leaderboard/hamper'
    | '/campaign/kbc/poll/attempt-loader'
    | '/campaign/kbc/poll/attempt'
    | '/campaign/kbc/quiz/score-loader'
    | '/campaign/kbc/quiz/score'
    | '/campaign/kbc/onboarding/almost-done'
    | '/campaign/kbc/onboarding/details'
    | '/campaign/kbc/onboarding/know-you'
    | '/campaign/kbc/onboarding/persona'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/campaign/kbc'
    | '/campaign/kbc/onboarding'
    | '/campaign/kbc/birthday'
    | '/campaign/kbc/faq'
    | '/campaign/kbc/leaderboard'
    | '/campaign/kbc/poll'
    | '/campaign/kbc/quiz-loader'
    | '/campaign/kbc/quiz'
    | '/campaign/kbc/referral'
    | '/campaign/mutual-funds/quiz'
    | '/campaign/kbc/birthday/faq'
    | '/campaign/kbc/leaderboard/hamper'
    | '/campaign/kbc/poll/attempt-loader'
    | '/campaign/kbc/poll/attempt'
    | '/campaign/kbc/quiz/score-loader'
    | '/campaign/kbc/quiz/score'
    | '/campaign/kbc/onboarding/almost-done'
    | '/campaign/kbc/onboarding/details'
    | '/campaign/kbc/onboarding/know-you'
    | '/campaign/kbc/onboarding/persona'
  id:
    | '__root__'
    | '/'
    | '/campaign/kbc/'
    | '/campaign/kbc/onboarding'
    | '/campaign/kbc/onboarding/_onboardingLayout'
    | '/campaign/kbc/birthday/'
    | '/campaign/kbc/faq/'
    | '/campaign/kbc/leaderboard/'
    | '/campaign/kbc/onboarding/'
    | '/campaign/kbc/poll/'
    | '/campaign/kbc/quiz-loader/'
    | '/campaign/kbc/quiz/'
    | '/campaign/kbc/referral/'
    | '/campaign/mutual-funds/quiz/'
    | '/campaign/kbc/birthday/faq/'
    | '/campaign/kbc/leaderboard/hamper/'
    | '/campaign/kbc/poll/attempt-loader/'
    | '/campaign/kbc/poll/attempt/'
    | '/campaign/kbc/quiz/score-loader/'
    | '/campaign/kbc/quiz/score/'
    | '/campaign/kbc/onboarding/_onboardingLayout/almost-done/'
    | '/campaign/kbc/onboarding/_onboardingLayout/details/'
    | '/campaign/kbc/onboarding/_onboardingLayout/know-you/'
    | '/campaign/kbc/onboarding/_onboardingLayout/persona/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  CampaignKbcIndexRoute: typeof CampaignKbcIndexRoute
  CampaignKbcOnboardingRoute: typeof CampaignKbcOnboardingRouteWithChildren
  CampaignKbcBirthdayIndexRoute: typeof CampaignKbcBirthdayIndexRoute
  CampaignKbcFaqIndexRoute: typeof CampaignKbcFaqIndexRoute
  CampaignKbcLeaderboardIndexRoute: typeof CampaignKbcLeaderboardIndexRoute
  CampaignKbcPollIndexRoute: typeof CampaignKbcPollIndexRoute
  CampaignKbcQuizLoaderIndexRoute: typeof CampaignKbcQuizLoaderIndexRoute
  CampaignKbcQuizIndexRoute: typeof CampaignKbcQuizIndexRoute
  CampaignKbcReferralIndexRoute: typeof CampaignKbcReferralIndexRoute
  CampaignMutualFundsQuizIndexRoute: typeof CampaignMutualFundsQuizIndexRoute
  CampaignKbcBirthdayFaqIndexRoute: typeof CampaignKbcBirthdayFaqIndexRoute
  CampaignKbcLeaderboardHamperIndexRoute: typeof CampaignKbcLeaderboardHamperIndexRoute
  CampaignKbcPollAttemptLoaderIndexRoute: typeof CampaignKbcPollAttemptLoaderIndexRoute
  CampaignKbcPollAttemptIndexRoute: typeof CampaignKbcPollAttemptIndexRoute
  CampaignKbcQuizScoreLoaderIndexRoute: typeof CampaignKbcQuizScoreLoaderIndexRoute
  CampaignKbcQuizScoreIndexRoute: typeof CampaignKbcQuizScoreIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/onboarding': {
      id: '/campaign/kbc/onboarding'
      path: '/campaign/kbc/onboarding'
      fullPath: '/campaign/kbc/onboarding'
      preLoaderRoute: typeof CampaignKbcOnboardingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/': {
      id: '/campaign/kbc/'
      path: '/campaign/kbc'
      fullPath: '/campaign/kbc'
      preLoaderRoute: typeof CampaignKbcIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/mutual-funds/quiz/': {
      id: '/campaign/mutual-funds/quiz/'
      path: '/campaign/mutual-funds/quiz'
      fullPath: '/campaign/mutual-funds/quiz'
      preLoaderRoute: typeof CampaignMutualFundsQuizIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/referral/': {
      id: '/campaign/kbc/referral/'
      path: '/campaign/kbc/referral'
      fullPath: '/campaign/kbc/referral'
      preLoaderRoute: typeof CampaignKbcReferralIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/quiz/': {
      id: '/campaign/kbc/quiz/'
      path: '/campaign/kbc/quiz'
      fullPath: '/campaign/kbc/quiz'
      preLoaderRoute: typeof CampaignKbcQuizIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/quiz-loader/': {
      id: '/campaign/kbc/quiz-loader/'
      path: '/campaign/kbc/quiz-loader'
      fullPath: '/campaign/kbc/quiz-loader'
      preLoaderRoute: typeof CampaignKbcQuizLoaderIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/poll/': {
      id: '/campaign/kbc/poll/'
      path: '/campaign/kbc/poll'
      fullPath: '/campaign/kbc/poll'
      preLoaderRoute: typeof CampaignKbcPollIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/onboarding/': {
      id: '/campaign/kbc/onboarding/'
      path: '/'
      fullPath: '/campaign/kbc/onboarding/'
      preLoaderRoute: typeof CampaignKbcOnboardingIndexRouteImport
      parentRoute: typeof CampaignKbcOnboardingRoute
    }
    '/campaign/kbc/leaderboard/': {
      id: '/campaign/kbc/leaderboard/'
      path: '/campaign/kbc/leaderboard'
      fullPath: '/campaign/kbc/leaderboard'
      preLoaderRoute: typeof CampaignKbcLeaderboardIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/faq/': {
      id: '/campaign/kbc/faq/'
      path: '/campaign/kbc/faq'
      fullPath: '/campaign/kbc/faq'
      preLoaderRoute: typeof CampaignKbcFaqIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/birthday/': {
      id: '/campaign/kbc/birthday/'
      path: '/campaign/kbc/birthday'
      fullPath: '/campaign/kbc/birthday'
      preLoaderRoute: typeof CampaignKbcBirthdayIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/onboarding/_onboardingLayout': {
      id: '/campaign/kbc/onboarding/_onboardingLayout'
      path: '/campaign/kbc/onboarding'
      fullPath: '/campaign/kbc/onboarding'
      preLoaderRoute: typeof CampaignKbcOnboardingOnboardingLayoutRouteImport
      parentRoute: typeof CampaignKbcOnboardingRoute
    }
    '/campaign/kbc/quiz/score/': {
      id: '/campaign/kbc/quiz/score/'
      path: '/campaign/kbc/quiz/score'
      fullPath: '/campaign/kbc/quiz/score'
      preLoaderRoute: typeof CampaignKbcQuizScoreIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/quiz/score-loader/': {
      id: '/campaign/kbc/quiz/score-loader/'
      path: '/campaign/kbc/quiz/score-loader'
      fullPath: '/campaign/kbc/quiz/score-loader'
      preLoaderRoute: typeof CampaignKbcQuizScoreLoaderIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/poll/attempt/': {
      id: '/campaign/kbc/poll/attempt/'
      path: '/campaign/kbc/poll/attempt'
      fullPath: '/campaign/kbc/poll/attempt'
      preLoaderRoute: typeof CampaignKbcPollAttemptIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/poll/attempt-loader/': {
      id: '/campaign/kbc/poll/attempt-loader/'
      path: '/campaign/kbc/poll/attempt-loader'
      fullPath: '/campaign/kbc/poll/attempt-loader'
      preLoaderRoute: typeof CampaignKbcPollAttemptLoaderIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/leaderboard/hamper/': {
      id: '/campaign/kbc/leaderboard/hamper/'
      path: '/campaign/kbc/leaderboard/hamper'
      fullPath: '/campaign/kbc/leaderboard/hamper'
      preLoaderRoute: typeof CampaignKbcLeaderboardHamperIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/birthday/faq/': {
      id: '/campaign/kbc/birthday/faq/'
      path: '/campaign/kbc/birthday/faq'
      fullPath: '/campaign/kbc/birthday/faq'
      preLoaderRoute: typeof CampaignKbcBirthdayFaqIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/campaign/kbc/onboarding/_onboardingLayout/persona/': {
      id: '/campaign/kbc/onboarding/_onboardingLayout/persona/'
      path: '/persona'
      fullPath: '/campaign/kbc/onboarding/persona'
      preLoaderRoute: typeof CampaignKbcOnboardingOnboardingLayoutPersonaIndexRouteImport
      parentRoute: typeof CampaignKbcOnboardingOnboardingLayoutRoute
    }
    '/campaign/kbc/onboarding/_onboardingLayout/know-you/': {
      id: '/campaign/kbc/onboarding/_onboardingLayout/know-you/'
      path: '/know-you'
      fullPath: '/campaign/kbc/onboarding/know-you'
      preLoaderRoute: typeof CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRouteImport
      parentRoute: typeof CampaignKbcOnboardingOnboardingLayoutRoute
    }
    '/campaign/kbc/onboarding/_onboardingLayout/details/': {
      id: '/campaign/kbc/onboarding/_onboardingLayout/details/'
      path: '/details'
      fullPath: '/campaign/kbc/onboarding/details'
      preLoaderRoute: typeof CampaignKbcOnboardingOnboardingLayoutDetailsIndexRouteImport
      parentRoute: typeof CampaignKbcOnboardingOnboardingLayoutRoute
    }
    '/campaign/kbc/onboarding/_onboardingLayout/almost-done/': {
      id: '/campaign/kbc/onboarding/_onboardingLayout/almost-done/'
      path: '/almost-done'
      fullPath: '/campaign/kbc/onboarding/almost-done'
      preLoaderRoute: typeof CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRouteImport
      parentRoute: typeof CampaignKbcOnboardingOnboardingLayoutRoute
    }
  }
}

interface CampaignKbcOnboardingOnboardingLayoutRouteChildren {
  CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute: typeof CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute
  CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute: typeof CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute
  CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute: typeof CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute
  CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute: typeof CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute
}

const CampaignKbcOnboardingOnboardingLayoutRouteChildren: CampaignKbcOnboardingOnboardingLayoutRouteChildren =
  {
    CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute:
      CampaignKbcOnboardingOnboardingLayoutAlmostDoneIndexRoute,
    CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute:
      CampaignKbcOnboardingOnboardingLayoutDetailsIndexRoute,
    CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute:
      CampaignKbcOnboardingOnboardingLayoutKnowYouIndexRoute,
    CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute:
      CampaignKbcOnboardingOnboardingLayoutPersonaIndexRoute,
  }

const CampaignKbcOnboardingOnboardingLayoutRouteWithChildren =
  CampaignKbcOnboardingOnboardingLayoutRoute._addFileChildren(
    CampaignKbcOnboardingOnboardingLayoutRouteChildren,
  )

interface CampaignKbcOnboardingRouteChildren {
  CampaignKbcOnboardingOnboardingLayoutRoute: typeof CampaignKbcOnboardingOnboardingLayoutRouteWithChildren
  CampaignKbcOnboardingIndexRoute: typeof CampaignKbcOnboardingIndexRoute
}

const CampaignKbcOnboardingRouteChildren: CampaignKbcOnboardingRouteChildren = {
  CampaignKbcOnboardingOnboardingLayoutRoute:
    CampaignKbcOnboardingOnboardingLayoutRouteWithChildren,
  CampaignKbcOnboardingIndexRoute: CampaignKbcOnboardingIndexRoute,
}

const CampaignKbcOnboardingRouteWithChildren =
  CampaignKbcOnboardingRoute._addFileChildren(
    CampaignKbcOnboardingRouteChildren,
  )

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  CampaignKbcIndexRoute: CampaignKbcIndexRoute,
  CampaignKbcOnboardingRoute: CampaignKbcOnboardingRouteWithChildren,
  CampaignKbcBirthdayIndexRoute: CampaignKbcBirthdayIndexRoute,
  CampaignKbcFaqIndexRoute: CampaignKbcFaqIndexRoute,
  CampaignKbcLeaderboardIndexRoute: CampaignKbcLeaderboardIndexRoute,
  CampaignKbcPollIndexRoute: CampaignKbcPollIndexRoute,
  CampaignKbcQuizLoaderIndexRoute: CampaignKbcQuizLoaderIndexRoute,
  CampaignKbcQuizIndexRoute: CampaignKbcQuizIndexRoute,
  CampaignKbcReferralIndexRoute: CampaignKbcReferralIndexRoute,
  CampaignMutualFundsQuizIndexRoute: CampaignMutualFundsQuizIndexRoute,
  CampaignKbcBirthdayFaqIndexRoute: CampaignKbcBirthdayFaqIndexRoute,
  CampaignKbcLeaderboardHamperIndexRoute:
    CampaignKbcLeaderboardHamperIndexRoute,
  CampaignKbcPollAttemptLoaderIndexRoute:
    CampaignKbcPollAttemptLoaderIndexRoute,
  CampaignKbcPollAttemptIndexRoute: CampaignKbcPollAttemptIndexRoute,
  CampaignKbcQuizScoreLoaderIndexRoute: CampaignKbcQuizScoreLoaderIndexRoute,
  CampaignKbcQuizScoreIndexRoute: CampaignKbcQuizScoreIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
