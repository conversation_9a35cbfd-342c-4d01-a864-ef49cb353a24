// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Faq.proto (package com.stablemoney.api.business.faq, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Faq.proto.
 */
export const file_Faq: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message com.stablemoney.api.business.faq.BankFaqResponse
 */
export type BankFaqResponse = Message<"com.stablemoney.api.business.faq.BankFaqResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 1;
   */
  bankFaq: Faq[];
};

/**
 * Describes the message com.stablemoney.api.business.faq.BankFaqResponse.
 * Use `create(BankFaqResponseSchema)` to create a new message.
 */
export const BankFaqResponseSchema: GenMessage<BankFaqResponse> = /*@__PURE__*/
  messageDesc(file_Faq, 0);

/**
 * @generated from message com.stablemoney.api.business.faq.Faq
 */
export type Faq = Message<"com.stablemoney.api.business.faq.Faq"> & {
  /**
   * @generated from field: string question = 1;
   */
  question: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;

  /**
   * @generated from field: string html_answer = 3;
   */
  htmlAnswer: string;

  /**
   * @generated from field: repeated string bank_faq_category_list = 4;
   */
  bankFaqCategoryList: string[];

  /**
   * @generated from field: string question_tag = 5;
   */
  questionTag: string;

  /**
   * @generated from field: optional string faq_id = 6;
   */
  faqId?: string;

  /**
   * @generated from field: int32 min_version_number = 7;
   */
  minVersionNumber: number;

  /**
   * @generated from field: int32 max_version_number = 8;
   */
  maxVersionNumber: number;

  /**
   * @generated from field: optional string user_name = 9;
   */
  userName?: string;

  /**
   * @generated from field: optional string user_location = 10;
   */
  userLocation?: string;

  /**
   * @generated from field: optional string user_profile_url = 11;
   */
  userProfileUrl?: string;

  /**
   * @generated from field: optional int32 upvotes = 12;
   */
  upvotes?: number;

  /**
   * @generated from field: optional int32 downvotes = 13;
   */
  downvotes?: number;
};

/**
 * Describes the message com.stablemoney.api.business.faq.Faq.
 * Use `create(FaqSchema)` to create a new message.
 */
export const FaqSchema: GenMessage<Faq> = /*@__PURE__*/
  messageDesc(file_Faq, 1);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqResponse
 */
export type SupportFaqResponse = Message<"com.stablemoney.api.business.faq.SupportFaqResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 1;
   */
  bankFaq: Faq[];
};

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqResponse.
 * Use `create(SupportFaqResponseSchema)` to create a new message.
 */
export const SupportFaqResponseSchema: GenMessage<SupportFaqResponse> = /*@__PURE__*/
  messageDesc(file_Faq, 2);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqV2
 */
export type SupportFaqV2 = Message<"com.stablemoney.api.business.faq.SupportFaqV2"> & {
  /**
   * @generated from field: com.stablemoney.api.business.faq.Faq faq = 1;
   */
  faq?: Faq;

  /**
   * @generated from field: string support_faq_icon_url = 3;
   */
  supportFaqIconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqV2.
 * Use `create(SupportFaqV2Schema)` to create a new message.
 */
export const SupportFaqV2Schema: GenMessage<SupportFaqV2> = /*@__PURE__*/
  messageDesc(file_Faq, 3);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqResponseV2
 */
export type SupportFaqResponseV2 = Message<"com.stablemoney.api.business.faq.SupportFaqResponseV2"> & {
  /**
   * @generated from field: string category_id = 1;
   */
  categoryId: string;

  /**
   * @generated from field: string category_name = 2;
   */
  categoryName: string;

  /**
   * @generated from field: string category_icon_url = 3;
   */
  categoryIconUrl: string;

  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.SupportFaqV2 faqs = 4;
   */
  faqs: SupportFaqV2[];

  /**
   * @generated from field: string category_description = 5;
   */
  categoryDescription: string;
};

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqResponseV2.
 * Use `create(SupportFaqResponseV2Schema)` to create a new message.
 */
export const SupportFaqResponseV2Schema: GenMessage<SupportFaqResponseV2> = /*@__PURE__*/
  messageDesc(file_Faq, 4);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqCategoryResponseV2
 */
export type SupportFaqCategoryResponseV2 = Message<"com.stablemoney.api.business.faq.SupportFaqCategoryResponseV2"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.SupportFaqResponseV2 category_support_faq = 1;
   */
  categorySupportFaq: SupportFaqResponseV2[];

  /**
   * @generated from field: string top_category_id = 2;
   */
  topCategoryId: string;
};

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqCategoryResponseV2.
 * Use `create(SupportFaqCategoryResponseV2Schema)` to create a new message.
 */
export const SupportFaqCategoryResponseV2Schema: GenMessage<SupportFaqCategoryResponseV2> = /*@__PURE__*/
  messageDesc(file_Faq, 5);

/**
 * @generated from message com.stablemoney.api.business.faq.AskQuestionRequest
 */
export type AskQuestionRequest = Message<"com.stablemoney.api.business.faq.AskQuestionRequest"> & {
  /**
   * @generated from field: string question = 1;
   */
  question: string;
};

/**
 * Describes the message com.stablemoney.api.business.faq.AskQuestionRequest.
 * Use `create(AskQuestionRequestSchema)` to create a new message.
 */
export const AskQuestionRequestSchema: GenMessage<AskQuestionRequest> = /*@__PURE__*/
  messageDesc(file_Faq, 6);

/**
 * @generated from message com.stablemoney.api.business.faq.GetFaqsRequest
 */
export type GetFaqsRequest = Message<"com.stablemoney.api.business.faq.GetFaqsRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.business.faq.BusinessUnit business_unit = 1;
   */
  businessUnit: BusinessUnit;

  /**
   * @generated from field: string namespace = 2;
   */
  namespace: string;

  /**
   * @generated from field: string identifier = 3;
   */
  identifier: string;
};

/**
 * Describes the message com.stablemoney.api.business.faq.GetFaqsRequest.
 * Use `create(GetFaqsRequestSchema)` to create a new message.
 */
export const GetFaqsRequestSchema: GenMessage<GetFaqsRequest> = /*@__PURE__*/
  messageDesc(file_Faq, 7);

/**
 * @generated from message com.stablemoney.api.business.faq.GetFaqsResponse
 */
export type GetFaqsResponse = Message<"com.stablemoney.api.business.faq.GetFaqsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.Faq faqs = 1;
   */
  faqs: Faq[];
};

/**
 * Describes the message com.stablemoney.api.business.faq.GetFaqsResponse.
 * Use `create(GetFaqsResponseSchema)` to create a new message.
 */
export const GetFaqsResponseSchema: GenMessage<GetFaqsResponse> = /*@__PURE__*/
  messageDesc(file_Faq, 8);

/**
 * @generated from message com.stablemoney.api.business.faq.Cf
 */
export type Cf = Message<"com.stablemoney.api.business.faq.Cf"> & {
  /**
   * @generated from field: string cf_gold_member = 1;
   */
  cfGoldMember: string;

  /**
   * @generated from field: string cf_ticket_cohort = 2;
   */
  cfTicketCohort: string;
};

/**
 * Describes the message com.stablemoney.api.business.faq.Cf.
 * Use `create(CfSchema)` to create a new message.
 */
export const CfSchema: GenMessage<Cf> = /*@__PURE__*/
  messageDesc(file_Faq, 9);

/**
 * @generated from message com.stablemoney.api.business.faq.CreateTicketRequest
 */
export type CreateTicketRequest = Message<"com.stablemoney.api.business.faq.CreateTicketRequest"> & {
  /**
   * @generated from field: string sub_category = 1;
   */
  subCategory: string;

  /**
   * @generated from field: string subject = 2;
   */
  subject: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: repeated string attachments = 4;
   */
  attachments: string[];

  /**
   * @generated from field: string category = 5;
   */
  category: string;

  /**
   * @generated from field: com.stablemoney.api.business.faq.Cf cf = 6;
   */
  cf?: Cf;
};

/**
 * Describes the message com.stablemoney.api.business.faq.CreateTicketRequest.
 * Use `create(CreateTicketRequestSchema)` to create a new message.
 */
export const CreateTicketRequestSchema: GenMessage<CreateTicketRequest> = /*@__PURE__*/
  messageDesc(file_Faq, 10);

/**
 * @generated from message com.stablemoney.api.business.faq.Attachment
 */
export type Attachment = Message<"com.stablemoney.api.business.faq.Attachment"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string href = 3;
   */
  href: string;

  /**
   * @generated from field: int32 size = 4;
   */
  size: number;
};

/**
 * Describes the message com.stablemoney.api.business.faq.Attachment.
 * Use `create(AttachmentSchema)` to create a new message.
 */
export const AttachmentSchema: GenMessage<Attachment> = /*@__PURE__*/
  messageDesc(file_Faq, 11);

/**
 * @generated from message com.stablemoney.api.business.faq.Ticket
 */
export type Ticket = Message<"com.stablemoney.api.business.faq.Ticket"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string status_type = 2;
   */
  statusType: string;

  /**
   * @generated from field: string ticket_number = 3;
   */
  ticketNumber: string;

  /**
   * @generated from field: string created_time = 4;
   */
  createdTime: string;

  /**
   * @generated from field: string subject = 5;
   */
  subject: string;

  /**
   * @generated from field: string description = 6;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.business.faq.Ticket.
 * Use `create(TicketSchema)` to create a new message.
 */
export const TicketSchema: GenMessage<Ticket> = /*@__PURE__*/
  messageDesc(file_Faq, 12);

/**
 * @generated from message com.stablemoney.api.business.faq.GetAllTicketsResponse
 */
export type GetAllTicketsResponse = Message<"com.stablemoney.api.business.faq.GetAllTicketsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.Ticket tickets = 1;
   */
  tickets: Ticket[];
};

/**
 * Describes the message com.stablemoney.api.business.faq.GetAllTicketsResponse.
 * Use `create(GetAllTicketsResponseSchema)` to create a new message.
 */
export const GetAllTicketsResponseSchema: GenMessage<GetAllTicketsResponse> = /*@__PURE__*/
  messageDesc(file_Faq, 13);

/**
 * @generated from message com.stablemoney.api.business.faq.Thread
 */
export type Thread = Message<"com.stablemoney.api.business.faq.Thread"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string ticket_id = 2;
   */
  ticketId: string;

  /**
   * @generated from field: com.stablemoney.api.business.faq.AuthorType authorType = 3;
   */
  authorType: AuthorType;

  /**
   * @generated from field: string time = 4;
   */
  time: string;

  /**
   * @generated from field: string content = 5;
   */
  content: string;

  /**
   * @generated from field: bool hasCompleteContent = 6;
   */
  hasCompleteContent: boolean;

  /**
   * @generated from field: int32 attachmentCount = 7;
   */
  attachmentCount: number;

  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.Attachment attachments = 8;
   */
  attachments: Attachment[];
};

/**
 * Describes the message com.stablemoney.api.business.faq.Thread.
 * Use `create(ThreadSchema)` to create a new message.
 */
export const ThreadSchema: GenMessage<Thread> = /*@__PURE__*/
  messageDesc(file_Faq, 14);

/**
 * @generated from message com.stablemoney.api.business.faq.GetTicketThreads
 */
export type GetTicketThreads = Message<"com.stablemoney.api.business.faq.GetTicketThreads"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.business.faq.Thread threads = 1;
   */
  threads: Thread[];
};

/**
 * Describes the message com.stablemoney.api.business.faq.GetTicketThreads.
 * Use `create(GetTicketThreadsSchema)` to create a new message.
 */
export const GetTicketThreadsSchema: GenMessage<GetTicketThreads> = /*@__PURE__*/
  messageDesc(file_Faq, 15);

/**
 * @generated from message com.stablemoney.api.business.faq.ChatReply
 */
export type ChatReply = Message<"com.stablemoney.api.business.faq.ChatReply"> & {
  /**
   * @generated from field: string content = 1;
   */
  content: string;

  /**
   * @generated from field: repeated string attachmentIds = 3;
   */
  attachmentIds: string[];
};

/**
 * Describes the message com.stablemoney.api.business.faq.ChatReply.
 * Use `create(ChatReplySchema)` to create a new message.
 */
export const ChatReplySchema: GenMessage<ChatReply> = /*@__PURE__*/
  messageDesc(file_Faq, 16);

/**
 * @generated from enum com.stablemoney.api.business.faq.FaqType
 */
export enum FaqType {
  /**
   * @generated from enum value: UNKNOWN_FAQ_TYPE = 0;
   */
  UNKNOWN_FAQ_TYPE = 0,

  /**
   * @generated from enum value: DEFAULT_FAQ_TYPE = 1;
   */
  DEFAULT_FAQ_TYPE = 1,

  /**
   * @generated from enum value: REWARD_FAQ_TYPE = 2;
   */
  REWARD_FAQ_TYPE = 2,

  /**
   * @generated from enum value: REFERRAL_FAQ_TYPE = 3;
   */
  REFERRAL_FAQ_TYPE = 3,

  /**
   * @generated from enum value: CATEGORY_FAQ_TYPE = 4;
   */
  CATEGORY_FAQ_TYPE = 4,
}

/**
 * Describes the enum com.stablemoney.api.business.faq.FaqType.
 */
export const FaqTypeSchema: GenEnum<FaqType> = /*@__PURE__*/
  enumDesc(file_Faq, 0);

/**
 * @generated from enum com.stablemoney.api.business.faq.BusinessUnit
 */
export enum BusinessUnit {
  /**
   * @generated from enum value: UNKNOWN_BUSINESS_UNIT = 0;
   */
  UNKNOWN_BUSINESS_UNIT = 0,

  /**
   * @generated from enum value: ALPHA = 1;
   */
  ALPHA = 1,

  /**
   * @generated from enum value: FINSERV = 2;
   */
  FINSERV = 2,

  /**
   * @generated from enum value: BROKING = 3;
   */
  BROKING = 3,
}

/**
 * Describes the enum com.stablemoney.api.business.faq.BusinessUnit.
 */
export const BusinessUnitSchema: GenEnum<BusinessUnit> = /*@__PURE__*/
  enumDesc(file_Faq, 1);

/**
 * @generated from enum com.stablemoney.api.business.faq.AuthorType
 */
export enum AuthorType {
  /**
   * @generated from enum value: END_USER = 0;
   */
  END_USER = 0,

  /**
   * @generated from enum value: AGENT = 1;
   */
  AGENT = 1,
}

/**
 * Describes the enum com.stablemoney.api.business.faq.AuthorType.
 */
export const AuthorTypeSchema: GenEnum<AuthorType> = /*@__PURE__*/
  enumDesc(file_Faq, 2);

