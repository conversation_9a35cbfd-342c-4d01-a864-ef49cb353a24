// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file campaign.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file campaign.proto.
 */
export const file_campaign: GenFile = /*@__PURE__*/
  fileDesc("Cg5jYW1wYWlnbi5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSJYChdPbmJvYXJkaW5nU3VibWl0UmVxdWVzdBI9CgdhbnN3ZXJzGAEgAygLMiwuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Vc2VyUXVpekFuc3dlciJfCgxTdGVwUmVzcG9uc2USNQoJbmV4dF9zdGVwGAEgASgOMiIuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5TdGVwEhgKEGlzX3BvbGxfZWxpZ2libGUYAiABKAgiWQoYT25ib2FyZGluZ1N1Ym1pdFJlc3BvbnNlEj0KCW5leHRfc3RlcBgBIAEoCzIqLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuU3RlcFJlc3BvbnNlIlMKElF1aXpTdGF0dXNSZXNwb25zZRI9CgluZXh0X3N0ZXAYASABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlN0ZXBSZXNwb25zZSJYCgxRdWl6UmVzcG9uc2USSAoPcXVlc3Rpb25fZ3JvdXBzGAEgAygLMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5RdWl6UXVlc3Rpb25Hcm91cCLYAQoRUXVpelF1ZXN0aW9uR3JvdXASEAoIZ3JvdXBfaWQYASABKAkSTwoIcXVlc3Rpb24YAiADKAsyPS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlF1aXpRdWVzdGlvbkdyb3VwLlF1ZXN0aW9uRW50cnkaYAoNUXVlc3Rpb25FbnRyeRILCgNrZXkYASABKAUSPgoFdmFsdWUYAiABKAsyLy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkxvY2FsaXplZFF1ZXN0aW9uOgI4ASLNAQoRTG9jYWxpemVkUXVlc3Rpb24SCgoCaWQYASABKAkSDAoEdGV4dBgCIAEoCRIZChFjb3JyZWN0X29wdGlvbl9pZBgDIAEoCRI5CgdvcHRpb25zGAQgAygLMiguY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5RdWl6T3B0aW9uEiIKGmNvcnJlY3Rfb3B0aW9uX2V4cGxhbmF0aW9uGAUgASgJEiQKHGluY29ycmVjdF9vcHRpb25fZXhwbGFuYXRpb24YBiABKAkiJgoKUXVpek9wdGlvbhIKCgJpZBgBIAEoCRIMCgR0ZXh0GAIgASgJIjUKGEdldENvcnJlY3RPcHRpb25SZXNwb25zZRIZChFjb3JyZWN0X29wdGlvbl9pZBgBIAEoCSJfCg5Vc2VyUXVpekFuc3dlchITCgtxdWVzdGlvbl9pZBgBIAEoCRIaChJzZWxlY3RlZF9vcHRpb25faWQYAiABKAkSHAoUc2VsZWN0ZWRfb3B0aW9uX3RleHQYAyABKAkiUgoRU3VibWl0UXVpelJlcXVlc3QSPQoHYW5zd2VycxgBIAMoCzIsLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuVXNlclF1aXpBbnN3ZXIiYgoSU3VibWl0UXVpelJlc3BvbnNlEg0KBXNjb3JlGAEgASgFEj0KCW5leHRfc3RlcBgCIAEoCzIqLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuU3RlcFJlc3BvbnNlKjkKDExhbmd1YWdlQ29kZRIZChVVTktOT1dOX0xBTkdVQUdFX0NPREUQABIGCgJFThABEgYKAkhJEAIqPgoEU3RlcBISCg5VTktOT1dOX1NUQVRVUxAAEg4KCk9OQk9BUkRJTkcQARIICgRRVUlaEAISCAoEUE9MTBADQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.identity.OnboardingSubmitRequest
 */
export type OnboardingSubmitRequest = Message<"com.stablemoney.api.identity.OnboardingSubmitRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.UserQuizAnswer answers = 1;
   */
  answers: UserQuizAnswer[];
};

/**
 * Describes the message com.stablemoney.api.identity.OnboardingSubmitRequest.
 * Use `create(OnboardingSubmitRequestSchema)` to create a new message.
 */
export const OnboardingSubmitRequestSchema: GenMessage<OnboardingSubmitRequest> = /*@__PURE__*/
  messageDesc(file_campaign, 0);

/**
 * @generated from message com.stablemoney.api.identity.StepResponse
 */
export type StepResponse = Message<"com.stablemoney.api.identity.StepResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.Step next_step = 1;
   */
  nextStep: Step;

  /**
   * @generated from field: bool is_poll_eligible = 2;
   */
  isPollEligible: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.StepResponse.
 * Use `create(StepResponseSchema)` to create a new message.
 */
export const StepResponseSchema: GenMessage<StepResponse> = /*@__PURE__*/
  messageDesc(file_campaign, 1);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingSubmitResponse
 */
export type OnboardingSubmitResponse = Message<"com.stablemoney.api.identity.OnboardingSubmitResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.StepResponse next_step = 1;
   */
  nextStep?: StepResponse;
};

/**
 * Describes the message com.stablemoney.api.identity.OnboardingSubmitResponse.
 * Use `create(OnboardingSubmitResponseSchema)` to create a new message.
 */
export const OnboardingSubmitResponseSchema: GenMessage<OnboardingSubmitResponse> = /*@__PURE__*/
  messageDesc(file_campaign, 2);

/**
 * @generated from message com.stablemoney.api.identity.QuizStatusResponse
 */
export type QuizStatusResponse = Message<"com.stablemoney.api.identity.QuizStatusResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.StepResponse next_step = 1;
   */
  nextStep?: StepResponse;
};

/**
 * Describes the message com.stablemoney.api.identity.QuizStatusResponse.
 * Use `create(QuizStatusResponseSchema)` to create a new message.
 */
export const QuizStatusResponseSchema: GenMessage<QuizStatusResponse> = /*@__PURE__*/
  messageDesc(file_campaign, 3);

/**
 * @generated from message com.stablemoney.api.identity.QuizResponse
 */
export type QuizResponse = Message<"com.stablemoney.api.identity.QuizResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.QuizQuestionGroup question_groups = 1;
   */
  questionGroups: QuizQuestionGroup[];
};

/**
 * Describes the message com.stablemoney.api.identity.QuizResponse.
 * Use `create(QuizResponseSchema)` to create a new message.
 */
export const QuizResponseSchema: GenMessage<QuizResponse> = /*@__PURE__*/
  messageDesc(file_campaign, 4);

/**
 * @generated from message com.stablemoney.api.identity.QuizQuestionGroup
 */
export type QuizQuestionGroup = Message<"com.stablemoney.api.identity.QuizQuestionGroup"> & {
  /**
   * @generated from field: string group_id = 1;
   */
  groupId: string;

  /**
   * @generated from field: map<int32, com.stablemoney.api.identity.LocalizedQuestion> question = 2;
   */
  question: { [key: number]: LocalizedQuestion };
};

/**
 * Describes the message com.stablemoney.api.identity.QuizQuestionGroup.
 * Use `create(QuizQuestionGroupSchema)` to create a new message.
 */
export const QuizQuestionGroupSchema: GenMessage<QuizQuestionGroup> = /*@__PURE__*/
  messageDesc(file_campaign, 5);

/**
 * @generated from message com.stablemoney.api.identity.LocalizedQuestion
 */
export type LocalizedQuestion = Message<"com.stablemoney.api.identity.LocalizedQuestion"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string text = 2;
   */
  text: string;

  /**
   * @generated from field: string correct_option_id = 3;
   */
  correctOptionId: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.QuizOption options = 4;
   */
  options: QuizOption[];

  /**
   * @generated from field: string correct_option_explanation = 5;
   */
  correctOptionExplanation: string;

  /**
   * @generated from field: string incorrect_option_explanation = 6;
   */
  incorrectOptionExplanation: string;
};

/**
 * Describes the message com.stablemoney.api.identity.LocalizedQuestion.
 * Use `create(LocalizedQuestionSchema)` to create a new message.
 */
export const LocalizedQuestionSchema: GenMessage<LocalizedQuestion> = /*@__PURE__*/
  messageDesc(file_campaign, 6);

/**
 * @generated from message com.stablemoney.api.identity.QuizOption
 */
export type QuizOption = Message<"com.stablemoney.api.identity.QuizOption"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string text = 2;
   */
  text: string;
};

/**
 * Describes the message com.stablemoney.api.identity.QuizOption.
 * Use `create(QuizOptionSchema)` to create a new message.
 */
export const QuizOptionSchema: GenMessage<QuizOption> = /*@__PURE__*/
  messageDesc(file_campaign, 7);

/**
 * @generated from message com.stablemoney.api.identity.GetCorrectOptionResponse
 */
export type GetCorrectOptionResponse = Message<"com.stablemoney.api.identity.GetCorrectOptionResponse"> & {
  /**
   * @generated from field: string correct_option_id = 1;
   */
  correctOptionId: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GetCorrectOptionResponse.
 * Use `create(GetCorrectOptionResponseSchema)` to create a new message.
 */
export const GetCorrectOptionResponseSchema: GenMessage<GetCorrectOptionResponse> = /*@__PURE__*/
  messageDesc(file_campaign, 8);

/**
 * @generated from message com.stablemoney.api.identity.UserQuizAnswer
 */
export type UserQuizAnswer = Message<"com.stablemoney.api.identity.UserQuizAnswer"> & {
  /**
   * @generated from field: string question_id = 1;
   */
  questionId: string;

  /**
   * @generated from field: string selected_option_id = 2;
   */
  selectedOptionId: string;

  /**
   * @generated from field: string selected_option_text = 3;
   */
  selectedOptionText: string;
};

/**
 * Describes the message com.stablemoney.api.identity.UserQuizAnswer.
 * Use `create(UserQuizAnswerSchema)` to create a new message.
 */
export const UserQuizAnswerSchema: GenMessage<UserQuizAnswer> = /*@__PURE__*/
  messageDesc(file_campaign, 9);

/**
 * @generated from message com.stablemoney.api.identity.SubmitQuizRequest
 */
export type SubmitQuizRequest = Message<"com.stablemoney.api.identity.SubmitQuizRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.UserQuizAnswer answers = 1;
   */
  answers: UserQuizAnswer[];
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitQuizRequest.
 * Use `create(SubmitQuizRequestSchema)` to create a new message.
 */
export const SubmitQuizRequestSchema: GenMessage<SubmitQuizRequest> = /*@__PURE__*/
  messageDesc(file_campaign, 10);

/**
 * @generated from message com.stablemoney.api.identity.SubmitQuizResponse
 */
export type SubmitQuizResponse = Message<"com.stablemoney.api.identity.SubmitQuizResponse"> & {
  /**
   * @generated from field: int32 score = 1;
   */
  score: number;

  /**
   * @generated from field: com.stablemoney.api.identity.StepResponse next_step = 2;
   */
  nextStep?: StepResponse;
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitQuizResponse.
 * Use `create(SubmitQuizResponseSchema)` to create a new message.
 */
export const SubmitQuizResponseSchema: GenMessage<SubmitQuizResponse> = /*@__PURE__*/
  messageDesc(file_campaign, 11);

/**
 * @generated from enum com.stablemoney.api.identity.LanguageCode
 */
export enum LanguageCode {
  /**
   * @generated from enum value: UNKNOWN_LANGUAGE_CODE = 0;
   */
  UNKNOWN_LANGUAGE_CODE = 0,

  /**
   * @generated from enum value: EN = 1;
   */
  EN = 1,

  /**
   * @generated from enum value: HI = 2;
   */
  HI = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.LanguageCode.
 */
export const LanguageCodeSchema: GenEnum<LanguageCode> = /*@__PURE__*/
  enumDesc(file_campaign, 0);

/**
 * @generated from enum com.stablemoney.api.identity.Step
 */
export enum Step {
  /**
   * @generated from enum value: UNKNOWN_STATUS = 0;
   */
  UNKNOWN_STATUS = 0,

  /**
   * @generated from enum value: ONBOARDING = 1;
   */
  ONBOARDING = 1,

  /**
   * @generated from enum value: QUIZ = 2;
   */
  QUIZ = 2,

  /**
   * @generated from enum value: POLL = 3;
   */
  POLL = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.Step.
 */
export const StepSchema: GenEnum<Step> = /*@__PURE__*/
  enumDesc(file_campaign, 1);

