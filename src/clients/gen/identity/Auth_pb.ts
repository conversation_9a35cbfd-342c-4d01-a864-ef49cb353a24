// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Auth.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { UserDevice } from "./Device_pb.js";
import { file_Device } from "./Device_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Auth.proto.
 */
export const file_Auth: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Device]);

/**
 * @generated from message com.stablemoney.api.identity.InitiateAuthRequest
 */
export type InitiateAuthRequest = Message<"com.stablemoney.api.identity.InitiateAuthRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.AuthType auth_type = 1;
   */
  authType: AuthType;

  /**
   * @generated from field: com.stablemoney.api.identity.UserDevice user_device = 2;
   */
  userDevice?: UserDevice;

  /**
   * @generated from oneof com.stablemoney.api.identity.InitiateAuthRequest.result
   */
  result: {
    /**
     * @generated from field: string email = 3;
     */
    value: string;
    case: "email";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.AppleLoginRequest apple_login_request = 4;
     */
    value: AppleLoginRequest;
    case: "appleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.GoogleLoginRequest google_login_request = 5;
     */
    value: GoogleLoginRequest;
    case: "googleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.MobileLoginRequest mobile_login_request = 6;
     */
    value: MobileLoginRequest;
    case: "mobileLoginRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateAuthRequest.
 * Use `create(InitiateAuthRequestSchema)` to create a new message.
 */
export const InitiateAuthRequestSchema: GenMessage<InitiateAuthRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 0);

/**
 * @generated from message com.stablemoney.api.identity.AppleLoginRequest
 */
export type AppleLoginRequest = Message<"com.stablemoney.api.identity.AppleLoginRequest"> & {
  /**
   * @generated from field: string authorisation_code = 1;
   */
  authorisationCode: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AppleLoginRequest.
 * Use `create(AppleLoginRequestSchema)` to create a new message.
 */
export const AppleLoginRequestSchema: GenMessage<AppleLoginRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 1);

/**
 * @generated from message com.stablemoney.api.identity.GoogleLoginRequest
 */
export type GoogleLoginRequest = Message<"com.stablemoney.api.identity.GoogleLoginRequest"> & {
  /**
   * @generated from field: string id_token = 1;
   */
  idToken: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GoogleLoginRequest.
 * Use `create(GoogleLoginRequestSchema)` to create a new message.
 */
export const GoogleLoginRequestSchema: GenMessage<GoogleLoginRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 2);

/**
 * @generated from message com.stablemoney.api.identity.InitiateAuthResponse
 */
export type InitiateAuthResponse = Message<"com.stablemoney.api.identity.InitiateAuthResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from oneof com.stablemoney.api.identity.InitiateAuthResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.OTPChallenge otp_challenge = 2;
     */
    value: OTPChallenge;
    case: "otpChallenge";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.AuthenticationResult authentication_result = 3;
     */
    value: AuthenticationResult;
    case: "authenticationResult";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateAuthResponse.
 * Use `create(InitiateAuthResponseSchema)` to create a new message.
 */
export const InitiateAuthResponseSchema: GenMessage<InitiateAuthResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 3);

/**
 * @generated from message com.stablemoney.api.identity.AuthenticationResult
 */
export type AuthenticationResult = Message<"com.stablemoney.api.identity.AuthenticationResult"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * @generated from field: string refresh_token = 2;
   */
  refreshToken: string;

  /**
   * @generated from field: string token_type = 3;
   */
  tokenType: string;

  /**
   * @generated from field: string expires_in = 4;
   */
  expiresIn: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AuthenticationResult.
 * Use `create(AuthenticationResultSchema)` to create a new message.
 */
export const AuthenticationResultSchema: GenMessage<AuthenticationResult> = /*@__PURE__*/
  messageDesc(file_Auth, 4);

/**
 * @generated from message com.stablemoney.api.identity.RespondToAuthChallengeResponse
 */
export type RespondToAuthChallengeResponse = Message<"com.stablemoney.api.identity.RespondToAuthChallengeResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: com.stablemoney.api.identity.AuthenticationResult authentication_result = 2;
   */
  authenticationResult?: AuthenticationResult;
};

/**
 * Describes the message com.stablemoney.api.identity.RespondToAuthChallengeResponse.
 * Use `create(RespondToAuthChallengeResponseSchema)` to create a new message.
 */
export const RespondToAuthChallengeResponseSchema: GenMessage<RespondToAuthChallengeResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 5);

/**
 * @generated from message com.stablemoney.api.identity.OTPChallenge
 */
export type OTPChallenge = Message<"com.stablemoney.api.identity.OTPChallenge"> & {
  /**
   * @generated from field: string challenge_id = 1;
   */
  challengeId: string;

  /**
   * @generated from field: int64 expiry = 2;
   */
  expiry: bigint;
};

/**
 * Describes the message com.stablemoney.api.identity.OTPChallenge.
 * Use `create(OTPChallengeSchema)` to create a new message.
 */
export const OTPChallengeSchema: GenMessage<OTPChallenge> = /*@__PURE__*/
  messageDesc(file_Auth, 6);

/**
 * @generated from message com.stablemoney.api.identity.RespondToAuthChallengeRequest
 */
export type RespondToAuthChallengeRequest = Message<"com.stablemoney.api.identity.RespondToAuthChallengeRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string challenge_id = 2;
   */
  challengeId: string;

  /**
   * @generated from field: string answer = 3;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.identity.RespondToAuthChallengeRequest.
 * Use `create(RespondToAuthChallengeRequestSchema)` to create a new message.
 */
export const RespondToAuthChallengeRequestSchema: GenMessage<RespondToAuthChallengeRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 7);

/**
 * @generated from message com.stablemoney.api.identity.RefreshTokenRequest
 */
export type RefreshTokenRequest = Message<"com.stablemoney.api.identity.RefreshTokenRequest"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;
};

/**
 * Describes the message com.stablemoney.api.identity.RefreshTokenRequest.
 * Use `create(RefreshTokenRequestSchema)` to create a new message.
 */
export const RefreshTokenRequestSchema: GenMessage<RefreshTokenRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 8);

/**
 * @generated from message com.stablemoney.api.identity.RefreshTokenResponse
 */
export type RefreshTokenResponse = Message<"com.stablemoney.api.identity.RefreshTokenResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: com.stablemoney.api.identity.AuthenticationResult authentication_result = 2;
   */
  authenticationResult?: AuthenticationResult;
};

/**
 * Describes the message com.stablemoney.api.identity.RefreshTokenResponse.
 * Use `create(RefreshTokenResponseSchema)` to create a new message.
 */
export const RefreshTokenResponseSchema: GenMessage<RefreshTokenResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 9);

/**
 * @generated from message com.stablemoney.api.identity.MobileLoginRequest
 */
export type MobileLoginRequest = Message<"com.stablemoney.api.identity.MobileLoginRequest"> & {
  /**
   * @generated from field: string mobile = 1;
   */
  mobile: string;

  /**
   * @generated from field: string country_code = 2;
   */
  countryCode: string;

  /**
   * @generated from field: string encrypted_mobile = 3;
   */
  encryptedMobile: string;

  /**
   * @generated from field: string encryption_key = 4;
   */
  encryptionKey: string;
};

/**
 * Describes the message com.stablemoney.api.identity.MobileLoginRequest.
 * Use `create(MobileLoginRequestSchema)` to create a new message.
 */
export const MobileLoginRequestSchema: GenMessage<MobileLoginRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 10);

/**
 * @generated from message com.stablemoney.api.identity.InitiateVerifyRequest
 */
export type InitiateVerifyRequest = Message<"com.stablemoney.api.identity.InitiateVerifyRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.AuthType auth_type = 1;
   */
  authType: AuthType;

  /**
   * @generated from oneof com.stablemoney.api.identity.InitiateVerifyRequest.result
   */
  result: {
    /**
     * @generated from field: string email = 2;
     */
    value: string;
    case: "email";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.AppleLoginRequest apple_login_request = 3;
     */
    value: AppleLoginRequest;
    case: "appleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.GoogleLoginRequest google_login_request = 4;
     */
    value: GoogleLoginRequest;
    case: "googleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.MobileLoginRequest mobile_login_request = 5;
     */
    value: MobileLoginRequest;
    case: "mobileLoginRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateVerifyRequest.
 * Use `create(InitiateVerifyRequestSchema)` to create a new message.
 */
export const InitiateVerifyRequestSchema: GenMessage<InitiateVerifyRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 11);

/**
 * @generated from message com.stablemoney.api.identity.InitiateVerifyResponse
 */
export type InitiateVerifyResponse = Message<"com.stablemoney.api.identity.InitiateVerifyResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.identity.InitiateVerifyResponse.result
   */
  result: {
    /**
     * @generated from field: bool is_email_verified = 1;
     */
    value: boolean;
    case: "isEmailVerified";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.OTPChallenge otp_challenge = 2;
     */
    value: OTPChallenge;
    case: "otpChallenge";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateVerifyResponse.
 * Use `create(InitiateVerifyResponseSchema)` to create a new message.
 */
export const InitiateVerifyResponseSchema: GenMessage<InitiateVerifyResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 12);

/**
 * @generated from message com.stablemoney.api.identity.RespondToVerifyChallengeRequest
 */
export type RespondToVerifyChallengeRequest = Message<"com.stablemoney.api.identity.RespondToVerifyChallengeRequest"> & {
  /**
   * @generated from field: string challenge_id = 1;
   */
  challengeId: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.identity.RespondToVerifyChallengeRequest.
 * Use `create(RespondToVerifyChallengeRequestSchema)` to create a new message.
 */
export const RespondToVerifyChallengeRequestSchema: GenMessage<RespondToVerifyChallengeRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 13);

/**
 * @generated from message com.stablemoney.api.identity.RespondToVerifyChallengeResponse
 */
export type RespondToVerifyChallengeResponse = Message<"com.stablemoney.api.identity.RespondToVerifyChallengeResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.RespondToVerifyChallengeResponse.
 * Use `create(RespondToVerifyChallengeResponseSchema)` to create a new message.
 */
export const RespondToVerifyChallengeResponseSchema: GenMessage<RespondToVerifyChallengeResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 14);

/**
 * @generated from enum com.stablemoney.api.identity.AuthType
 */
export enum AuthType {
  /**
   * @generated from enum value: LOGIN_TYPE_UNKNOWN = 0;
   */
  LOGIN_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: EMAIL_OTP = 1;
   */
  EMAIL_OTP = 1,

  /**
   * @generated from enum value: GOOGLE = 2;
   */
  GOOGLE = 2,

  /**
   * @generated from enum value: APPLE = 3;
   */
  APPLE = 3,

  /**
   * @generated from enum value: MOBILE_OTP = 4;
   */
  MOBILE_OTP = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.AuthType.
 */
export const AuthTypeSchema: GenEnum<AuthType> = /*@__PURE__*/
  enumDesc(file_Auth, 0);

/**
 * @generated from enum com.stablemoney.api.identity.AuthProcess
 */
export enum AuthProcess {
  /**
   * @generated from enum value: AUTH_PROCESS_UNKNOWN = 0;
   */
  AUTH_PROCESS_UNKNOWN = 0,

  /**
   * @generated from enum value: LOGIN = 1;
   */
  LOGIN = 1,

  /**
   * @generated from enum value: VERIFY = 2;
   */
  VERIFY = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.AuthProcess.
 */
export const AuthProcessSchema: GenEnum<AuthProcess> = /*@__PURE__*/
  enumDesc(file_Auth, 1);

