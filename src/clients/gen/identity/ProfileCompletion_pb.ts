// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file ProfileCompletion.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Gender, MaritalStatus } from "./Profile_pb.js";
import { file_Profile } from "./Profile_pb.js";
import type { CityResponse } from "./City_pb.js";
import { file_City } from "./City_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file ProfileCompletion.proto.
 */
export const file_ProfileCompletion: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Profile, file_City]);

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionStepsResponse
 */
export type ProfileCompletionStepsResponse = Message<"com.stablemoney.api.identity.ProfileCompletionStepsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.ProfileCompletionStep steps = 1;
   */
  steps: ProfileCompletionStep[];

  /**
   * @generated from field: string next_step = 2;
   */
  nextStep: string;

  /**
   * @generated from field: bool is_completed = 3;
   */
  isCompleted: boolean;

  /**
   * @generated from field: double completion_percentage = 4;
   */
  completionPercentage: number;
};

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionStepsResponse.
 * Use `create(ProfileCompletionStepsResponseSchema)` to create a new message.
 */
export const ProfileCompletionStepsResponseSchema: GenMessage<ProfileCompletionStepsResponse> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 0);

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionStep
 */
export type ProfileCompletionStep = Message<"com.stablemoney.api.identity.ProfileCompletionStep"> & {
  /**
   * @generated from field: string step_name = 1;
   */
  stepName: string;

  /**
   * @generated from field: bool is_completed = 2;
   */
  isCompleted: boolean;

  /**
   * @generated from field: int32 priority = 3;
   */
  priority: number;
};

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionStep.
 * Use `create(ProfileCompletionStepSchema)` to create a new message.
 */
export const ProfileCompletionStepSchema: GenMessage<ProfileCompletionStep> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 1);

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionRequest
 */
export type ProfileCompletionRequest = Message<"com.stablemoney.api.identity.ProfileCompletionRequest"> & {
  /**
   * @generated from field: string step_name = 1;
   */
  stepName: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.PersonalDetails personal_details = 2;
   */
  personalDetails?: PersonalDetails;

  /**
   * @generated from field: optional string dob = 3;
   */
  dob?: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.DemographicDetails demographic_details = 4;
   */
  demographicDetails?: DemographicDetails;

  /**
   * @generated from field: optional com.stablemoney.api.identity.EmploymentDetails employment_details = 5;
   */
  employmentDetails?: EmploymentDetails;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.UserBank user_bank = 6;
   */
  userBank: UserBank[];

  /**
   * @generated from field: optional com.stablemoney.api.identity.Location location = 7;
   */
  location?: Location;
};

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionRequest.
 * Use `create(ProfileCompletionRequestSchema)` to create a new message.
 */
export const ProfileCompletionRequestSchema: GenMessage<ProfileCompletionRequest> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 2);

/**
 * @generated from message com.stablemoney.api.identity.Location
 */
export type Location = Message<"com.stablemoney.api.identity.Location"> & {
  /**
   * @generated from field: double latitude = 1;
   */
  latitude: number;

  /**
   * @generated from field: double longitude = 2;
   */
  longitude: number;
};

/**
 * Describes the message com.stablemoney.api.identity.Location.
 * Use `create(LocationSchema)` to create a new message.
 */
export const LocationSchema: GenMessage<Location> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 3);

/**
 * @generated from message com.stablemoney.api.identity.UserBank
 */
export type UserBank = Message<"com.stablemoney.api.identity.UserBank"> & {
  /**
   * @generated from field: string bank_id = 1;
   */
  bankId: string;

  /**
   * @generated from field: bool has_fd_with_bank = 2;
   */
  hasFdWithBank: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.UserBank.
 * Use `create(UserBankSchema)` to create a new message.
 */
export const UserBankSchema: GenMessage<UserBank> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 4);

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionResponse
 */
export type ProfileCompletionResponse = Message<"com.stablemoney.api.identity.ProfileCompletionResponse"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string image_url = 2;
   */
  imageUrl: string;

  /**
   * @generated from field: string image_type = 3;
   */
  imageType: string;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: double completion_percentage = 5;
   */
  completionPercentage: number;

  /**
   * @generated from field: string button_text = 6;
   */
  buttonText: string;

  /**
   * @generated from field: string next_step = 7;
   */
  nextStep: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionResponse.
 * Use `create(ProfileCompletionResponseSchema)` to create a new message.
 */
export const ProfileCompletionResponseSchema: GenMessage<ProfileCompletionResponse> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 5);

/**
 * @generated from message com.stablemoney.api.identity.PersonalDetails
 */
export type PersonalDetails = Message<"com.stablemoney.api.identity.PersonalDetails"> & {
  /**
   * @generated from field: string first_name = 1;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 2;
   */
  lastName: string;

  /**
   * @generated from field: optional string dob = 3;
   */
  dob?: string;
};

/**
 * Describes the message com.stablemoney.api.identity.PersonalDetails.
 * Use `create(PersonalDetailsSchema)` to create a new message.
 */
export const PersonalDetailsSchema: GenMessage<PersonalDetails> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 6);

/**
 * @generated from message com.stablemoney.api.identity.DemographicDetails
 */
export type DemographicDetails = Message<"com.stablemoney.api.identity.DemographicDetails"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.Gender gender = 1;
   */
  gender: Gender;

  /**
   * @generated from field: com.stablemoney.api.identity.MaritalStatus marital_status = 2;
   */
  maritalStatus: MaritalStatus;

  /**
   * @generated from field: string city_id = 3;
   */
  cityId: string;
};

/**
 * Describes the message com.stablemoney.api.identity.DemographicDetails.
 * Use `create(DemographicDetailsSchema)` to create a new message.
 */
export const DemographicDetailsSchema: GenMessage<DemographicDetails> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 7);

/**
 * @generated from message com.stablemoney.api.identity.EmploymentDetails
 */
export type EmploymentDetails = Message<"com.stablemoney.api.identity.EmploymentDetails"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.ProfileCompletionEmploymentType employment_type = 1;
   */
  employmentType: ProfileCompletionEmploymentType;

  /**
   * @generated from field: optional com.stablemoney.api.identity.ProfileCompletionMonthlyIncomeRange monthly_income = 2;
   */
  monthlyIncome?: ProfileCompletionMonthlyIncomeRange;

  /**
   * @generated from field: optional com.stablemoney.api.identity.ProfileCompletionAnnualIncomeRange annual_income = 3;
   */
  annualIncome?: ProfileCompletionAnnualIncomeRange;
};

/**
 * Describes the message com.stablemoney.api.identity.EmploymentDetails.
 * Use `create(EmploymentDetailsSchema)` to create a new message.
 */
export const EmploymentDetailsSchema: GenMessage<EmploymentDetails> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 8);

/**
 * @generated from message com.stablemoney.api.identity.GetProfileResponse
 */
export type GetProfileResponse = Message<"com.stablemoney.api.identity.GetProfileResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.PersonalDetails personal_details = 1;
   */
  personalDetails?: PersonalDetails;

  /**
   * @generated from field: string dob = 2;
   */
  dob: string;

  /**
   * @generated from field: com.stablemoney.api.identity.DemographicDetailsResponse demographic_details = 3;
   */
  demographicDetails?: DemographicDetailsResponse;

  /**
   * @generated from field: com.stablemoney.api.identity.EmploymentDetails employment_details = 4;
   */
  employmentDetails?: EmploymentDetails;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.Bank banks = 5;
   */
  banks: Bank[];

  /**
   * @generated from field: string background_image_url = 6;
   */
  backgroundImageUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GetProfileResponse.
 * Use `create(GetProfileResponseSchema)` to create a new message.
 */
export const GetProfileResponseSchema: GenMessage<GetProfileResponse> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 9);

/**
 * @generated from message com.stablemoney.api.identity.DemographicDetailsResponse
 */
export type DemographicDetailsResponse = Message<"com.stablemoney.api.identity.DemographicDetailsResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.Gender gender = 1;
   */
  gender: Gender;

  /**
   * @generated from field: com.stablemoney.api.identity.MaritalStatus marital_status = 2;
   */
  maritalStatus: MaritalStatus;

  /**
   * @generated from field: com.stablemoney.api.location.city.CityResponse city = 3;
   */
  city?: CityResponse;
};

/**
 * Describes the message com.stablemoney.api.identity.DemographicDetailsResponse.
 * Use `create(DemographicDetailsResponseSchema)` to create a new message.
 */
export const DemographicDetailsResponseSchema: GenMessage<DemographicDetailsResponse> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 10);

/**
 * @generated from message com.stablemoney.api.identity.Bank
 */
export type Bank = Message<"com.stablemoney.api.identity.Bank"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string id = 2;
   */
  id: string;

  /**
   * @generated from field: string icon_url = 3;
   */
  iconUrl: string;

  /**
   * @generated from field: bool has_fd_with_bank = 4;
   */
  hasFdWithBank: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.Bank.
 * Use `create(BankSchema)` to create a new message.
 */
export const BankSchema: GenMessage<Bank> = /*@__PURE__*/
  messageDesc(file_ProfileCompletion, 11);

/**
 * @generated from enum com.stablemoney.api.identity.ProfileCompletionAnnualIncomeRange
 */
export enum ProfileCompletionAnnualIncomeRange {
  /**
   * @generated from enum value: UNKNOWN_PROFILE_COMPLETION_ANNUAL_INCOME_RANGE = 0;
   */
  UNKNOWN_PROFILE_COMPLETION_ANNUAL_INCOME_RANGE = 0,

  /**
   * @generated from enum value: LESS_THAN_1L_INCOME_RANGE = 1;
   */
  LESS_THAN_1L_INCOME_RANGE = 1,

  /**
   * @generated from enum value: BETWEEN_1L_AND_5L_INCOME_RANGE = 2;
   */
  BETWEEN_1L_AND_5L_INCOME_RANGE = 2,

  /**
   * @generated from enum value: BETWEEN_5L_AND_15L_INCOME_RANGE = 3;
   */
  BETWEEN_5L_AND_15L_INCOME_RANGE = 3,

  /**
   * @generated from enum value: BETWEEN_15L_AND_50L_INCOME_RANGE = 4;
   */
  BETWEEN_15L_AND_50L_INCOME_RANGE = 4,

  /**
   * @generated from enum value: MORE_THAN_50L_INCOME_RANGE = 5;
   */
  MORE_THAN_50L_INCOME_RANGE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.ProfileCompletionAnnualIncomeRange.
 */
export const ProfileCompletionAnnualIncomeRangeSchema: GenEnum<ProfileCompletionAnnualIncomeRange> = /*@__PURE__*/
  enumDesc(file_ProfileCompletion, 0);

/**
 * @generated from enum com.stablemoney.api.identity.ProfileCompletionMonthlyIncomeRange
 */
export enum ProfileCompletionMonthlyIncomeRange {
  /**
   * @generated from enum value: UNKNOWN_PROFILE_COMPLETION_MONTHLY_INCOME_RANGE = 0;
   */
  UNKNOWN_PROFILE_COMPLETION_MONTHLY_INCOME_RANGE = 0,

  /**
   * @generated from enum value: LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE = 1;
   */
  LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE = 1,

  /**
   * @generated from enum value: BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE = 2;
   */
  BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE = 2,

  /**
   * @generated from enum value: BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE = 3;
   */
  BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE = 3,

  /**
   * @generated from enum value: BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE = 4;
   */
  BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE = 4,

  /**
   * @generated from enum value: BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE = 5;
   */
  BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE = 5,

  /**
   * @generated from enum value: ABOVE_5L_PROFILE_COMPLETION_INCOME = 6;
   */
  ABOVE_5L_PROFILE_COMPLETION_INCOME = 6,
}

/**
 * Describes the enum com.stablemoney.api.identity.ProfileCompletionMonthlyIncomeRange.
 */
export const ProfileCompletionMonthlyIncomeRangeSchema: GenEnum<ProfileCompletionMonthlyIncomeRange> = /*@__PURE__*/
  enumDesc(file_ProfileCompletion, 1);

/**
 * @generated from enum com.stablemoney.api.identity.ProfileCompletionEmploymentType
 */
export enum ProfileCompletionEmploymentType {
  /**
   * @generated from enum value: UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 0;
   */
  UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 0,

  /**
   * @generated from enum value: SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 1;
   */
  SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 1,

  /**
   * @generated from enum value: SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 2;
   */
  SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 2,

  /**
   * @generated from enum value: RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 3;
   */
  RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 3,

  /**
   * @generated from enum value: HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 4;
   */
  HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 4,

  /**
   * @generated from enum value: OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 5;
   */
  OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.ProfileCompletionEmploymentType.
 */
export const ProfileCompletionEmploymentTypeSchema: GenEnum<ProfileCompletionEmploymentType> = /*@__PURE__*/
  enumDesc(file_ProfileCompletion, 2);

