// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file MobileVerification.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { OTPChallenge } from "./Auth_pb.js";
import { file_Auth } from "./Auth_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file MobileVerification.proto.
 */
export const file_MobileVerification: GenFile = /*@__PURE__*/
  fileDesc("ChhNb2JpbGVWZXJpZmljYXRpb24ucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiSQohSW5pdGlhdGVNb2JpbGVWZXJpZmljYXRpb25SZXF1ZXN0Eg4KBm1vYmlsZRgBIAEoCRIUCgxjb3VudHJ5X2NvZGUYAiABKAkiZwoiSW5pdGlhdGVNb2JpbGVWZXJpZmljYXRpb25SZXNwb25zZRJBCg1vdHBfY2hhbGxlbmdlGAEgASgLMiouY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5PVFBDaGFsbGVuZ2UiTAokUmVzcG9uZFRvTW9iaWxlVmVyaWZpY2F0aW9uQ2hhbGxlbmdlEhQKDGNoYWxsZW5nZV9pZBgBIAEoCRIOCgZhbnN3ZXIYAiABKAkiUAosUmVzcG9uZFRvTW9iaWxlVmVyaWZpY2F0aW9uQ2hhbGxlbmdlUmVzcG9uc2USDwoHZXhwaXJlZBgBIAEoCBIPCgdtZXNzYWdlGAIgASgJQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z", [file_Auth]);

/**
 * @generated from message com.stablemoney.api.identity.InitiateMobileVerificationRequest
 */
export type InitiateMobileVerificationRequest = Message<"com.stablemoney.api.identity.InitiateMobileVerificationRequest"> & {
  /**
   * @generated from field: string mobile = 1;
   */
  mobile: string;

  /**
   * @generated from field: string country_code = 2;
   */
  countryCode: string;
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateMobileVerificationRequest.
 * Use `create(InitiateMobileVerificationRequestSchema)` to create a new message.
 */
export const InitiateMobileVerificationRequestSchema: GenMessage<InitiateMobileVerificationRequest> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 0);

/**
 * @generated from message com.stablemoney.api.identity.InitiateMobileVerificationResponse
 */
export type InitiateMobileVerificationResponse = Message<"com.stablemoney.api.identity.InitiateMobileVerificationResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.OTPChallenge otp_challenge = 1;
   */
  otpChallenge?: OTPChallenge;
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateMobileVerificationResponse.
 * Use `create(InitiateMobileVerificationResponseSchema)` to create a new message.
 */
export const InitiateMobileVerificationResponseSchema: GenMessage<InitiateMobileVerificationResponse> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 1);

/**
 * @generated from message com.stablemoney.api.identity.RespondToMobileVerificationChallenge
 */
export type RespondToMobileVerificationChallenge = Message<"com.stablemoney.api.identity.RespondToMobileVerificationChallenge"> & {
  /**
   * @generated from field: string challenge_id = 1;
   */
  challengeId: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.identity.RespondToMobileVerificationChallenge.
 * Use `create(RespondToMobileVerificationChallengeSchema)` to create a new message.
 */
export const RespondToMobileVerificationChallengeSchema: GenMessage<RespondToMobileVerificationChallenge> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 2);

/**
 * @generated from message com.stablemoney.api.identity.RespondToMobileVerificationChallengeResponse
 */
export type RespondToMobileVerificationChallengeResponse = Message<"com.stablemoney.api.identity.RespondToMobileVerificationChallengeResponse"> & {
  /**
   * @generated from field: bool expired = 1;
   */
  expired: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message com.stablemoney.api.identity.RespondToMobileVerificationChallengeResponse.
 * Use `create(RespondToMobileVerificationChallengeResponseSchema)` to create a new message.
 */
export const RespondToMobileVerificationChallengeResponseSchema: GenMessage<RespondToMobileVerificationChallengeResponse> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 3);

