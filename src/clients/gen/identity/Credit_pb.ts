// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Credit.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Credit.proto.
 */
export const file_Credit: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message com.stablemoney.api.identity.CreditReportSummary
 */
export type CreditReportSummary = Message<"com.stablemoney.api.identity.CreditReportSummary"> & {
  /**
   * @generated from field: int32 credit_score = 1;
   */
  creditScore: number;

  /**
   * @generated from field: int32 min_credit_score = 2;
   */
  minCreditScore: number;

  /**
   * @generated from field: int32 max_credit_score = 3;
   */
  maxCreditScore: number;

  /**
   * @generated from field: string last_updated_date = 4;
   */
  lastUpdatedDate: string;

  /**
   * @generated from field: string summary_heading = 5;
   */
  summaryHeading: string;

  /**
   * @generated from field: string summary_description = 6;
   */
  summaryDescription: string;

  /**
   * @generated from field: string summary_logo_url = 7;
   */
  summaryLogoUrl: string;

  /**
   * @generated from field: double credit_score_percentage = 8;
   */
  creditScorePercentage: number;

  /**
   * @generated from field: string heading = 9;
   */
  heading: string;

  /**
   * @generated from field: string experian_logo_url = 10;
   */
  experianLogoUrl: string;

  /**
   * @generated from field: bool refresh_button = 11;
   */
  refreshButton: boolean;

  /**
   * @generated from field: string refresh_button_cta = 12;
   */
  refreshButtonCta: string;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditReportSummary.
 * Use `create(CreditReportSummarySchema)` to create a new message.
 */
export const CreditReportSummarySchema: GenMessage<CreditReportSummary> = /*@__PURE__*/
  messageDesc(file_Credit, 0);

/**
 * @generated from message com.stablemoney.api.identity.SectionItem
 */
export type SectionItem = Message<"com.stablemoney.api.identity.SectionItem"> & {
  /**
   * @generated from field: string item_logo = 1;
   */
  itemLogo: string;

  /**
   * @generated from field: string item_title = 2;
   */
  itemTitle: string;

  /**
   * @generated from field: string item_description = 3;
   */
  itemDescription: string;

  /**
   * @generated from field: double item_value = 4;
   */
  itemValue: number;

  /**
   * @generated from field: string item_value_str = 5;
   */
  itemValueStr: string;

  /**
   * @generated from field: string item_value_description = 6;
   */
  itemValueDescription: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SectionItem.
 * Use `create(SectionItemSchema)` to create a new message.
 */
export const SectionItemSchema: GenMessage<SectionItem> = /*@__PURE__*/
  messageDesc(file_Credit, 1);

/**
 * @generated from message com.stablemoney.api.identity.CreditReportSection
 */
export type CreditReportSection = Message<"com.stablemoney.api.identity.CreditReportSection"> & {
  /**
   * @generated from field: string section_title = 1;
   */
  sectionTitle: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.SectionItem section_item = 2;
   */
  sectionItem: SectionItem[];

  /**
   * @generated from field: string bottom_sheet_header = 3;
   */
  bottomSheetHeader: string;

  /**
   * @generated from field: string bottom_sheet_header_color = 4;
   */
  bottomSheetHeaderColor: string;

  /**
   * @generated from field: string bottom_sheet_description = 5;
   */
  bottomSheetDescription: string;

  /**
   * @generated from field: string bottom_sheet_cta = 6;
   */
  bottomSheetCta: string;

  /**
   * @generated from field: int32 size = 7;
   */
  size: number;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditReportSection.
 * Use `create(CreditReportSectionSchema)` to create a new message.
 */
export const CreditReportSectionSchema: GenMessage<CreditReportSection> = /*@__PURE__*/
  messageDesc(file_Credit, 2);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatsDetailSection
 */
export type CreditScoreStatsDetailSection = Message<"com.stablemoney.api.identity.CreditScoreStatsDetailSection"> & {
  /**
   * @generated from field: optional string section_title = 1;
   */
  sectionTitle?: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.SectionItem section_item = 2;
   */
  sectionItem: SectionItem[];
};

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatsDetailSection.
 * Use `create(CreditScoreStatsDetailSectionSchema)` to create a new message.
 */
export const CreditScoreStatsDetailSectionSchema: GenMessage<CreditScoreStatsDetailSection> = /*@__PURE__*/
  messageDesc(file_Credit, 3);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatsDetails
 */
export type CreditScoreStatsDetails = Message<"com.stablemoney.api.identity.CreditScoreStatsDetails"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.CreditScoreStatsDetailSection credit_score_stats_detail_sections = 1;
   */
  creditScoreStatsDetailSections: CreditScoreStatsDetailSection[];

  /**
   * @generated from field: string left_heading = 2;
   */
  leftHeading: string;

  /**
   * @generated from field: string right_heading = 3;
   */
  rightHeading: string;

  /**
   * @generated from field: string left_heading_value = 4;
   */
  leftHeadingValue: string;

  /**
   * @generated from field: string right_heading_value = 5;
   */
  rightHeadingValue: string;

  /**
   * @generated from field: string question = 6;
   */
  question: string;

  /**
   * @generated from field: string answer = 7;
   */
  answer: string;

  /**
   * @generated from field: string cta_text = 8;
   */
  ctaText: string;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatsDetails.
 * Use `create(CreditScoreStatsDetailsSchema)` to create a new message.
 */
export const CreditScoreStatsDetailsSchema: GenMessage<CreditScoreStatsDetails> = /*@__PURE__*/
  messageDesc(file_Credit, 4);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatItem
 */
export type CreditScoreStatItem = Message<"com.stablemoney.api.identity.CreditScoreStatItem"> & {
  /**
   * @generated from field: string stat_title = 1;
   */
  statTitle: string;

  /**
   * @generated from field: string stat_impact = 2;
   */
  statImpact: string;

  /**
   * @generated from field: string stat_value = 3;
   */
  statValue: string;

  /**
   * @generated from field: string stat_rating = 4;
   */
  statRating: string;

  /**
   * @generated from field: string stat_rating_color = 5;
   */
  statRatingColor: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.CreditScoreStatsDetails credit_score_stats_details = 6;
   */
  creditScoreStatsDetails?: CreditScoreStatsDetails;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatItem.
 * Use `create(CreditScoreStatItemSchema)` to create a new message.
 */
export const CreditScoreStatItemSchema: GenMessage<CreditScoreStatItem> = /*@__PURE__*/
  messageDesc(file_Credit, 5);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStats
 */
export type CreditScoreStats = Message<"com.stablemoney.api.identity.CreditScoreStats"> & {
  /**
   * @generated from field: string heading = 1;
   */
  heading: string;

  /**
   * @generated from field: string body = 2;
   */
  body: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.CreditScoreStatItem stats = 3;
   */
  stats: CreditScoreStatItem[];

  /**
   * @generated from field: string icon_url = 4;
   */
  iconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStats.
 * Use `create(CreditScoreStatsSchema)` to create a new message.
 */
export const CreditScoreStatsSchema: GenMessage<CreditScoreStats> = /*@__PURE__*/
  messageDesc(file_Credit, 6);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatsV2
 */
export type CreditScoreStatsV2 = Message<"com.stablemoney.api.identity.CreditScoreStatsV2"> & {
  /**
   * @generated from field: string heading = 1;
   */
  heading: string;

  /**
   * @generated from field: string body = 2;
   */
  body: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.CreditScoreStatItem stats = 3;
   */
  stats: CreditScoreStatItem[];

  /**
   * @generated from field: string icon_url = 4;
   */
  iconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatsV2.
 * Use `create(CreditScoreStatsV2Schema)` to create a new message.
 */
export const CreditScoreStatsV2Schema: GenMessage<CreditScoreStatsV2> = /*@__PURE__*/
  messageDesc(file_Credit, 7);

/**
 * @generated from message com.stablemoney.api.identity.CreditReport
 */
export type CreditReport = Message<"com.stablemoney.api.identity.CreditReport"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.CreditReportSummary credit_report_summary = 1;
   */
  creditReportSummary?: CreditReportSummary;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.CreditReportSection sections = 2;
   */
  sections: CreditReportSection[];

  /**
   * @generated from field: optional com.stablemoney.api.identity.CreditScoreStats credit_score_stats = 3;
   */
  creditScoreStats?: CreditScoreStats;

  /**
   * @generated from field: string experian_url = 4;
   */
  experianUrl: string;

  /**
   * @generated from field: string unique_tr_id = 5;
   */
  uniqueTrId: string;

  /**
   * @generated from field: string experian_ref_id = 6;
   */
  experianRefId: string;

  /**
   * @generated from field: string experian_cta = 7;
   */
  experianCta: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.CreditScoreStatsV2 credit_score_stats_v2 = 8;
   */
  creditScoreStatsV2?: CreditScoreStatsV2;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditReport.
 * Use `create(CreditReportSchema)` to create a new message.
 */
export const CreditReportSchema: GenMessage<CreditReport> = /*@__PURE__*/
  messageDesc(file_Credit, 8);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreDashboardResponse
 */
export type CreditScoreDashboardResponse = Message<"com.stablemoney.api.identity.CreditScoreDashboardResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.CreditScoreStatus credit_score_status = 1;
   */
  creditScoreStatus: CreditScoreStatus;

  /**
   * @generated from field: string section_title = 2;
   */
  sectionTitle: string;

  /**
   * @generated from field: string section_body = 3;
   */
  sectionBody: string;

  /**
   * @generated from field: optional int32 credit_score = 4;
   */
  creditScore?: number;

  /**
   * @generated from field: optional string credit_score_description = 5;
   */
  creditScoreDescription?: string;

  /**
   * @generated from field: optional string credit_score_color = 6;
   */
  creditScoreColor?: string;

  /**
   * @generated from field: optional string cta_text = 7;
   */
  ctaText?: string;

  /**
   * @generated from field: optional string last_updated_date = 8;
   */
  lastUpdatedDate?: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.CreditReport credit_report = 9;
   */
  creditReport?: CreditReport;
};

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreDashboardResponse.
 * Use `create(CreditScoreDashboardResponseSchema)` to create a new message.
 */
export const CreditScoreDashboardResponseSchema: GenMessage<CreditScoreDashboardResponse> = /*@__PURE__*/
  messageDesc(file_Credit, 9);

/**
 * @generated from enum com.stablemoney.api.identity.CreditScoreStatus
 */
export enum CreditScoreStatus {
  /**
   * @generated from enum value: UNKNOWN_CREDIT_SCORE_STATUS = 0;
   */
  UNKNOWN_CREDIT_SCORE_STATUS = 0,

  /**
   * @generated from enum value: NOT_INITIATED_CREDIT_SCORE_STATUS = 1;
   */
  NOT_INITIATED_CREDIT_SCORE_STATUS = 1,

  /**
   * @generated from enum value: ACTIVE_CREDIT_SCORE_STATUS = 2;
   */
  ACTIVE_CREDIT_SCORE_STATUS = 2,

  /**
   * @generated from enum value: CONSENT_REQUIRED_CREDIT_SCORE_STATUS = 3;
   */
  CONSENT_REQUIRED_CREDIT_SCORE_STATUS = 3,

  /**
   * @generated from enum value: NOT_FOUND_CREDIT_SCORE_STATUS = 4;
   */
  NOT_FOUND_CREDIT_SCORE_STATUS = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.CreditScoreStatus.
 */
export const CreditScoreStatusSchema: GenEnum<CreditScoreStatus> = /*@__PURE__*/
  enumDesc(file_Credit, 0);

