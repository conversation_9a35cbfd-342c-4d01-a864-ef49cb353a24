// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Common.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Common.proto.
 */
export const file_Common: GenFile = /*@__PURE__*/
  fileDesc("CgxDb21tb24ucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiOgoNRXJyb3JSZXNwb25zZRISCgplcnJvcl9jb2RlGAEgASgJEhUKDWVycm9yX21lc3NhZ2UYAiABKAkiTAoSQ29uZmlnRGF0YVJlc3BvbnNlEjYKBGRhdGEYASADKAsyKC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkNvbmZpZ0RhdGEi6gEKCkNvbmZpZ0RhdGESQAoLY29uZmlnX25hbWUYASABKA4yKy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkFwcENvbmZpZ1R5cGUSFAoMY29uZmlnX3ZhbHVlGAIgASgJEkUKC2NvbmZpZ190eXBlGAMgASgOMjAuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5BcHBDb25maWdWYWx1ZVR5cGUSEwoLbWluX3ZlcnNpb24YBCABKAUSEwoLbWF4X3ZlcnNpb24YBSABKAUSEwoLZGVzY3JpcHRpb24YBiABKAkiPAoQUGFnaW5hdGlvbkZpbHRlchIMCgRwYWdlGAEgASgFEhEKBHNpemUYAiABKAVIAIgBAUIHCgVfc2l6ZSL/AgoHRGF0YUtleRILCgNrZXkYASABKAkSSQoRY29udGV4dF92YXJpYWJsZXMYAyADKAsyLi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkRhdGFLZXkuVmFyaWFibGUaaQoIVmFyaWFibGUSDAoEbmFtZRgBIAEoCRJACgR0eXBlGAIgASgOMjIuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5EYXRhS2V5LlZhcmlhYmxlVHlwZRINCgV2YWx1ZRgDIAEoCSKwAQoMVmFyaWFibGVUeXBlEgsKB1VOS05PV04QABIKCgZTVFJJTkcQARIMCghDVVJSRU5DWRACEggKBERBVEUQAxINCglEQVRFX1RJTUUQBBITCg9VU0VSX0ZJUlNUX05BTUUQBRISCg5TSE9SVF9DVVJSRU5DWRAGEgsKB1BFUkNFTlQQBxIOCgpQRVJDRU5UXzJGEAgSCgoGTlVNQkVSEAkSDgoKQ09VTlRfRE9XThAKIikKElBhZ2luYXRpb25SZXNwb25zZRITCgtoYXNOZXh0UGFnZRgBIAEoCCIvChFQYWdpbmF0aW9uUmVxdWVzdBIMCgRwYWdlGAEgASgFEgwKBHNpemUYAiABKAUqbAoLTWVzc2FnZVR5cGUSGAoUVU5LTk9XTl9NRVNTQUdFX1RZUEUQABIXChNQUk9NT1RJT05BTF9NRVNTQUdFEAESGQoVVFJBTlNBQ1RJT05BTF9NRVNTQUdFEAISDwoLT1RQX01FU1NBR0UQAyo+CgtDb250ZW50VHlwZRIYChRVTktOT1dOX0NPTlRFTlRfVFlQRRAAEggKBFRFWFQQARILCgdVTklDT0RFEAIqRAoLT3B0aW5TdGF0dXMSGAoUVU5LTk9XTl9PUFRJTl9TVEFUVVMQABIMCghPUFRFRF9JThABEg0KCU9QVEVEX09VVBACKkcKFFdoYXRzYXBwUHJvdmlkZXJUeXBlEiIKHlVOS05PV05fV0hBVFNBUFBfUFJPVklERVJfVFlQRRAAEgsKB0dVUFNIVVAQASrKCQoNQXBwQ29uZmlnVHlwZRIbChdBUFBfQ09ORklHX1RZUEVfVU5LTk9XThAAEhkKFUJBTktfVkVSSUZJQ0FUSU9OX1JQRBABEhgKFEJBTktfVkVSSUZJQ0FUSU9OX1BEEAISGwoXQkFTSUNfREVUQUlMU19RVUVTVElPTlMQAxIRCg1QQU5fQ1ZMX0NIRUNLEAQSGgoWUlBEX1NVUFBPUlRFRF9VUElfQVBQUxAFEhEKDUNSRURJVF9SRVBPUlQQBhIRCg1NT0JJTEVfUkVTRU5EEAcSEgoOTU9CSUxFX0FUVEVNUFQQCBIVChFNT0JJTEVfT1RQX0xFTkdUSBAJEhQKEEVNQUlMX09UUF9MRU5HVEgQChIQCgxFTUFJTF9SRVNFTkQQCxIRCg1FTUFJTF9BVFRFTVBUEAwSFwoTQU5EUk9JRF9BUFBfVkVSU0lPThANEhMKD0lPU19BUFBfVkVSU0lPThAOEhMKD1dFQl9BUFBfVkVSU0lPThAPEiYKIk1PQklMRV9MT0dJTl9CTE9DS0VEX1RJTUVfSU5fSE9VUlMQEBIsCihFTUFJTF9MT0dJTl9SRVNFTkRfQkxPQ0tFRF9USU1FX0lOX0hPVVJTEBESJQohTU9CSUxFX09UUF9FWFBJUllfVElNRV9JTl9NSU5VVEVTEBISJAogRU1BSUxfT1RQX0VYUElSWV9USU1FX0lOX01JTlVURVMQExI2CjJFTUFJTF9PVFBfUkVHRU5FUkFURV9CRUZPUkVfRVhQSVJZX1RJTUVfSU5fTUlOVVRFUxAUEi0KKUVNQUlMX0xPR0lOX0FUVEVNUFRfQkxPQ0tFRF9USU1FX0lOX0hPVVJTEBUSLgoqTU9CSUxFX0xPR0lOX0FUVEVNUFRfQkxPQ0tFRF9USU1FX0lOX0hPVVJTEBYSLQopTU9CSUxFX0xPR0lOX1JFU0VORF9CTE9DS0VEX1RJTUVfSU5fSE9VUlMQFxI3CjNNT0JJTEVfT1RQX1JFR0VORVJBVEVfQkVGT1JFX0VYUElSWV9USU1FX0lOX01JTlVURVMQGBIlCiFFTUFJTF9PVFBfUkVTRU5EX1RJTUVSX0lOX1NFQ09ORFMQGRImCiJNT0JJTEVfT1RQX1JFU0VORF9USU1FUl9JTl9TRUNPTkRTEBoSGwoXVE9LRU5fVkFMSURJVFlfSU5fSE9VUlMQGxIjCh9SRUZSRVNIX1RPS0VOX1ZBTElESVRZX0lOX0hPVVJTEBwSGQoVRU5DUllQVElPTl9QVUJMSUNfS0VZEB0SDwoLWk9IT19DT05GSUcQHhIYChRJTl9BUFBfUkFUSU5HX0NPTkZJRxAfEhsKF0dUX0dSQUNFX1BFUklPRF9JTl9EQVlTECASHQoZUlNBX0VOQ1JZUFRJT05fUFVCTElDX0tFWRAhEhUKEUxPQ0FMSVpBVElPTl9DQUxMECISFwoTSU5WRVNUSUJMRV9CQU5LX0lEUxAjEhgKFEJBTktfU1VQUE9SVF9OVU1CRVJTECQSEQoNRU5BQkxFX1NFTlRSWRAlEhcKE0dVUFNIVVBfU0lNX0JJTkRJTkcQJhIhCh1TQ0NfREVMSVZFUllfUkVRVUVTVF9ET0NVTUVOVBAnKooBChJBcHBDb25maWdWYWx1ZVR5cGUSIQodQVBQX0NPTkZJR19WQUxVRV9UWVBFX1VOS05PV04QABINCglURVhUX1RZUEUQARIQCgxCT09MRUFOX1RZUEUQAhIPCgtTVFJJTkdfVFlQRRADEg0KCUpTT05fVFlQRRAEEhAKDElOVEVHRVJfVFlQRRAFKt8DChRQdXNoTm90aWZpY2F0aW9uVHlwZRIiCh5VTktOT1dOX1BVU0hfTk9USUZJQ0FUSU9OX1RZUEUQABIWChJURF9CT09LRURfQ09NUExFVEUQARITCg9QQVlNRU5UX1NVQ0NFU1MQAhINCglURF9SRVNVTUUQAxITCg9QQVlNRU5UX0ZBSUxVUkUQBBIQCgxWS1lDX1NVQ0NFU1MQBRIRCg1WS1lDX1JFTUlOREVSEAYSDgoKVktZQ19SRVRSWRAHEhoKFlREX1dJVEhEUkFXQUxfQ09NUExFVEUQCBIPCgtURF9SRUpFQ1RFRBAJEg8KC1REX01BVFVSSVRZEAoSEQoNVERfQk9PS0VEX0FDSxALEhIKDlBST0dSRVNTX1NBVkVEEAwSFgoSSU5WRVNUX0lOU1RBTlRfRkRTEA0SEAoMVElDS0VUX0FEREVEEA4SEgoOTlRCX0FVVEhfRVJST1IQDxISCg5FVEJfQVVUSF9FUlJPUhAQEhMKD0JJTkRJTkdfU1VDQ0VTUxAREhAKDFZLWUNfRkFJTFVSRRASEiYKIlREX01BVFVSSVRZX0lOU1RSVUNUSU9OX1VQREFURV9EVUUQExIXChNURF9SRU5FV0FMX0NPTVBMRVRFEBRCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.identity.ErrorResponse
 */
export type ErrorResponse = Message<"com.stablemoney.api.identity.ErrorResponse"> & {
  /**
   * @generated from field: string error_code = 1;
   */
  errorCode: string;

  /**
   * @generated from field: string error_message = 2;
   */
  errorMessage: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ErrorResponse.
 * Use `create(ErrorResponseSchema)` to create a new message.
 */
export const ErrorResponseSchema: GenMessage<ErrorResponse> = /*@__PURE__*/
  messageDesc(file_Common, 0);

/**
 * @generated from message com.stablemoney.api.identity.ConfigDataResponse
 */
export type ConfigDataResponse = Message<"com.stablemoney.api.identity.ConfigDataResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.ConfigData data = 1;
   */
  data: ConfigData[];
};

/**
 * Describes the message com.stablemoney.api.identity.ConfigDataResponse.
 * Use `create(ConfigDataResponseSchema)` to create a new message.
 */
export const ConfigDataResponseSchema: GenMessage<ConfigDataResponse> = /*@__PURE__*/
  messageDesc(file_Common, 1);

/**
 * @generated from message com.stablemoney.api.identity.ConfigData
 */
export type ConfigData = Message<"com.stablemoney.api.identity.ConfigData"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.AppConfigType config_name = 1;
   */
  configName: AppConfigType;

  /**
   * @generated from field: string config_value = 2;
   */
  configValue: string;

  /**
   * @generated from field: com.stablemoney.api.identity.AppConfigValueType config_type = 3;
   */
  configType: AppConfigValueType;

  /**
   * @generated from field: int32 min_version = 4;
   */
  minVersion: number;

  /**
   * @generated from field: int32 max_version = 5;
   */
  maxVersion: number;

  /**
   * @generated from field: string description = 6;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ConfigData.
 * Use `create(ConfigDataSchema)` to create a new message.
 */
export const ConfigDataSchema: GenMessage<ConfigData> = /*@__PURE__*/
  messageDesc(file_Common, 2);

/**
 * @generated from message com.stablemoney.api.identity.PaginationFilter
 */
export type PaginationFilter = Message<"com.stablemoney.api.identity.PaginationFilter"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: optional int32 size = 2;
   */
  size?: number;
};

/**
 * Describes the message com.stablemoney.api.identity.PaginationFilter.
 * Use `create(PaginationFilterSchema)` to create a new message.
 */
export const PaginationFilterSchema: GenMessage<PaginationFilter> = /*@__PURE__*/
  messageDesc(file_Common, 3);

/**
 * @generated from message com.stablemoney.api.identity.DataKey
 */
export type DataKey = Message<"com.stablemoney.api.identity.DataKey"> & {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.DataKey.Variable context_variables = 3;
   */
  contextVariables: DataKey_Variable[];
};

/**
 * Describes the message com.stablemoney.api.identity.DataKey.
 * Use `create(DataKeySchema)` to create a new message.
 */
export const DataKeySchema: GenMessage<DataKey> = /*@__PURE__*/
  messageDesc(file_Common, 4);

/**
 * @generated from message com.stablemoney.api.identity.DataKey.Variable
 */
export type DataKey_Variable = Message<"com.stablemoney.api.identity.DataKey.Variable"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.identity.DataKey.VariableType type = 2;
   */
  type: DataKey_VariableType;

  /**
   * @generated from field: string value = 3;
   */
  value: string;
};

/**
 * Describes the message com.stablemoney.api.identity.DataKey.Variable.
 * Use `create(DataKey_VariableSchema)` to create a new message.
 */
export const DataKey_VariableSchema: GenMessage<DataKey_Variable> = /*@__PURE__*/
  messageDesc(file_Common, 4, 0);

/**
 * @generated from enum com.stablemoney.api.identity.DataKey.VariableType
 */
export enum DataKey_VariableType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STRING = 1;
   */
  STRING = 1,

  /**
   * @generated from enum value: CURRENCY = 2;
   */
  CURRENCY = 2,

  /**
   * @generated from enum value: DATE = 3;
   */
  DATE = 3,

  /**
   * @generated from enum value: DATE_TIME = 4;
   */
  DATE_TIME = 4,

  /**
   * @generated from enum value: USER_FIRST_NAME = 5;
   */
  USER_FIRST_NAME = 5,

  /**
   * @generated from enum value: SHORT_CURRENCY = 6;
   */
  SHORT_CURRENCY = 6,

  /**
   * @generated from enum value: PERCENT = 7;
   */
  PERCENT = 7,

  /**
   * @generated from enum value: PERCENT_2F = 8;
   */
  PERCENT_2F = 8,

  /**
   * @generated from enum value: NUMBER = 9;
   */
  NUMBER = 9,

  /**
   * @generated from enum value: COUNT_DOWN = 10;
   */
  COUNT_DOWN = 10,
}

/**
 * Describes the enum com.stablemoney.api.identity.DataKey.VariableType.
 */
export const DataKey_VariableTypeSchema: GenEnum<DataKey_VariableType> = /*@__PURE__*/
  enumDesc(file_Common, 4, 0);

/**
 * @generated from message com.stablemoney.api.identity.PaginationResponse
 */
export type PaginationResponse = Message<"com.stablemoney.api.identity.PaginationResponse"> & {
  /**
   * @generated from field: bool hasNextPage = 1;
   */
  hasNextPage: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.PaginationResponse.
 * Use `create(PaginationResponseSchema)` to create a new message.
 */
export const PaginationResponseSchema: GenMessage<PaginationResponse> = /*@__PURE__*/
  messageDesc(file_Common, 5);

/**
 * @generated from message com.stablemoney.api.identity.PaginationRequest
 */
export type PaginationRequest = Message<"com.stablemoney.api.identity.PaginationRequest"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: int32 size = 2;
   */
  size: number;
};

/**
 * Describes the message com.stablemoney.api.identity.PaginationRequest.
 * Use `create(PaginationRequestSchema)` to create a new message.
 */
export const PaginationRequestSchema: GenMessage<PaginationRequest> = /*@__PURE__*/
  messageDesc(file_Common, 6);

/**
 * @generated from enum com.stablemoney.api.identity.MessageType
 */
export enum MessageType {
  /**
   * @generated from enum value: UNKNOWN_MESSAGE_TYPE = 0;
   */
  UNKNOWN_MESSAGE_TYPE = 0,

  /**
   * @generated from enum value: PROMOTIONAL_MESSAGE = 1;
   */
  PROMOTIONAL_MESSAGE = 1,

  /**
   * @generated from enum value: TRANSACTIONAL_MESSAGE = 2;
   */
  TRANSACTIONAL_MESSAGE = 2,

  /**
   * @generated from enum value: OTP_MESSAGE = 3;
   */
  OTP_MESSAGE = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType> = /*@__PURE__*/
  enumDesc(file_Common, 0);

/**
 * @generated from enum com.stablemoney.api.identity.ContentType
 */
export enum ContentType {
  /**
   * @generated from enum value: UNKNOWN_CONTENT_TYPE = 0;
   */
  UNKNOWN_CONTENT_TYPE = 0,

  /**
   * @generated from enum value: TEXT = 1;
   */
  TEXT = 1,

  /**
   * @generated from enum value: UNICODE = 2;
   */
  UNICODE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.ContentType.
 */
export const ContentTypeSchema: GenEnum<ContentType> = /*@__PURE__*/
  enumDesc(file_Common, 1);

/**
 * @generated from enum com.stablemoney.api.identity.OptinStatus
 */
export enum OptinStatus {
  /**
   * @generated from enum value: UNKNOWN_OPTIN_STATUS = 0;
   */
  UNKNOWN_OPTIN_STATUS = 0,

  /**
   * @generated from enum value: OPTED_IN = 1;
   */
  OPTED_IN = 1,

  /**
   * @generated from enum value: OPTED_OUT = 2;
   */
  OPTED_OUT = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.OptinStatus.
 */
export const OptinStatusSchema: GenEnum<OptinStatus> = /*@__PURE__*/
  enumDesc(file_Common, 2);

/**
 * @generated from enum com.stablemoney.api.identity.WhatsappProviderType
 */
export enum WhatsappProviderType {
  /**
   * @generated from enum value: UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0;
   */
  UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0,

  /**
   * @generated from enum value: GUPSHUP = 1;
   */
  GUPSHUP = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.WhatsappProviderType.
 */
export const WhatsappProviderTypeSchema: GenEnum<WhatsappProviderType> = /*@__PURE__*/
  enumDesc(file_Common, 3);

/**
 * @generated from enum com.stablemoney.api.identity.AppConfigType
 */
export enum AppConfigType {
  /**
   * @generated from enum value: APP_CONFIG_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: BANK_VERIFICATION_RPD = 1;
   */
  BANK_VERIFICATION_RPD = 1,

  /**
   * @generated from enum value: BANK_VERIFICATION_PD = 2;
   */
  BANK_VERIFICATION_PD = 2,

  /**
   * @generated from enum value: BASIC_DETAILS_QUESTIONS = 3;
   */
  BASIC_DETAILS_QUESTIONS = 3,

  /**
   * @generated from enum value: PAN_CVL_CHECK = 4;
   */
  PAN_CVL_CHECK = 4,

  /**
   * @generated from enum value: RPD_SUPPORTED_UPI_APPS = 5;
   */
  RPD_SUPPORTED_UPI_APPS = 5,

  /**
   * @generated from enum value: CREDIT_REPORT = 6;
   */
  CREDIT_REPORT = 6,

  /**
   * @generated from enum value: MOBILE_RESEND = 7;
   */
  MOBILE_RESEND = 7,

  /**
   * @generated from enum value: MOBILE_ATTEMPT = 8;
   */
  MOBILE_ATTEMPT = 8,

  /**
   * @generated from enum value: MOBILE_OTP_LENGTH = 9;
   */
  MOBILE_OTP_LENGTH = 9,

  /**
   * @generated from enum value: EMAIL_OTP_LENGTH = 10;
   */
  EMAIL_OTP_LENGTH = 10,

  /**
   * @generated from enum value: EMAIL_RESEND = 11;
   */
  EMAIL_RESEND = 11,

  /**
   * @generated from enum value: EMAIL_ATTEMPT = 12;
   */
  EMAIL_ATTEMPT = 12,

  /**
   * @generated from enum value: ANDROID_APP_VERSION = 13;
   */
  ANDROID_APP_VERSION = 13,

  /**
   * @generated from enum value: IOS_APP_VERSION = 14;
   */
  IOS_APP_VERSION = 14,

  /**
   * @generated from enum value: WEB_APP_VERSION = 15;
   */
  WEB_APP_VERSION = 15,

  /**
   * @generated from enum value: MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16;
   */
  MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16,

  /**
   * @generated from enum value: EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17;
   */
  EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17,

  /**
   * @generated from enum value: MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18;
   */
  MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18,

  /**
   * @generated from enum value: EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19;
   */
  EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19,

  /**
   * @generated from enum value: EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20;
   */
  EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20,

  /**
   * @generated from enum value: EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21;
   */
  EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21,

  /**
   * @generated from enum value: MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22;
   */
  MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22,

  /**
   * @generated from enum value: MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23;
   */
  MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23,

  /**
   * @generated from enum value: MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24;
   */
  MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24,

  /**
   * @generated from enum value: EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25;
   */
  EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25,

  /**
   * @generated from enum value: MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26;
   */
  MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26,

  /**
   * @generated from enum value: TOKEN_VALIDITY_IN_HOURS = 27;
   */
  TOKEN_VALIDITY_IN_HOURS = 27,

  /**
   * @generated from enum value: REFRESH_TOKEN_VALIDITY_IN_HOURS = 28;
   */
  REFRESH_TOKEN_VALIDITY_IN_HOURS = 28,

  /**
   * @generated from enum value: ENCRYPTION_PUBLIC_KEY = 29;
   */
  ENCRYPTION_PUBLIC_KEY = 29,

  /**
   * @generated from enum value: ZOHO_CONFIG = 30;
   */
  ZOHO_CONFIG = 30,

  /**
   * @generated from enum value: IN_APP_RATING_CONFIG = 31;
   */
  IN_APP_RATING_CONFIG = 31,

  /**
   * @generated from enum value: GT_GRACE_PERIOD_IN_DAYS = 32;
   */
  GT_GRACE_PERIOD_IN_DAYS = 32,

  /**
   * @generated from enum value: RSA_ENCRYPTION_PUBLIC_KEY = 33;
   */
  RSA_ENCRYPTION_PUBLIC_KEY = 33,

  /**
   * @generated from enum value: LOCALIZATION_CALL = 34;
   */
  LOCALIZATION_CALL = 34,

  /**
   * @generated from enum value: INVESTIBLE_BANK_IDS = 35;
   */
  INVESTIBLE_BANK_IDS = 35,

  /**
   * @generated from enum value: BANK_SUPPORT_NUMBERS = 36;
   */
  BANK_SUPPORT_NUMBERS = 36,

  /**
   * @generated from enum value: ENABLE_SENTRY = 37;
   */
  ENABLE_SENTRY = 37,

  /**
   * @generated from enum value: GUPSHUP_SIM_BINDING = 38;
   */
  GUPSHUP_SIM_BINDING = 38,

  /**
   * @generated from enum value: SCC_DELIVERY_REQUEST_DOCUMENT = 39;
   */
  SCC_DELIVERY_REQUEST_DOCUMENT = 39,
}

/**
 * Describes the enum com.stablemoney.api.identity.AppConfigType.
 */
export const AppConfigTypeSchema: GenEnum<AppConfigType> = /*@__PURE__*/
  enumDesc(file_Common, 4);

/**
 * @generated from enum com.stablemoney.api.identity.AppConfigValueType
 */
export enum AppConfigValueType {
  /**
   * @generated from enum value: APP_CONFIG_VALUE_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_VALUE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: TEXT_TYPE = 1;
   */
  TEXT_TYPE = 1,

  /**
   * @generated from enum value: BOOLEAN_TYPE = 2;
   */
  BOOLEAN_TYPE = 2,

  /**
   * @generated from enum value: STRING_TYPE = 3;
   */
  STRING_TYPE = 3,

  /**
   * @generated from enum value: JSON_TYPE = 4;
   */
  JSON_TYPE = 4,

  /**
   * @generated from enum value: INTEGER_TYPE = 5;
   */
  INTEGER_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.AppConfigValueType.
 */
export const AppConfigValueTypeSchema: GenEnum<AppConfigValueType> = /*@__PURE__*/
  enumDesc(file_Common, 5);

/**
 * @generated from enum com.stablemoney.api.identity.PushNotificationType
 */
export enum PushNotificationType {
  /**
   * @generated from enum value: UNKNOWN_PUSH_NOTIFICATION_TYPE = 0;
   */
  UNKNOWN_PUSH_NOTIFICATION_TYPE = 0,

  /**
   * @generated from enum value: TD_BOOKED_COMPLETE = 1;
   */
  TD_BOOKED_COMPLETE = 1,

  /**
   * @generated from enum value: PAYMENT_SUCCESS = 2;
   */
  PAYMENT_SUCCESS = 2,

  /**
   * @generated from enum value: TD_RESUME = 3;
   */
  TD_RESUME = 3,

  /**
   * @generated from enum value: PAYMENT_FAILURE = 4;
   */
  PAYMENT_FAILURE = 4,

  /**
   * @generated from enum value: VKYC_SUCCESS = 5;
   */
  VKYC_SUCCESS = 5,

  /**
   * @generated from enum value: VKYC_REMINDER = 6;
   */
  VKYC_REMINDER = 6,

  /**
   * @generated from enum value: VKYC_RETRY = 7;
   */
  VKYC_RETRY = 7,

  /**
   * @generated from enum value: TD_WITHDRAWAL_COMPLETE = 8;
   */
  TD_WITHDRAWAL_COMPLETE = 8,

  /**
   * @generated from enum value: TD_REJECTED = 9;
   */
  TD_REJECTED = 9,

  /**
   * @generated from enum value: TD_MATURITY = 10;
   */
  TD_MATURITY = 10,

  /**
   * @generated from enum value: TD_BOOKED_ACK = 11;
   */
  TD_BOOKED_ACK = 11,

  /**
   * @generated from enum value: PROGRESS_SAVED = 12;
   */
  PROGRESS_SAVED = 12,

  /**
   * @generated from enum value: INVEST_INSTANT_FDS = 13;
   */
  INVEST_INSTANT_FDS = 13,

  /**
   * @generated from enum value: TICKET_ADDED = 14;
   */
  TICKET_ADDED = 14,

  /**
   * @generated from enum value: NTB_AUTH_ERROR = 15;
   */
  NTB_AUTH_ERROR = 15,

  /**
   * @generated from enum value: ETB_AUTH_ERROR = 16;
   */
  ETB_AUTH_ERROR = 16,

  /**
   * @generated from enum value: BINDING_SUCCESS = 17;
   */
  BINDING_SUCCESS = 17,

  /**
   * @generated from enum value: VKYC_FAILURE = 18;
   */
  VKYC_FAILURE = 18,

  /**
   * @generated from enum value: TD_MATURITY_INSTRUCTION_UPDATE_DUE = 19;
   */
  TD_MATURITY_INSTRUCTION_UPDATE_DUE = 19,

  /**
   * @generated from enum value: TD_RENEWAL_COMPLETE = 20;
   */
  TD_RENEWAL_COMPLETE = 20,
}

/**
 * Describes the enum com.stablemoney.api.identity.PushNotificationType.
 */
export const PushNotificationTypeSchema: GenEnum<PushNotificationType> = /*@__PURE__*/
  enumDesc(file_Common, 6);

