// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Referral.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { PaginationFilter } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { CampaignType } from "./Campaign_pb.js";
import { file_Campaign } from "./Campaign_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Referral.proto.
 */
export const file_Referral: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Common, file_Campaign]);

/**
 * @generated from message com.stablemoney.api.identity.ReferralItem
 */
export type ReferralItem = Message<"com.stablemoney.api.identity.ReferralItem"> & {
  /**
   * @generated from field: string referee_name = 1;
   */
  refereeName: string;

  /**
   * @generated from field: string referee_dp = 2;
   */
  refereeDp: string;

  /**
   * @generated from field: string referral_level = 3;
   */
  referralLevel: string;

  /**
   * @generated from field: string referral_level_pic_url = 4;
   */
  referralLevelPicUrl: string;

  /**
   * @generated from field: string referral_status_description = 5;
   */
  referralStatusDescription: string;

  /**
   * @generated from field: com.stablemoney.api.identity.ReferralItemStatus referral_status = 6;
   */
  referralStatus: ReferralItemStatus;

  /**
   * @generated from field: optional double reward_amount = 7;
   */
  rewardAmount?: number;

  /**
   * @generated from field: optional string days_left = 8;
   */
  daysLeft?: string;

  /**
   * @generated from field: string updated_at = 9;
   */
  updatedAt: string;

  /**
   * @generated from field: string share_text = 10;
   */
  shareText: string;

  /**
   * @generated from field: string phone_number = 11;
   */
  phoneNumber: string;

  /**
   * @generated from field: string cta_text = 12;
   */
  ctaText: string;

  /**
   * @generated from field: string referee_id = 13;
   */
  refereeId: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralItem.
 * Use `create(ReferralItemSchema)` to create a new message.
 */
export const ReferralItemSchema: GenMessage<ReferralItem> = /*@__PURE__*/
  messageDesc(file_Referral, 0);

/**
 * @generated from message com.stablemoney.api.identity.ReferralSummary
 */
export type ReferralSummary = Message<"com.stablemoney.api.identity.ReferralSummary"> & {
  /**
   * @generated from field: double referral_earnings = 1;
   */
  referralEarnings: number;

  /**
   * @generated from field: double withdrawable_earnings = 2;
   */
  withdrawableEarnings: number;

  /**
   * @generated from field: double maximum_earnings = 3;
   */
  maximumEarnings: number;

  /**
   * @generated from field: int32 total_referrals = 4;
   */
  totalReferrals: number;

  /**
   * @generated from field: int32 successful_referrals = 5;
   */
  successfulReferrals: number;

  /**
   * @generated from field: string title = 6;
   */
  title: string;

  /**
   * @generated from field: double current_earnings_percentage = 7;
   */
  currentEarningsPercentage: number;
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralSummary.
 * Use `create(ReferralSummarySchema)` to create a new message.
 */
export const ReferralSummarySchema: GenMessage<ReferralSummary> = /*@__PURE__*/
  messageDesc(file_Referral, 1);

/**
 * @generated from message com.stablemoney.api.identity.ReferralDashboardResponse
 */
export type ReferralDashboardResponse = Message<"com.stablemoney.api.identity.ReferralDashboardResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.ReferralSummary referralSummary = 1;
   */
  referralSummary?: ReferralSummary;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.ReferralItem referral_items_list = 2;
   */
  referralItemsList: ReferralItem[];
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralDashboardResponse.
 * Use `create(ReferralDashboardResponseSchema)` to create a new message.
 */
export const ReferralDashboardResponseSchema: GenMessage<ReferralDashboardResponse> = /*@__PURE__*/
  messageDesc(file_Referral, 2);

/**
 * @generated from message com.stablemoney.api.identity.ReferrerItem
 */
export type ReferrerItem = Message<"com.stablemoney.api.identity.ReferrerItem"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string id = 2;
   */
  id: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ReferrerItem.
 * Use `create(ReferrerItemSchema)` to create a new message.
 */
export const ReferrerItemSchema: GenMessage<ReferrerItem> = /*@__PURE__*/
  messageDesc(file_Referral, 3);

/**
 * @generated from message com.stablemoney.api.identity.ReferralDashboardResponseV2
 */
export type ReferralDashboardResponseV2 = Message<"com.stablemoney.api.identity.ReferralDashboardResponseV2"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.ReferralSummary referralSummary = 1;
   */
  referralSummary?: ReferralSummary;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.ReferralItem referral_items_list = 2;
   */
  referralItemsList: ReferralItem[];

  /**
   * @generated from field: optional double potential_earning_amount = 3;
   */
  potentialEarningAmount?: number;

  /**
   * @generated from field: com.stablemoney.api.identity.ReferrerItem referrer = 4;
   */
  referrer?: ReferrerItem;
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralDashboardResponseV2.
 * Use `create(ReferralDashboardResponseV2Schema)` to create a new message.
 */
export const ReferralDashboardResponseV2Schema: GenMessage<ReferralDashboardResponseV2> = /*@__PURE__*/
  messageDesc(file_Referral, 4);

/**
 * @generated from message com.stablemoney.api.identity.ReferralLeaderboardItem
 */
export type ReferralLeaderboardItem = Message<"com.stablemoney.api.identity.ReferralLeaderboardItem"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: int32 rank = 2;
   */
  rank: number;

  /**
   * @generated from field: int32 referral_count = 3;
   */
  referralCount: number;

  /**
   * @generated from field: double referral_earning = 4;
   */
  referralEarning: number;

  /**
   * @generated from field: string icon_url = 5;
   */
  iconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralLeaderboardItem.
 * Use `create(ReferralLeaderboardItemSchema)` to create a new message.
 */
export const ReferralLeaderboardItemSchema: GenMessage<ReferralLeaderboardItem> = /*@__PURE__*/
  messageDesc(file_Referral, 5);

/**
 * @generated from message com.stablemoney.api.identity.ReferralLeaderboardResponse
 */
export type ReferralLeaderboardResponse = Message<"com.stablemoney.api.identity.ReferralLeaderboardResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.ReferralLeaderboardItem referral_items_list = 2;
   */
  referralItemsList: ReferralLeaderboardItem[];
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralLeaderboardResponse.
 * Use `create(ReferralLeaderboardResponseSchema)` to create a new message.
 */
export const ReferralLeaderboardResponseSchema: GenMessage<ReferralLeaderboardResponse> = /*@__PURE__*/
  messageDesc(file_Referral, 6);

/**
 * @generated from message com.stablemoney.api.identity.GetRefereesRequest
 */
export type GetRefereesRequest = Message<"com.stablemoney.api.identity.GetRefereesRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.PaginationFilter paginationFilter = 1;
   */
  paginationFilter?: PaginationFilter;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.CampaignType campaignTypes = 3;
   */
  campaignTypes: CampaignType[];
};

/**
 * Describes the message com.stablemoney.api.identity.GetRefereesRequest.
 * Use `create(GetRefereesRequestSchema)` to create a new message.
 */
export const GetRefereesRequestSchema: GenMessage<GetRefereesRequest> = /*@__PURE__*/
  messageDesc(file_Referral, 7);

/**
 * @generated from message com.stablemoney.api.identity.Referee
 */
export type Referee = Message<"com.stablemoney.api.identity.Referee"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string first_name = 2;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 3;
   */
  lastName: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.CampaignType campaign_type = 4;
   */
  campaignType?: CampaignType;
};

/**
 * Describes the message com.stablemoney.api.identity.Referee.
 * Use `create(RefereeSchema)` to create a new message.
 */
export const RefereeSchema: GenMessage<Referee> = /*@__PURE__*/
  messageDesc(file_Referral, 8);

/**
 * @generated from message com.stablemoney.api.identity.Referer
 */
export type Referer = Message<"com.stablemoney.api.identity.Referer"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string first_name = 2;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 3;
   */
  lastName: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.CampaignType campaign_type = 4;
   */
  campaignType?: CampaignType;
};

/**
 * Describes the message com.stablemoney.api.identity.Referer.
 * Use `create(RefererSchema)` to create a new message.
 */
export const RefererSchema: GenMessage<Referer> = /*@__PURE__*/
  messageDesc(file_Referral, 9);

/**
 * @generated from message com.stablemoney.api.identity.GetRefereesResponse
 */
export type GetRefereesResponse = Message<"com.stablemoney.api.identity.GetRefereesResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.Referee referees = 1;
   */
  referees: Referee[];
};

/**
 * Describes the message com.stablemoney.api.identity.GetRefereesResponse.
 * Use `create(GetRefereesResponseSchema)` to create a new message.
 */
export const GetRefereesResponseSchema: GenMessage<GetRefereesResponse> = /*@__PURE__*/
  messageDesc(file_Referral, 10);

/**
 * @generated from message com.stablemoney.api.identity.ReferralLinkRequest
 */
export type ReferralLinkRequest = Message<"com.stablemoney.api.identity.ReferralLinkRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: com.stablemoney.api.identity.CampaignType campaign_type = 2;
   */
  campaignType: CampaignType;
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralLinkRequest.
 * Use `create(ReferralLinkRequestSchema)` to create a new message.
 */
export const ReferralLinkRequestSchema: GenMessage<ReferralLinkRequest> = /*@__PURE__*/
  messageDesc(file_Referral, 11);

/**
 * @generated from enum com.stablemoney.api.identity.ReferralItemStatus
 */
export enum ReferralItemStatus {
  /**
   * @generated from enum value: UNKNOWN_REFERRAL_STATUS = 0;
   */
  UNKNOWN_REFERRAL_STATUS = 0,

  /**
   * @generated from enum value: BOOKING_PENDING_REFERRAL_STATUS = 1;
   */
  BOOKING_PENDING_REFERRAL_STATUS = 1,

  /**
   * @generated from enum value: FD_BOOKED_REFERRAL_STATUS = 2;
   */
  FD_BOOKED_REFERRAL_STATUS = 2,

  /**
   * @generated from enum value: FD_WITHDRAWN_REFERRAL_STATUS = 3;
   */
  FD_WITHDRAWN_REFERRAL_STATUS = 3,

  /**
   * @generated from enum value: FD_BOOKED_INELIGIBLE_STATUS = 4;
   */
  FD_BOOKED_INELIGIBLE_STATUS = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.ReferralItemStatus.
 */
export const ReferralItemStatusSchema: GenEnum<ReferralItemStatus> = /*@__PURE__*/
  enumDesc(file_Referral, 0);

