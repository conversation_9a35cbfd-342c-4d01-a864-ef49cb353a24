// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file DeviceBinding.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file DeviceBinding.proto.
 */
export const file_DeviceBinding: GenFile = /*@__PURE__*/
  fileDesc("ChNEZXZpY2VCaW5kaW5nLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5ImIKFERldmljZUJpbmRpbmdSZXF1ZXN0EhEKCWRldmljZV9pZBgBIAEoCRI3CghzaW1fZGF0YRgCIAMoCzIlLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuU2ltRGF0YSI2CgdTaW1EYXRhEhkKEXNpbV9zZXJpYWxfbnVtYmVyGAEgASgJEhAKCHNpbV9uYW1lGAIgASgJIlMKFURldmljZUJpbmRpbmdSZXNwb25zZRIPCgdtZXNzYWdlGAEgASgJEhIKCnJlcXVlc3RfaWQYAiABKAkSFQoNbW9iaWxlX251bWJlchgDIAEoCSK/AQoRSW5ib3VuZFNtc1JlcXVlc3QSFwoPbG9uZ2NvZGVfbnVtYmVyGAEgASgJEhcKD2N1c3RvbWVyX251bWJlchgCIAEoCRINCgVwcmljZRgIIAEoCRIOCgZzdGF0dXMYCSABKAkSDgoGY2lyY2xlGAMgASgJEhIKCmNvbXBhbnlfaWQYBCABKAUSDwoHbWVzc2FnZRgFIAEoCRIPCgdrZXl3b3JkGAYgASgJEhMKC3JlY2VpdmVkX2F0GAcgASgJIpMBChZHdXBzaHVwQ2FsbGJhY2tSZXF1ZXN0Eg8KB2tleXdvcmQYASABKAkSEQoJcGhvbmVjb2RlGAIgASgJEhAKCGxvY2F0aW9uGAMgASgJEg8KB2NhcnJpZXIYBCABKAkSDwoHY29udGVudBgFIAEoCRIOCgZtc2lzZG4YBiABKAkSEQoJdGltZXN0YW1wGAcgASgJInEKG0RldmljZUJpbmRpbmdTdGF0dXNSZXNwb25zZRJBCgZzdGF0dXMYASABKA4yMS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkRldmljZUJpbmRpbmdTdGF0dXMSDwoHbWVzc2FnZRgCIAEoCSq9AQoTRGV2aWNlQmluZGluZ1N0YXR1cxIhCh1ERVZJQ0VfQklORElOR19TVEFUVVNfVU5LTk9XThAAEiEKHURFVklDRV9CSU5ESU5HX1NUQVRVU19QRU5ESU5HEAESIgoeREVWSUNFX0JJTkRJTkdfU1RBVFVTX0FQUFJPVkVEEAISIgoeREVWSUNFX0JJTkRJTkdfU1RBVFVTX1JFSkVDVEVEEAMSGAoUUkVRVUVTVF9JRF9OT1RfRk9VTkQQBEIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw");

/**
 * @generated from message com.stablemoney.api.identity.DeviceBindingRequest
 */
export type DeviceBindingRequest = Message<"com.stablemoney.api.identity.DeviceBindingRequest"> & {
  /**
   * @generated from field: string device_id = 1;
   */
  deviceId: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.SimData sim_data = 2;
   */
  simData: SimData[];
};

/**
 * Describes the message com.stablemoney.api.identity.DeviceBindingRequest.
 * Use `create(DeviceBindingRequestSchema)` to create a new message.
 */
export const DeviceBindingRequestSchema: GenMessage<DeviceBindingRequest> = /*@__PURE__*/
  messageDesc(file_DeviceBinding, 0);

/**
 * @generated from message com.stablemoney.api.identity.SimData
 */
export type SimData = Message<"com.stablemoney.api.identity.SimData"> & {
  /**
   * @generated from field: string sim_serial_number = 1;
   */
  simSerialNumber: string;

  /**
   * @generated from field: string sim_name = 2;
   */
  simName: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SimData.
 * Use `create(SimDataSchema)` to create a new message.
 */
export const SimDataSchema: GenMessage<SimData> = /*@__PURE__*/
  messageDesc(file_DeviceBinding, 1);

/**
 * @generated from message com.stablemoney.api.identity.DeviceBindingResponse
 */
export type DeviceBindingResponse = Message<"com.stablemoney.api.identity.DeviceBindingResponse"> & {
  /**
   * @generated from field: string message = 1;
   */
  message: string;

  /**
   * @generated from field: string request_id = 2;
   */
  requestId: string;

  /**
   * @generated from field: string mobile_number = 3;
   */
  mobileNumber: string;
};

/**
 * Describes the message com.stablemoney.api.identity.DeviceBindingResponse.
 * Use `create(DeviceBindingResponseSchema)` to create a new message.
 */
export const DeviceBindingResponseSchema: GenMessage<DeviceBindingResponse> = /*@__PURE__*/
  messageDesc(file_DeviceBinding, 2);

/**
 * @generated from message com.stablemoney.api.identity.InboundSmsRequest
 */
export type InboundSmsRequest = Message<"com.stablemoney.api.identity.InboundSmsRequest"> & {
  /**
   * @generated from field: string longcode_number = 1;
   */
  longcodeNumber: string;

  /**
   * @generated from field: string customer_number = 2;
   */
  customerNumber: string;

  /**
   * @generated from field: string price = 8;
   */
  price: string;

  /**
   * @generated from field: string status = 9;
   */
  status: string;

  /**
   * @generated from field: string circle = 3;
   */
  circle: string;

  /**
   * @generated from field: int32 company_id = 4;
   */
  companyId: number;

  /**
   * @generated from field: string message = 5;
   */
  message: string;

  /**
   * @generated from field: string keyword = 6;
   */
  keyword: string;

  /**
   * @generated from field: string received_at = 7;
   */
  receivedAt: string;
};

/**
 * Describes the message com.stablemoney.api.identity.InboundSmsRequest.
 * Use `create(InboundSmsRequestSchema)` to create a new message.
 */
export const InboundSmsRequestSchema: GenMessage<InboundSmsRequest> = /*@__PURE__*/
  messageDesc(file_DeviceBinding, 3);

/**
 * @generated from message com.stablemoney.api.identity.GupshupCallbackRequest
 */
export type GupshupCallbackRequest = Message<"com.stablemoney.api.identity.GupshupCallbackRequest"> & {
  /**
   * @generated from field: string keyword = 1;
   */
  keyword: string;

  /**
   * @generated from field: string phonecode = 2;
   */
  phonecode: string;

  /**
   * @generated from field: string location = 3;
   */
  location: string;

  /**
   * @generated from field: string carrier = 4;
   */
  carrier: string;

  /**
   * @generated from field: string content = 5;
   */
  content: string;

  /**
   * @generated from field: string msisdn = 6;
   */
  msisdn: string;

  /**
   * @generated from field: string timestamp = 7;
   */
  timestamp: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GupshupCallbackRequest.
 * Use `create(GupshupCallbackRequestSchema)` to create a new message.
 */
export const GupshupCallbackRequestSchema: GenMessage<GupshupCallbackRequest> = /*@__PURE__*/
  messageDesc(file_DeviceBinding, 4);

/**
 * @generated from message com.stablemoney.api.identity.DeviceBindingStatusResponse
 */
export type DeviceBindingStatusResponse = Message<"com.stablemoney.api.identity.DeviceBindingStatusResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.DeviceBindingStatus status = 1;
   */
  status: DeviceBindingStatus;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message com.stablemoney.api.identity.DeviceBindingStatusResponse.
 * Use `create(DeviceBindingStatusResponseSchema)` to create a new message.
 */
export const DeviceBindingStatusResponseSchema: GenMessage<DeviceBindingStatusResponse> = /*@__PURE__*/
  messageDesc(file_DeviceBinding, 5);

/**
 * @generated from enum com.stablemoney.api.identity.DeviceBindingStatus
 */
export enum DeviceBindingStatus {
  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_UNKNOWN = 0;
   */
  DEVICE_BINDING_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_PENDING = 1;
   */
  DEVICE_BINDING_STATUS_PENDING = 1,

  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_APPROVED = 2;
   */
  DEVICE_BINDING_STATUS_APPROVED = 2,

  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_REJECTED = 3;
   */
  DEVICE_BINDING_STATUS_REJECTED = 3,

  /**
   * @generated from enum value: REQUEST_ID_NOT_FOUND = 4;
   */
  REQUEST_ID_NOT_FOUND = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.DeviceBindingStatus.
 */
export const DeviceBindingStatusSchema: GenEnum<DeviceBindingStatus> = /*@__PURE__*/
  enumDesc(file_DeviceBinding, 0);

