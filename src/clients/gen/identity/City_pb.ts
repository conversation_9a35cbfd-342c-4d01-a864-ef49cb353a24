// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file City.proto (package com.stablemoney.api.location.city, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file City.proto.
 */
export const file_City: GenFile = /*@__PURE__*/
  fileDesc("CgpDaXR5LnByb3RvEiFjb20uc3RhYmxlbW9uZXkuYXBpLmxvY2F0aW9uLmNpdHkicAoMQ2l0eVJlc3BvbnNlEgoKAmlkGAEgASgJEgwKBG5hbWUYAiABKAkSDQoFc3RhdGUYAyABKAkSDwoHY291bnRyeRgEIAEoCRISCgppc19wb3B1bGFyGAUgASgIEhIKCmFsaWFzX2xpc3QYBiADKAkioQEKEUdldENpdGllc1Jlc3BvbnNlEj8KBmNpdGllcxgBIAMoCzIvLmNvbS5zdGFibGVtb25leS5hcGkubG9jYXRpb24uY2l0eS5DaXR5UmVzcG9uc2USRwoOcG9wdWxhcl9jaXRpZXMYAiADKAsyLy5jb20uc3RhYmxlbW9uZXkuYXBpLmxvY2F0aW9uLmNpdHkuQ2l0eVJlc3BvbnNlOgIYASJjChhHZXRQb3B1bGFyQ2l0aWVzUmVzcG9uc2USRwoOcG9wdWxhcl9jaXRpZXMYAiADKAsyLy5jb20uc3RhYmxlbW9uZXkuYXBpLmxvY2F0aW9uLmNpdHkuQ2l0eVJlc3BvbnNlIlUKElNlYXJjaENpdHlSZXNwb25zZRI/CgZjaXRpZXMYAiADKAsyLy5jb20uc3RhYmxlbW9uZXkuYXBpLmxvY2F0aW9uLmNpdHkuQ2l0eVJlc3BvbnNlQiUKIWNvbS5zdGFibGVtb25leS5hcGkubG9jYXRpb24uY2l0eVABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.location.city.CityResponse
 */
export type CityResponse = Message<"com.stablemoney.api.location.city.CityResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string state = 3;
   */
  state: string;

  /**
   * @generated from field: string country = 4;
   */
  country: string;

  /**
   * @generated from field: bool is_popular = 5;
   */
  isPopular: boolean;

  /**
   * @generated from field: repeated string alias_list = 6;
   */
  aliasList: string[];
};

/**
 * Describes the message com.stablemoney.api.location.city.CityResponse.
 * Use `create(CityResponseSchema)` to create a new message.
 */
export const CityResponseSchema: GenMessage<CityResponse> = /*@__PURE__*/
  messageDesc(file_City, 0);

/**
 * @generated from message com.stablemoney.api.location.city.GetCitiesResponse
 * @deprecated
 */
export type GetCitiesResponse = Message<"com.stablemoney.api.location.city.GetCitiesResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.location.city.CityResponse cities = 1;
   */
  cities: CityResponse[];

  /**
   * @generated from field: repeated com.stablemoney.api.location.city.CityResponse popular_cities = 2;
   */
  popularCities: CityResponse[];
};

/**
 * Describes the message com.stablemoney.api.location.city.GetCitiesResponse.
 * Use `create(GetCitiesResponseSchema)` to create a new message.
 * @deprecated
 */
export const GetCitiesResponseSchema: GenMessage<GetCitiesResponse> = /*@__PURE__*/
  messageDesc(file_City, 1);

/**
 * @generated from message com.stablemoney.api.location.city.GetPopularCitiesResponse
 */
export type GetPopularCitiesResponse = Message<"com.stablemoney.api.location.city.GetPopularCitiesResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.location.city.CityResponse popular_cities = 2;
   */
  popularCities: CityResponse[];
};

/**
 * Describes the message com.stablemoney.api.location.city.GetPopularCitiesResponse.
 * Use `create(GetPopularCitiesResponseSchema)` to create a new message.
 */
export const GetPopularCitiesResponseSchema: GenMessage<GetPopularCitiesResponse> = /*@__PURE__*/
  messageDesc(file_City, 2);

/**
 * @generated from message com.stablemoney.api.location.city.SearchCityResponse
 */
export type SearchCityResponse = Message<"com.stablemoney.api.location.city.SearchCityResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.location.city.CityResponse cities = 2;
   */
  cities: CityResponse[];
};

/**
 * Describes the message com.stablemoney.api.location.city.SearchCityResponse.
 * Use `create(SearchCityResponseSchema)` to create a new message.
 */
export const SearchCityResponseSchema: GenMessage<SearchCityResponse> = /*@__PURE__*/
  messageDesc(file_City, 3);

