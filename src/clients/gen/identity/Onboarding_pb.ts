// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Onboarding.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { KycType, OnBoardingStatus } from "./Kyc_pb.js";
import { file_Kyc } from "./Kyc_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Onboarding.proto.
 */
export const file_Onboarding: GenFile = /*@__PURE__*/
  fileDesc("ChBPbmJvYXJkaW5nLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IlgKG1VzZXJPbmJvYXJkaW5nU3RlcHNSZXNwb25zZRI5CgRkYXRhGAIgAygLMisuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Vc2VyU3RhdGVEYXRhIo8BCg1Vc2VyU3RhdGVEYXRhEj4KD29uYm9hcmRpbmdfc3RlcBgBIAEoDjIlLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuS3ljVHlwZRI+CgZzdGF0dXMYAiABKA4yLi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk9uQm9hcmRpbmdTdGF0dXMiYgodT25ib2FyZGluZ01vZHVsZVN0ZXBzUmVzcG9uc2USQQoEZGF0YRgBIAMoCzIzLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuT25ib2FyZGluZ01vZHVsZVN0ZXBzIpMBChVPbmJvYXJkaW5nTW9kdWxlU3RlcHMSPgoPb25ib2FyZGluZ19zdGVwGAEgASgOMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5LeWNUeXBlEhQKDGt5Y19zZXF1ZW5jZRgCIAEoBRIOCgZtb2R1bGUYAyABKAkSFAoMaXNfc2tpcHBhYmxlGAQgASgIIlQKD09uYm9hcmRpbmdTdGF0ZRI4CgRuZXh0GAEgASgOMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5LeWNUeXBlSACIAQFCBwoFX25leHQiGAoWU2tpcE9uYm9hcmRpbmdSZXNwb25zZSpFChBPbmJvYXJkaW5nTW9kdWxlEh0KGU9OQk9BUkRJTkdfTU9EVUxFX1VOS05PV04QABISCg5BUFBfT05CT0FSRElORxABQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z", [file_Kyc]);

/**
 * @generated from message com.stablemoney.api.identity.UserOnboardingStepsResponse
 */
export type UserOnboardingStepsResponse = Message<"com.stablemoney.api.identity.UserOnboardingStepsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.UserStateData data = 2;
   */
  data: UserStateData[];
};

/**
 * Describes the message com.stablemoney.api.identity.UserOnboardingStepsResponse.
 * Use `create(UserOnboardingStepsResponseSchema)` to create a new message.
 */
export const UserOnboardingStepsResponseSchema: GenMessage<UserOnboardingStepsResponse> = /*@__PURE__*/
  messageDesc(file_Onboarding, 0);

/**
 * @generated from message com.stablemoney.api.identity.UserStateData
 */
export type UserStateData = Message<"com.stablemoney.api.identity.UserStateData"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.KycType onboarding_step = 1;
   */
  onboardingStep: KycType;

  /**
   * @generated from field: com.stablemoney.api.identity.OnBoardingStatus status = 2;
   */
  status: OnBoardingStatus;
};

/**
 * Describes the message com.stablemoney.api.identity.UserStateData.
 * Use `create(UserStateDataSchema)` to create a new message.
 */
export const UserStateDataSchema: GenMessage<UserStateData> = /*@__PURE__*/
  messageDesc(file_Onboarding, 1);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingModuleStepsResponse
 */
export type OnboardingModuleStepsResponse = Message<"com.stablemoney.api.identity.OnboardingModuleStepsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.OnboardingModuleSteps data = 1;
   */
  data: OnboardingModuleSteps[];
};

/**
 * Describes the message com.stablemoney.api.identity.OnboardingModuleStepsResponse.
 * Use `create(OnboardingModuleStepsResponseSchema)` to create a new message.
 */
export const OnboardingModuleStepsResponseSchema: GenMessage<OnboardingModuleStepsResponse> = /*@__PURE__*/
  messageDesc(file_Onboarding, 2);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingModuleSteps
 */
export type OnboardingModuleSteps = Message<"com.stablemoney.api.identity.OnboardingModuleSteps"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.KycType onboarding_step = 1;
   */
  onboardingStep: KycType;

  /**
   * @generated from field: int32 kyc_sequence = 2;
   */
  kycSequence: number;

  /**
   * @generated from field: string module = 3;
   */
  module: string;

  /**
   * @generated from field: bool is_skippable = 4;
   */
  isSkippable: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.OnboardingModuleSteps.
 * Use `create(OnboardingModuleStepsSchema)` to create a new message.
 */
export const OnboardingModuleStepsSchema: GenMessage<OnboardingModuleSteps> = /*@__PURE__*/
  messageDesc(file_Onboarding, 3);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingState
 */
export type OnboardingState = Message<"com.stablemoney.api.identity.OnboardingState"> & {
  /**
   * @generated from field: optional com.stablemoney.api.identity.KycType next = 1;
   */
  next?: KycType;
};

/**
 * Describes the message com.stablemoney.api.identity.OnboardingState.
 * Use `create(OnboardingStateSchema)` to create a new message.
 */
export const OnboardingStateSchema: GenMessage<OnboardingState> = /*@__PURE__*/
  messageDesc(file_Onboarding, 4);

/**
 * @generated from message com.stablemoney.api.identity.SkipOnboardingResponse
 */
export type SkipOnboardingResponse = Message<"com.stablemoney.api.identity.SkipOnboardingResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.SkipOnboardingResponse.
 * Use `create(SkipOnboardingResponseSchema)` to create a new message.
 */
export const SkipOnboardingResponseSchema: GenMessage<SkipOnboardingResponse> = /*@__PURE__*/
  messageDesc(file_Onboarding, 5);

/**
 * @generated from enum com.stablemoney.api.identity.OnboardingModule
 */
export enum OnboardingModule {
  /**
   * @generated from enum value: ONBOARDING_MODULE_UNKNOWN = 0;
   */
  ONBOARDING_MODULE_UNKNOWN = 0,

  /**
   *  FIXED_DEPOSIT = 2; [Deprecated]
   *  MUTUAL_FUND = 3; [Deprecated]
   *  BOND_ONBOARDING = 4; [Deprecated]
   *
   * @generated from enum value: APP_ONBOARDING = 1;
   */
  APP_ONBOARDING = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.OnboardingModule.
 */
export const OnboardingModuleSchema: GenEnum<OnboardingModule> = /*@__PURE__*/
  enumDesc(file_Onboarding, 0);

