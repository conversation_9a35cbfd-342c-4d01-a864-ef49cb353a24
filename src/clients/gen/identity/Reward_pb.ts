// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Reward.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Reward.proto.
 */
export const file_Reward: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp]);

/**
 * @generated from message com.stablemoney.api.identity.VoucherItem
 */
export type VoucherItem = Message<"com.stablemoney.api.identity.VoucherItem"> & {
  /**
   * @generated from field: string voucher_code = 1;
   */
  voucherCode: string;

  /**
   * @generated from field: double amount = 2;
   */
  amount: number;

  /**
   * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 3;
   */
  rewardCode: RewardCode;

  /**
   * @generated from field: com.stablemoney.api.identity.VoucherStatus voucher_status = 4;
   */
  voucherStatus: VoucherStatus;

  /**
   * @generated from field: optional google.protobuf.Timestamp expiry_date = 5;
   */
  expiryDate?: Timestamp;

  /**
   * @generated from field: com.stablemoney.api.identity.VoucherType voucher_type = 6;
   */
  voucherType: VoucherType;
};

/**
 * Describes the message com.stablemoney.api.identity.VoucherItem.
 * Use `create(VoucherItemSchema)` to create a new message.
 */
export const VoucherItemSchema: GenMessage<VoucherItem> = /*@__PURE__*/
  messageDesc(file_Reward, 0);

/**
 * @generated from message com.stablemoney.api.identity.BulkVoucherRequest
 */
export type BulkVoucherRequest = Message<"com.stablemoney.api.identity.BulkVoucherRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.VoucherItem voucher_items_list = 1;
   */
  voucherItemsList: VoucherItem[];
};

/**
 * Describes the message com.stablemoney.api.identity.BulkVoucherRequest.
 * Use `create(BulkVoucherRequestSchema)` to create a new message.
 */
export const BulkVoucherRequestSchema: GenMessage<BulkVoucherRequest> = /*@__PURE__*/
  messageDesc(file_Reward, 1);

/**
 * @generated from message com.stablemoney.api.identity.AddRewardTransactionRequest
 */
export type AddRewardTransactionRequest = Message<"com.stablemoney.api.identity.AddRewardTransactionRequest"> & {
  /**
   * @generated from field: string reward_link = 1;
   */
  rewardLink: string;

  /**
   * @generated from field: string phone_number = 2;
   */
  phoneNumber: string;

  /**
   * @generated from field: google.protobuf.Timestamp transaction_timestamp = 3;
   */
  transactionTimestamp?: Timestamp;
};

/**
 * Describes the message com.stablemoney.api.identity.AddRewardTransactionRequest.
 * Use `create(AddRewardTransactionRequestSchema)` to create a new message.
 */
export const AddRewardTransactionRequestSchema: GenMessage<AddRewardTransactionRequest> = /*@__PURE__*/
  messageDesc(file_Reward, 2);

/**
 * @generated from message com.stablemoney.api.identity.BulkAddRewardTransactionsRequest
 */
export type BulkAddRewardTransactionsRequest = Message<"com.stablemoney.api.identity.BulkAddRewardTransactionsRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.AddRewardTransactionRequest transactions = 1;
   */
  transactions: AddRewardTransactionRequest[];
};

/**
 * Describes the message com.stablemoney.api.identity.BulkAddRewardTransactionsRequest.
 * Use `create(BulkAddRewardTransactionsRequestSchema)` to create a new message.
 */
export const BulkAddRewardTransactionsRequestSchema: GenMessage<BulkAddRewardTransactionsRequest> = /*@__PURE__*/
  messageDesc(file_Reward, 3);

/**
 * @generated from message com.stablemoney.api.identity.ManualAddRewardRequest
 */
export type ManualAddRewardRequest = Message<"com.stablemoney.api.identity.ManualAddRewardRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 1;
   */
  rewardCode: RewardCode;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;

  /**
   * @generated from field: optional string referrer_user_id = 3;
   */
  referrerUserId?: string;

  /**
   * @generated from field: double amount = 4;
   */
  amount: number;

  /**
   * @generated from field: string bank_id = 5;
   */
  bankId: string;

  /**
   * @generated from field: optional bool consume_booking = 6;
   */
  consumeBooking?: boolean;

  /**
   * @generated from field: optional bool override_payment_time_check = 7;
   */
  overridePaymentTimeCheck?: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.ManualAddRewardRequest.
 * Use `create(ManualAddRewardRequestSchema)` to create a new message.
 */
export const ManualAddRewardRequestSchema: GenMessage<ManualAddRewardRequest> = /*@__PURE__*/
  messageDesc(file_Reward, 4);

/**
 * @generated from message com.stablemoney.api.identity.CheckGoldenTicketRequest
 */
export type CheckGoldenTicketRequest = Message<"com.stablemoney.api.identity.CheckGoldenTicketRequest"> & {
  /**
   * @generated from field: repeated string userIdList = 1;
   */
  userIdList: string[];
};

/**
 * Describes the message com.stablemoney.api.identity.CheckGoldenTicketRequest.
 * Use `create(CheckGoldenTicketRequestSchema)` to create a new message.
 */
export const CheckGoldenTicketRequestSchema: GenMessage<CheckGoldenTicketRequest> = /*@__PURE__*/
  messageDesc(file_Reward, 5);

/**
 * @generated from message com.stablemoney.api.identity.RewardItem
 */
export type RewardItem = Message<"com.stablemoney.api.identity.RewardItem"> & {
  /**
   * @generated from field: string reward_link = 1;
   */
  rewardLink: string;

  /**
   * @generated from field: google.protobuf.Timestamp reward_sent_timestamp = 2;
   */
  rewardSentTimestamp?: Timestamp;

  /**
   * @generated from field: double reward_amount = 3;
   */
  rewardAmount: number;

  /**
   * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 4;
   */
  rewardCode: RewardCode;

  /**
   * @generated from field: com.stablemoney.api.identity.RewardDeliveryStatus reward_delivery_status = 5;
   */
  rewardDeliveryStatus: RewardDeliveryStatus;
};

/**
 * Describes the message com.stablemoney.api.identity.RewardItem.
 * Use `create(RewardItemSchema)` to create a new message.
 */
export const RewardItemSchema: GenMessage<RewardItem> = /*@__PURE__*/
  messageDesc(file_Reward, 6);

/**
 * @generated from message com.stablemoney.api.identity.RewardsDataResponse
 */
export type RewardsDataResponse = Message<"com.stablemoney.api.identity.RewardsDataResponse"> & {
  /**
   * @generated from field: double total_earnings = 1;
   */
  totalEarnings: number;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.RewardItem rewards = 2;
   */
  rewards: RewardItem[];
};

/**
 * Describes the message com.stablemoney.api.identity.RewardsDataResponse.
 * Use `create(RewardsDataResponseSchema)` to create a new message.
 */
export const RewardsDataResponseSchema: GenMessage<RewardsDataResponse> = /*@__PURE__*/
  messageDesc(file_Reward, 7);

/**
 * @generated from message com.stablemoney.api.identity.RewardTypesResponse
 */
export type RewardTypesResponse = Message<"com.stablemoney.api.identity.RewardTypesResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.RewardType reward_types = 1;
   */
  rewardTypes: RewardType[];
};

/**
 * Describes the message com.stablemoney.api.identity.RewardTypesResponse.
 * Use `create(RewardTypesResponseSchema)` to create a new message.
 */
export const RewardTypesResponseSchema: GenMessage<RewardTypesResponse> = /*@__PURE__*/
  messageDesc(file_Reward, 8);

/**
 * @generated from message com.stablemoney.api.identity.RewardTypesRequest
 */
export type RewardTypesRequest = Message<"com.stablemoney.api.identity.RewardTypesRequest"> & {
  /**
   * @generated from field: optional com.stablemoney.api.identity.RewardCode rewardCode = 1;
   */
  rewardCode?: RewardCode;
};

/**
 * Describes the message com.stablemoney.api.identity.RewardTypesRequest.
 * Use `create(RewardTypesRequestSchema)` to create a new message.
 */
export const RewardTypesRequestSchema: GenMessage<RewardTypesRequest> = /*@__PURE__*/
  messageDesc(file_Reward, 9);

/**
 * @generated from message com.stablemoney.api.identity.RewardType
 */
export type RewardType = Message<"com.stablemoney.api.identity.RewardType"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: double amount = 2;
   */
  amount: number;

  /**
   * @generated from field: uint32 max_count = 3;
   */
  maxCount: number;

  /**
   * @generated from field: double min_transaction_amount = 4;
   */
  minTransactionAmount: number;

  /**
   * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 5;
   */
  rewardCode: RewardCode;

  /**
   * @generated from field: optional uint64 valid_from = 6;
   */
  validFrom?: bigint;

  /**
   * @generated from field: optional uint64 valid_till = 7;
   */
  validTill?: bigint;

  /**
   * @generated from field: optional double instant_reward_tr_amount = 8;
   */
  instantRewardTrAmount?: number;

  /**
   * @generated from field: optional uint32 delivery_delay_in_days = 9;
   */
  deliveryDelayInDays?: number;

  /**
   * @generated from field: optional float referral_percent = 10;
   */
  referralPercent?: number;

  /**
   * @generated from field: optional uint32 referral_level = 11;
   */
  referralLevel?: number;

  /**
   * @generated from field: optional double referer_min_net_worth = 12;
   */
  refererMinNetWorth?: number;

  /**
   * @generated from field: optional double golden_ticket_amount = 13;
   */
  goldenTicketAmount?: number;

  /**
   * @generated from field: optional uint32 randomized_reward_percentage = 14;
   */
  randomizedRewardPercentage?: number;

  /**
   * @generated from field: optional uint32 referral_count = 15;
   */
  referralCount?: number;
};

/**
 * Describes the message com.stablemoney.api.identity.RewardType.
 * Use `create(RewardTypeSchema)` to create a new message.
 */
export const RewardTypeSchema: GenMessage<RewardType> = /*@__PURE__*/
  messageDesc(file_Reward, 10);

/**
 * @generated from enum com.stablemoney.api.identity.VoucherType
 */
export enum VoucherType {
  /**
   * @generated from enum value: VOUCHER_TYPE_UNKNOWN = 0;
   */
  VOUCHER_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: AMAZON_PAY_VOUCHER = 1;
   */
  AMAZON_PAY_VOUCHER = 1,

  /**
   * @generated from enum value: AMAZON_VOUCHER_CODE = 2;
   */
  AMAZON_VOUCHER_CODE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.VoucherType.
 */
export const VoucherTypeSchema: GenEnum<VoucherType> = /*@__PURE__*/
  enumDesc(file_Reward, 0);

/**
 * @generated from enum com.stablemoney.api.identity.RewardCode
 */
export enum RewardCode {
  /**
   * @generated from enum value: REWARD_CODE_UNKNOWN = 0;
   */
  REWARD_CODE_UNKNOWN = 0,

  /**
   * @generated from enum value: FIRST_FD_REWARD = 1;
   */
  FIRST_FD_REWARD = 1,

  /**
   * @generated from enum value: REFERRAL_REWARD = 2;
   */
  REFERRAL_REWARD = 2,

  /**
   * @generated from enum value: INVESTMENT_BASED_REFERRAL_REWARD = 3;
   */
  INVESTMENT_BASED_REFERRAL_REWARD = 3,

  /**
   * @generated from enum value: REFEREE_FIRST_FD_REWARD = 4;
   */
  REFEREE_FIRST_FD_REWARD = 4,

  /**
   * @generated from enum value: EMERGENCY_FUND_REFERRAL_REWARD = 5;
   */
  EMERGENCY_FUND_REFERRAL_REWARD = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.RewardCode.
 */
export const RewardCodeSchema: GenEnum<RewardCode> = /*@__PURE__*/
  enumDesc(file_Reward, 1);

/**
 * @generated from enum com.stablemoney.api.identity.VoucherStatus
 */
export enum VoucherStatus {
  /**
   * @generated from enum value: VOUCHER_STATUS_UNKNOWN = 0;
   */
  VOUCHER_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: SENT = 1;
   */
  SENT = 1,

  /**
   * @generated from enum value: AVAILABLE = 2;
   */
  AVAILABLE = 2,

  /**
   * @generated from enum value: EXPIRED = 3;
   */
  EXPIRED = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.VoucherStatus.
 */
export const VoucherStatusSchema: GenEnum<VoucherStatus> = /*@__PURE__*/
  enumDesc(file_Reward, 2);

/**
 * @generated from enum com.stablemoney.api.identity.RewardDeliveryStatus
 */
export enum RewardDeliveryStatus {
  /**
   * @generated from enum value: REWARD_PROCESSING_STATUS_UNKNOWN = 0;
   */
  REWARD_PROCESSING_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: QUEUED = 1;
   */
  QUEUED = 1,

  /**
   * @generated from enum value: FAILED = 2;
   */
  FAILED = 2,

  /**
   * @generated from enum value: SENT_FROM_BACKEND = 3;
   */
  SENT_FROM_BACKEND = 3,

  /**
   * @generated from enum value: DELIVERED = 4;
   */
  DELIVERED = 4,

  /**
   * @generated from enum value: OPENED = 5;
   */
  OPENED = 5,

  /**
   * @generated from enum value: REWARD_TO_BE_SENT = 6;
   */
  REWARD_TO_BE_SENT = 6,

  /**
   * @generated from enum value: REWARD_NOT_ELIGIBLE = 7;
   */
  REWARD_NOT_ELIGIBLE = 7,
}

/**
 * Describes the enum com.stablemoney.api.identity.RewardDeliveryStatus.
 */
export const RewardDeliveryStatusSchema: GenEnum<RewardDeliveryStatus> = /*@__PURE__*/
  enumDesc(file_Reward, 3);

/**
 * @generated from enum com.stablemoney.api.identity.BlacklistUserType
 */
export enum BlacklistUserType {
  /**
   * @generated from enum value: BLACKLIST_USER_TYPE_UNKNOWN = 0;
   */
  BLACKLIST_USER_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: INTERNAL = 1;
   */
  INTERNAL = 1,

  /**
   * @generated from enum value: EXTERNAL = 2;
   */
  EXTERNAL = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.BlacklistUserType.
 */
export const BlacklistUserTypeSchema: GenEnum<BlacklistUserType> = /*@__PURE__*/
  enumDesc(file_Reward, 4);

/**
 * @generated from enum com.stablemoney.api.identity.ReferralStatus
 */
export enum ReferralStatus {
  /**
   * @generated from enum value: REFERRAL_STATUS_UNKNOWN = 0;
   */
  REFERRAL_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: BOOKING_NOT_INITIATED = 1;
   */
  BOOKING_NOT_INITIATED = 1,

  /**
   * @generated from enum value: BOOKING_DONE = 2;
   */
  BOOKING_DONE = 2,

  /**
   * @generated from enum value: REWARD_PROCESSED = 3;
   */
  REWARD_PROCESSED = 3,

  /**
   * @generated from enum value: REWARD_NOT_APPLICABLE = 4;
   */
  REWARD_NOT_APPLICABLE = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.ReferralStatus.
 */
export const ReferralStatusSchema: GenEnum<ReferralStatus> = /*@__PURE__*/
  enumDesc(file_Reward, 5);

