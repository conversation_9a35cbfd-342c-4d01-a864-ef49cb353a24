// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Kyc.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Kyc.proto.
 */
export const file_Kyc: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message com.stablemoney.api.identity.GenerateTokenRequest
 */
export type GenerateTokenRequest = Message<"com.stablemoney.api.identity.GenerateTokenRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.GenerateTokenRequest.
 * Use `create(GenerateTokenRequestSchema)` to create a new message.
 */
export const GenerateTokenRequestSchema: GenMessage<GenerateTokenRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 0);

/**
 * @generated from message com.stablemoney.api.identity.GenerateTokenResponse
 */
export type GenerateTokenResponse = Message<"com.stablemoney.api.identity.GenerateTokenResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string access_token = 2;
   */
  accessToken: string;

  /**
   * @generated from field: string customer_identifier = 3;
   */
  customerIdentifier: string;

  /**
   * @generated from field: bool is_new = 4;
   */
  isNew: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.GenerateTokenResponse.
 * Use `create(GenerateTokenResponseSchema)` to create a new message.
 */
export const GenerateTokenResponseSchema: GenMessage<GenerateTokenResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 1);

/**
 * @generated from message com.stablemoney.api.identity.InitiateKycRequest
 */
export type InitiateKycRequest = Message<"com.stablemoney.api.identity.InitiateKycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.KycType kyc_type = 1;
   */
  kycType: KycType;

  /**
   * @generated from oneof com.stablemoney.api.identity.InitiateKycRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.PanKycRequest pan_kyc_request = 2;
     */
    value: PanKycRequest;
    case: "panKycRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.WetSignatureRequest wet_signature_request = 3;
     */
    value: WetSignatureRequest;
    case: "wetSignatureRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.SelfieRequest selfie_request = 4;
     */
    value: SelfieRequest;
    case: "selfieRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.KycRequest kyc_request = 5;
     */
    value: KycRequest;
    case: "kycRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.EsignRequest esign_request = 6;
     */
    value: EsignRequest;
    case: "esignRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateKycRequest.
 * Use `create(InitiateKycRequestSchema)` to create a new message.
 */
export const InitiateKycRequestSchema: GenMessage<InitiateKycRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 2);

/**
 * @generated from message com.stablemoney.api.identity.EsignRequest
 */
export type EsignRequest = Message<"com.stablemoney.api.identity.EsignRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.EsignStep step = 1;
   */
  step: EsignStep;

  /**
   * @generated from oneof com.stablemoney.api.identity.EsignRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.GenerateTokenRequest generate_token_request = 2;
     */
    value: GenerateTokenRequest;
    case: "generateTokenRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.StatusRequest status_request = 3;
     */
    value: StatusRequest;
    case: "statusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.EsignRequest.
 * Use `create(EsignRequestSchema)` to create a new message.
 */
export const EsignRequestSchema: GenMessage<EsignRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 3);

/**
 * @generated from message com.stablemoney.api.identity.KycRequest
 */
export type KycRequest = Message<"com.stablemoney.api.identity.KycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.KycStep step = 1;
   */
  step: KycStep;

  /**
   * @generated from oneof com.stablemoney.api.identity.KycRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.GenerateTokenRequest generate_token_request = 2;
     */
    value: GenerateTokenRequest;
    case: "generateTokenRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.StatusRequest status_request = 3;
     */
    value: StatusRequest;
    case: "statusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.KycRequest.
 * Use `create(KycRequestSchema)` to create a new message.
 */
export const KycRequestSchema: GenMessage<KycRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 4);

/**
 * @generated from message com.stablemoney.api.identity.InitiateKycResponse
 */
export type InitiateKycResponse = Message<"com.stablemoney.api.identity.InitiateKycResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.identity.InitiateKycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.PanKycResponse pan_kyc_response = 1;
     */
    value: PanKycResponse;
    case: "panKycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.WetSignatureResponse wet_signature_response = 2;
     */
    value: WetSignatureResponse;
    case: "wetSignatureResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.SelfieResponse selfie_response = 3;
     */
    value: SelfieResponse;
    case: "selfieResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.KycResponse kyc_response = 4;
     */
    value: KycResponse;
    case: "kycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.EsignKycResponse esign_response = 5;
     */
    value: EsignKycResponse;
    case: "esignResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.InitiateKycResponse.
 * Use `create(InitiateKycResponseSchema)` to create a new message.
 */
export const InitiateKycResponseSchema: GenMessage<InitiateKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 5);

/**
 * @generated from message com.stablemoney.api.identity.EsignKycResponse
 */
export type EsignKycResponse = Message<"com.stablemoney.api.identity.EsignKycResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.identity.EsignKycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.GenerateTokenResponse generate_token_response = 1;
     */
    value: GenerateTokenResponse;
    case: "generateTokenResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.StatusResponse status_response = 2;
     */
    value: StatusResponse;
    case: "statusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.EsignKycResponse.
 * Use `create(EsignKycResponseSchema)` to create a new message.
 */
export const EsignKycResponseSchema: GenMessage<EsignKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 6);

/**
 * @generated from message com.stablemoney.api.identity.KycResponse
 */
export type KycResponse = Message<"com.stablemoney.api.identity.KycResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.identity.KycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.GenerateTokenResponse generate_token_response = 1;
     */
    value: GenerateTokenResponse;
    case: "generateTokenResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.StatusResponse status_response = 2;
     */
    value: StatusResponse;
    case: "statusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.KycResponse.
 * Use `create(KycResponseSchema)` to create a new message.
 */
export const KycResponseSchema: GenMessage<KycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 7);

/**
 * @generated from message com.stablemoney.api.identity.SelfieRequest
 */
export type SelfieRequest = Message<"com.stablemoney.api.identity.SelfieRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.SelfieKycStep step = 1;
   */
  step: SelfieKycStep;

  /**
   * @generated from oneof com.stablemoney.api.identity.SelfieRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.StartSelfiStepRequest start_selfi_step_request = 2;
     */
    value: StartSelfiStepRequest;
    case: "startSelfiStepRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.SetSelfiStatusRequest set_selfi_status_request = 3;
     */
    value: SetSelfiStatusRequest;
    case: "setSelfiStatusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.SelfieRequest.
 * Use `create(SelfieRequestSchema)` to create a new message.
 */
export const SelfieRequestSchema: GenMessage<SelfieRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 8);

/**
 * @generated from message com.stablemoney.api.identity.StartSelfiStepRequest
 */
export type StartSelfiStepRequest = Message<"com.stablemoney.api.identity.StartSelfiStepRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.StartSelfiStepRequest.
 * Use `create(StartSelfiStepRequestSchema)` to create a new message.
 */
export const StartSelfiStepRequestSchema: GenMessage<StartSelfiStepRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 9);

/**
 * @generated from message com.stablemoney.api.identity.SetSelfiStatusRequest
 */
export type SetSelfiStatusRequest = Message<"com.stablemoney.api.identity.SetSelfiStatusRequest"> & {
  /**
   * @generated from field: string transaction_id = 1;
   */
  transactionId: string;

  /**
   * @generated from field: string status = 2;
   */
  status: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SetSelfiStatusRequest.
 * Use `create(SetSelfiStatusRequestSchema)` to create a new message.
 */
export const SetSelfiStatusRequestSchema: GenMessage<SetSelfiStatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 10);

/**
 * @generated from message com.stablemoney.api.identity.SelfieResponse
 */
export type SelfieResponse = Message<"com.stablemoney.api.identity.SelfieResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.identity.SelfieResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.StartSelfiStepResponse start_selfi_step_response = 1;
     */
    value: StartSelfiStepResponse;
    case: "startSelfiStepResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.SetSelfiStatusResponse set_selfi_status_response = 2;
     */
    value: SetSelfiStatusResponse;
    case: "setSelfiStatusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.SelfieResponse.
 * Use `create(SelfieResponseSchema)` to create a new message.
 */
export const SelfieResponseSchema: GenMessage<SelfieResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 11);

/**
 * @generated from message com.stablemoney.api.identity.StartSelfiStepResponse
 */
export type StartSelfiStepResponse = Message<"com.stablemoney.api.identity.StartSelfiStepResponse"> & {
  /**
   * @generated from field: string selfie = 1;
   */
  selfie: string;

  /**
   * @generated from field: string workflow_id = 2;
   */
  workflowId: string;

  /**
   * @generated from field: string access_token = 3;
   */
  accessToken: string;

  /**
   * @generated from field: string transaction_id = 4;
   */
  transactionId: string;
};

/**
 * Describes the message com.stablemoney.api.identity.StartSelfiStepResponse.
 * Use `create(StartSelfiStepResponseSchema)` to create a new message.
 */
export const StartSelfiStepResponseSchema: GenMessage<StartSelfiStepResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 12);

/**
 * @generated from message com.stablemoney.api.identity.SetSelfiStatusResponse
 */
export type SetSelfiStatusResponse = Message<"com.stablemoney.api.identity.SetSelfiStatusResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.SetSelfiStatusResponse.
 * Use `create(SetSelfiStatusResponseSchema)` to create a new message.
 */
export const SetSelfiStatusResponseSchema: GenMessage<SetSelfiStatusResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 13);

/**
 * @generated from message com.stablemoney.api.identity.PanKycResponse
 */
export type PanKycResponse = Message<"com.stablemoney.api.identity.PanKycResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.PanKycStep step = 1;
   */
  step: PanKycStep;

  /**
   * @generated from oneof com.stablemoney.api.identity.PanKycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.KycFetchNameByPanResponse kyc_fetch_name_by_pan_response = 2;
     */
    value: KycFetchNameByPanResponse;
    case: "kycFetchNameByPanResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse kyc_validate_name_and_get_pan_status_response = 3;
     */
    value: KycValidateNameAndGetPanStatusResponse;
    case: "kycValidateNameAndGetPanStatusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.PanKycResponse.
 * Use `create(PanKycResponseSchema)` to create a new message.
 */
export const PanKycResponseSchema: GenMessage<PanKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 14);

/**
 * @generated from message com.stablemoney.api.identity.PanKycRequest
 */
export type PanKycRequest = Message<"com.stablemoney.api.identity.PanKycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.PanKycStep step = 1;
   */
  step: PanKycStep;

  /**
   * @generated from oneof com.stablemoney.api.identity.PanKycRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.identity.KycFetchNameByPanRequest kyc_fetch_name_by_pan_request = 2;
     */
    value: KycFetchNameByPanRequest;
    case: "kycFetchNameByPanRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest kyc_validate_name_and_get_kyc_status_request = 3;
     */
    value: KycValidateNameAndGetKycStatusRequest;
    case: "kycValidateNameAndGetKycStatusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.PanKycRequest.
 * Use `create(PanKycRequestSchema)` to create a new message.
 */
export const PanKycRequestSchema: GenMessage<PanKycRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 15);

/**
 * @generated from message com.stablemoney.api.identity.KycFetchNameByPanResponse
 */
export type KycFetchNameByPanResponse = Message<"com.stablemoney.api.identity.KycFetchNameByPanResponse"> & {
  /**
   * @generated from field: string pan = 1;
   */
  pan: string;

  /**
   * @generated from field: string full_name = 2;
   */
  fullName: string;
};

/**
 * Describes the message com.stablemoney.api.identity.KycFetchNameByPanResponse.
 * Use `create(KycFetchNameByPanResponseSchema)` to create a new message.
 */
export const KycFetchNameByPanResponseSchema: GenMessage<KycFetchNameByPanResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 16);

/**
 * @generated from message com.stablemoney.api.identity.KycFetchNameByPanRequest
 */
export type KycFetchNameByPanRequest = Message<"com.stablemoney.api.identity.KycFetchNameByPanRequest"> & {
  /**
   * @generated from field: string pan = 1;
   */
  pan: string;
};

/**
 * Describes the message com.stablemoney.api.identity.KycFetchNameByPanRequest.
 * Use `create(KycFetchNameByPanRequestSchema)` to create a new message.
 */
export const KycFetchNameByPanRequestSchema: GenMessage<KycFetchNameByPanRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 17);

/**
 * @generated from message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusRequest
 */
export type KycValidateNameAndGetPanStatusRequest = Message<"com.stablemoney.api.identity.KycValidateNameAndGetPanStatusRequest"> & {
  /**
   * @generated from field: bool is_name_match = 1;
   */
  isNameMatch: boolean;

  /**
   * @generated from field: string dob = 2;
   */
  dob: string;
};

/**
 * Describes the message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusRequest.
 * Use `create(KycValidateNameAndGetPanStatusRequestSchema)` to create a new message.
 */
export const KycValidateNameAndGetPanStatusRequestSchema: GenMessage<KycValidateNameAndGetPanStatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 18);

/**
 * @generated from message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse
 */
export type KycValidateNameAndGetPanStatusResponse = Message<"com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse"> & {
  /**
   * @generated from field: bool pan_kyc_status = 1;
   */
  panKycStatus: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse.
 * Use `create(KycValidateNameAndGetPanStatusResponseSchema)` to create a new message.
 */
export const KycValidateNameAndGetPanStatusResponseSchema: GenMessage<KycValidateNameAndGetPanStatusResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 19);

/**
 * @generated from message com.stablemoney.api.identity.StatusRequest
 */
export type StatusRequest = Message<"com.stablemoney.api.identity.StatusRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message com.stablemoney.api.identity.StatusRequest.
 * Use `create(StatusRequestSchema)` to create a new message.
 */
export const StatusRequestSchema: GenMessage<StatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 20);

/**
 * @generated from message com.stablemoney.api.identity.StatusResponse
 */
export type StatusResponse = Message<"com.stablemoney.api.identity.StatusResponse"> & {
  /**
   * @generated from field: string kyc_status = 1;
   */
  kycStatus: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: string digilocker_status = 3;
   */
  digilockerStatus: string;
};

/**
 * Describes the message com.stablemoney.api.identity.StatusResponse.
 * Use `create(StatusResponseSchema)` to create a new message.
 */
export const StatusResponseSchema: GenMessage<StatusResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 21);

/**
 * @generated from message com.stablemoney.api.identity.WetSignatureRequest
 */
export type WetSignatureRequest = Message<"com.stablemoney.api.identity.WetSignatureRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.RaaDuration raa_duration = 1;
   */
  raaDuration: RaaDuration;

  /**
   * @generated from field: bool is_pep = 2;
   */
  isPep: boolean;

  /**
   * @generated from field: bool is_indian_citizen = 3;
   */
  isIndianCitizen: boolean;

  /**
   * @generated from field: string document_id = 4;
   */
  documentId: string;

  /**
   * @generated from field: bool credit_report_consent = 5;
   */
  creditReportConsent: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.WetSignatureRequest.
 * Use `create(WetSignatureRequestSchema)` to create a new message.
 */
export const WetSignatureRequestSchema: GenMessage<WetSignatureRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 22);

/**
 * @generated from message com.stablemoney.api.identity.WetSignatureResponse
 */
export type WetSignatureResponse = Message<"com.stablemoney.api.identity.WetSignatureResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.WetSignatureResponse.
 * Use `create(WetSignatureResponseSchema)` to create a new message.
 */
export const WetSignatureResponseSchema: GenMessage<WetSignatureResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 23);

/**
 * @generated from message com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest
 */
export type KycValidateNameAndGetKycStatusRequest = Message<"com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest"> & {
  /**
   * @generated from field: bool is_name_match = 1;
   */
  isNameMatch: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest.
 * Use `create(KycValidateNameAndGetKycStatusRequestSchema)` to create a new message.
 */
export const KycValidateNameAndGetKycStatusRequestSchema: GenMessage<KycValidateNameAndGetKycStatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 24);

/**
 * @generated from enum com.stablemoney.api.identity.KycType
 */
export enum KycType {
  /**
   * @generated from enum value: KYC_TYPE_UNKNOWN = 0;
   */
  KYC_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: POI = 1;
   */
  POI = 1,

  /**
   * @generated from enum value: KYC = 2;
   */
  KYC = 2,

  /**
   * @generated from enum value: POA = 3;
   */
  POA = 3,

  /**
   * @generated from enum value: SELFIE = 4;
   */
  SELFIE = 4,

  /**
   * @generated from enum value: ESIGN = 5;
   */
  ESIGN = 5,

  /**
   * @generated from enum value: BANK_ACCOUNT = 6;
   */
  BANK_ACCOUNT = 6,

  /**
   * @generated from enum value: DEMAT_ACCOUNT = 7;
   */
  DEMAT_ACCOUNT = 7,

  /**
   * @generated from enum value: NOMINEE = 8;
   */
  NOMINEE = 8,

  /**
   * @generated from enum value: WET_SIGNATURE = 9;
   */
  WET_SIGNATURE = 9,

  /**
   * @generated from enum value: USER_PROFILE = 10;
   */
  USER_PROFILE = 10,

  /**
   * @generated from enum value: PAN_KYC = 11;
   */
  PAN_KYC = 11,

  /**
   * @generated from enum value: NAME = 12;
   */
  NAME = 12,

  /**
   * @generated from enum value: QUESTIONNAIRE = 13;
   */
  QUESTIONNAIRE = 13,

  /**
   * @generated from enum value: EMAIL = 14;
   */
  EMAIL = 14,

  /**
   * @generated from enum value: WHITELIST_CHECK = 15;
   */
  WHITELIST_CHECK = 15,
}

/**
 * Describes the enum com.stablemoney.api.identity.KycType.
 */
export const KycTypeSchema: GenEnum<KycType> = /*@__PURE__*/
  enumDesc(file_Kyc, 0);

/**
 * @generated from enum com.stablemoney.api.identity.ProofType
 */
export enum ProofType {
  /**
   * @generated from enum value: PROOF_TYPE_UNKNOWN = 0;
   */
  PROOF_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: PAN = 1;
   */
  PAN = 1,

  /**
   * @generated from enum value: AADHAR = 2;
   */
  AADHAR = 2,

  /**
   * @generated from enum value: PASSPORT = 3;
   */
  PASSPORT = 3,

  /**
   * @generated from enum value: DRIVING_LICENSE = 4;
   */
  DRIVING_LICENSE = 4,

  /**
   * @generated from enum value: VOTER_ID = 5;
   */
  VOTER_ID = 5,

  /**
   * @generated from enum value: GOVT_ID = 6;
   */
  GOVT_ID = 6,

  /**
   * @generated from enum value: REGULATORY_ID = 7;
   */
  REGULATORY_ID = 7,

  /**
   * @generated from enum value: PSU_ID = 8;
   */
  PSU_ID = 8,

  /**
   * @generated from enum value: BANK_ID = 9;
   */
  BANK_ID = 9,

  /**
   * @generated from enum value: PUBLIC_FINANCIAL_INSTITUTION_ID = 10;
   */
  PUBLIC_FINANCIAL_INSTITUTION_ID = 10,

  /**
   * @generated from enum value: COLLEGE_ID = 11;
   */
  COLLEGE_ID = 11,

  /**
   * @generated from enum value: PROFESSIONAL_BODY_ID = 12;
   */
  PROFESSIONAL_BODY_ID = 12,

  /**
   * @generated from enum value: CREDIT_CARD = 13;
   */
  CREDIT_CARD = 13,

  /**
   * @generated from enum value: OTHER_ID = 16;
   */
  OTHER_ID = 16,

  /**
   * @generated from enum value: BANK_PASSBOOK = 17;
   */
  BANK_PASSBOOK = 17,

  /**
   * @generated from enum value: BANK_ACCOUNT_STATEMENT = 18;
   */
  BANK_ACCOUNT_STATEMENT = 18,

  /**
   * @generated from enum value: RATION_CARD = 19;
   */
  RATION_CARD = 19,

  /**
   * @generated from enum value: LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20;
   */
  LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20,

  /**
   * @generated from enum value: LAND_LINE_TELEPHONE_BILL = 21;
   */
  LAND_LINE_TELEPHONE_BILL = 21,

  /**
   * @generated from enum value: ELECTRICITY_BILL = 22;
   */
  ELECTRICITY_BILL = 22,

  /**
   * @generated from enum value: GAS_BILL = 23;
   */
  GAS_BILL = 23,

  /**
   * @generated from enum value: FLAT_MAINTENANCE_BILL = 24;
   */
  FLAT_MAINTENANCE_BILL = 24,

  /**
   * @generated from enum value: INSURANCE_COPY = 25;
   */
  INSURANCE_COPY = 25,

  /**
   * @generated from enum value: SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26;
   */
  SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26,

  /**
   * @generated from enum value: POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27;
   */
  POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27,

  /**
   * @generated from enum value: POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28;
   */
  POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28,

  /**
   * @generated from enum value: POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29;
   */
  POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29,

  /**
   * @generated from enum value: POA_ISSUED_BY_PARLIAMENT = 30;
   */
  POA_ISSUED_BY_PARLIAMENT = 30,

  /**
   * @generated from enum value: POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31;
   */
  POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31,

  /**
   * @generated from enum value: POA_ISSUED_BY_NOTARY_PUBLIC = 32;
   */
  POA_ISSUED_BY_NOTARY_PUBLIC = 32,

  /**
   * @generated from enum value: POA_ISSUED_BY_GAZETTED_OFFICER = 33;
   */
  POA_ISSUED_BY_GAZETTED_OFFICER = 33,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34;
   */
  ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35;
   */
  ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36;
   */
  ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37;
   */
  ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38;
   */
  ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39;
   */
  ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40;
   */
  ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40,
}

/**
 * Describes the enum com.stablemoney.api.identity.ProofType.
 */
export const ProofTypeSchema: GenEnum<ProofType> = /*@__PURE__*/
  enumDesc(file_Kyc, 1);

/**
 * @generated from enum com.stablemoney.api.identity.KycProvider
 */
export enum KycProvider {
  /**
   * @generated from enum value: KYC_PROVIDER_UNKNOWN = 0;
   */
  KYC_PROVIDER_UNKNOWN = 0,

  /**
   * @generated from enum value: NONE = 1;
   */
  NONE = 1,

  /**
   * @generated from enum value: CDSL = 2;
   */
  CDSL = 2,

  /**
   * @generated from enum value: DIGIO = 3;
   */
  DIGIO = 3,

  /**
   * @generated from enum value: HYPERVERGE = 4;
   */
  HYPERVERGE = 4,

  /**
   * @generated from enum value: TARRAKKI = 5;
   */
  TARRAKKI = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.KycProvider.
 */
export const KycProviderSchema: GenEnum<KycProvider> = /*@__PURE__*/
  enumDesc(file_Kyc, 2);

/**
 * @generated from enum com.stablemoney.api.identity.OnBoardingStatus
 */
export enum OnBoardingStatus {
  /**
   * @generated from enum value: ONBOARDING_STATUS_UNKNOWN = 0;
   */
  ONBOARDING_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: COMPLETE = 2;
   */
  COMPLETE = 2,

  /**
   * @generated from enum value: REJECTED = 3;
   */
  REJECTED = 3,

  /**
   * @generated from enum value: SKIPPED = 4;
   */
  SKIPPED = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.OnBoardingStatus.
 */
export const OnBoardingStatusSchema: GenEnum<OnBoardingStatus> = /*@__PURE__*/
  enumDesc(file_Kyc, 3);

/**
 * @generated from enum com.stablemoney.api.identity.EsignStep
 */
export enum EsignStep {
  /**
   * @generated from enum value: ESIGN_STEP_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ESIGN_STEP_GENERATE_TOKEN = 1;
   */
  GENERATE_TOKEN = 1,

  /**
   * @generated from enum value: ESIGN_STEP_STATUS = 2;
   */
  STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.EsignStep.
 */
export const EsignStepSchema: GenEnum<EsignStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 4);

/**
 * @generated from enum com.stablemoney.api.identity.PanKycStep
 */
export enum PanKycStep {
  /**
   * @generated from enum value: UNKNOWN_PAN_STEP = 0;
   */
  UNKNOWN_PAN_STEP = 0,

  /**
   * @generated from enum value: NAME_FETCH = 1;
   */
  NAME_FETCH = 1,

  /**
   * @generated from enum value: PAN_STATUS = 2;
   */
  PAN_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.PanKycStep.
 */
export const PanKycStepSchema: GenEnum<PanKycStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 5);

/**
 * @generated from enum com.stablemoney.api.identity.KycStep
 */
export enum KycStep {
  /**
   * @generated from enum value: UNKNOWN_STEP = 0;
   */
  UNKNOWN_STEP = 0,

  /**
   * @generated from enum value: GENERATE_TOKEN = 1;
   */
  GENERATE_TOKEN = 1,

  /**
   * @generated from enum value: GET_STATUS = 2;
   */
  GET_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.KycStep.
 */
export const KycStepSchema: GenEnum<KycStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 6);

/**
 * @generated from enum com.stablemoney.api.identity.SelfieKycStep
 */
export enum SelfieKycStep {
  /**
   * @generated from enum value: UNKNOWN_SELFIE_STEP = 0;
   */
  UNKNOWN_SELFIE_STEP = 0,

  /**
   * @generated from enum value: START_SELFIE_STEP = 1;
   */
  START_SELFIE_STEP = 1,

  /**
   * @generated from enum value: SET_STATUS = 2;
   */
  SET_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.SelfieKycStep.
 */
export const SelfieKycStepSchema: GenEnum<SelfieKycStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 7);

/**
 * @generated from enum com.stablemoney.api.identity.RaaDuration
 */
export enum RaaDuration {
  /**
   * @generated from enum value: UNKNOWN_RAA_DURATION = 0;
   */
  UNKNOWN_RAA_DURATION = 0,

  /**
   * @generated from enum value: RAA_60_DAYS = 1;
   */
  RAA_60_DAYS = 1,

  /**
   * @generated from enum value: RAA_90_DAYS = 2;
   */
  RAA_90_DAYS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.RaaDuration.
 */
export const RaaDurationSchema: GenEnum<RaaDuration> = /*@__PURE__*/
  enumDesc(file_Kyc, 8);

