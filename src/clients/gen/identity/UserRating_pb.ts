// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file UserRating.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file UserRating.proto.
 */
export const file_UserRating: GenFile = /*@__PURE__*/
  fileDesc("ChBVc2VyUmF0aW5nLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Ir8BChFQb3N0UmF0aW5nUmVxdWVzdBITCgZyYXRpbmcYASABKAFIAIgBARITCgZyZXZpZXcYAiABKAlIAYgBARIeChFpc19yZXZpZXdfc2tpcHBlZBgDIAEoCEgCiAEBEg8KB3VzZXJfaWQYBCABKAkSIwobaXNfZ29vZ2xlX3BsYXlfcmF0aW5nX3Nob3duGAYgASgIQgkKB19yYXRpbmdCCQoHX3Jldmlld0IUChJfaXNfcmV2aWV3X3NraXBwZWQiFAoSUG9zdFJhdGluZ1Jlc3BvbnNlIiQKEVVzZXJSYXRpbmdSZXF1ZXN0Eg8KB3VzZXJfaWQYASABKAkiiQEKElVzZXJSYXRpbmdSZXNwb25zZRIOCgZyYXRpbmcYASABKAESDgoGcmV2aWV3GAIgASgJEhkKEWlzX3Jldmlld19za2lwcGVkGAMgASgIEhwKFGluX2FwcF9yZXZpZXdfY29ob3J0GAQgASgJEhoKEnNob3dfaW5fYXBwX3JldmlldxgGIAEoCCKAAQoURW1haWxGZWVkYmFja1JlcXVlc3QSEwoLY3VzdG9tZXJfaWQYASABKAkSEgoKYm9va2luZ19pZBgCIAEoCRIOCgZyYXRpbmcYAyABKAUSHgoWZ29vZF9leHBlcmllbmNlX29wdGlvbhgEIAMoCRIPCgdtZXNzYWdlGAUgASgJIicKFUVtYWlsRmVlZGJhY2tSZXNwb25zZRIOCgZzdGF0dXMYASABKAgqZgoMUmF0aW5nU291cmNlEhkKFVVOS05PV05fUkFUSU5HX1NPVVJDRRAAEhgKFElOX0FQUF9SQVRJTkdfU09VUkNFEAESIQodVERfQk9PS0VEX0VNQUlMX1JBVElOR19TT1VSQ0UQAkIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw");

/**
 * @generated from message com.stablemoney.api.identity.PostRatingRequest
 */
export type PostRatingRequest = Message<"com.stablemoney.api.identity.PostRatingRequest"> & {
  /**
   * @generated from field: optional double rating = 1;
   */
  rating?: number;

  /**
   * @generated from field: optional string review = 2;
   */
  review?: string;

  /**
   * @generated from field: optional bool is_review_skipped = 3;
   */
  isReviewSkipped?: boolean;

  /**
   * @generated from field: string user_id = 4;
   */
  userId: string;

  /**
   * @generated from field: bool is_google_play_rating_shown = 6;
   */
  isGooglePlayRatingShown: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.PostRatingRequest.
 * Use `create(PostRatingRequestSchema)` to create a new message.
 */
export const PostRatingRequestSchema: GenMessage<PostRatingRequest> = /*@__PURE__*/
  messageDesc(file_UserRating, 0);

/**
 * @generated from message com.stablemoney.api.identity.PostRatingResponse
 */
export type PostRatingResponse = Message<"com.stablemoney.api.identity.PostRatingResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.PostRatingResponse.
 * Use `create(PostRatingResponseSchema)` to create a new message.
 */
export const PostRatingResponseSchema: GenMessage<PostRatingResponse> = /*@__PURE__*/
  messageDesc(file_UserRating, 1);

/**
 * @generated from message com.stablemoney.api.identity.UserRatingRequest
 */
export type UserRatingRequest = Message<"com.stablemoney.api.identity.UserRatingRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;
};

/**
 * Describes the message com.stablemoney.api.identity.UserRatingRequest.
 * Use `create(UserRatingRequestSchema)` to create a new message.
 */
export const UserRatingRequestSchema: GenMessage<UserRatingRequest> = /*@__PURE__*/
  messageDesc(file_UserRating, 2);

/**
 * @generated from message com.stablemoney.api.identity.UserRatingResponse
 */
export type UserRatingResponse = Message<"com.stablemoney.api.identity.UserRatingResponse"> & {
  /**
   * @generated from field: double rating = 1;
   */
  rating: number;

  /**
   * @generated from field: string review = 2;
   */
  review: string;

  /**
   * @generated from field: bool is_review_skipped = 3;
   */
  isReviewSkipped: boolean;

  /**
   * @generated from field: string in_app_review_cohort = 4;
   */
  inAppReviewCohort: string;

  /**
   * @generated from field: bool show_in_app_review = 6;
   */
  showInAppReview: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.UserRatingResponse.
 * Use `create(UserRatingResponseSchema)` to create a new message.
 */
export const UserRatingResponseSchema: GenMessage<UserRatingResponse> = /*@__PURE__*/
  messageDesc(file_UserRating, 3);

/**
 * @generated from message com.stablemoney.api.identity.EmailFeedbackRequest
 */
export type EmailFeedbackRequest = Message<"com.stablemoney.api.identity.EmailFeedbackRequest"> & {
  /**
   * @generated from field: string customer_id = 1;
   */
  customerId: string;

  /**
   * @generated from field: string booking_id = 2;
   */
  bookingId: string;

  /**
   * @generated from field: int32 rating = 3;
   */
  rating: number;

  /**
   * @generated from field: repeated string good_experience_option = 4;
   */
  goodExperienceOption: string[];

  /**
   * @generated from field: string message = 5;
   */
  message: string;
};

/**
 * Describes the message com.stablemoney.api.identity.EmailFeedbackRequest.
 * Use `create(EmailFeedbackRequestSchema)` to create a new message.
 */
export const EmailFeedbackRequestSchema: GenMessage<EmailFeedbackRequest> = /*@__PURE__*/
  messageDesc(file_UserRating, 4);

/**
 * @generated from message com.stablemoney.api.identity.EmailFeedbackResponse
 */
export type EmailFeedbackResponse = Message<"com.stablemoney.api.identity.EmailFeedbackResponse"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.EmailFeedbackResponse.
 * Use `create(EmailFeedbackResponseSchema)` to create a new message.
 */
export const EmailFeedbackResponseSchema: GenMessage<EmailFeedbackResponse> = /*@__PURE__*/
  messageDesc(file_UserRating, 5);

/**
 * @generated from enum com.stablemoney.api.identity.RatingSource
 */
export enum RatingSource {
  /**
   * @generated from enum value: UNKNOWN_RATING_SOURCE = 0;
   */
  UNKNOWN_RATING_SOURCE = 0,

  /**
   * @generated from enum value: IN_APP_RATING_SOURCE = 1;
   */
  IN_APP_RATING_SOURCE = 1,

  /**
   * @generated from enum value: TD_BOOKED_EMAIL_RATING_SOURCE = 2;
   */
  TD_BOOKED_EMAIL_RATING_SOURCE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.RatingSource.
 */
export const RatingSourceSchema: GenEnum<RatingSource> = /*@__PURE__*/
  enumDesc(file_UserRating, 0);

