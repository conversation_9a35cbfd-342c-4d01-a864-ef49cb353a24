// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file StableExchange.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file StableExchange.proto.
 */
export const file_StableExchange: GenFile = /*@__PURE__*/
  fileDesc("ChRTdGFibGVFeGNoYW5nZS5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSKhBQoMUXVlc3Rpb25EYXRhEhAKCHF1ZXN0aW9uGAEgASgJEhMKC3F1ZXN0aW9uX2lkGAIgASgJEhMKC3BlcnNwZWN0aXZlGAMgASgJEhEKCXVzZXJfbmFtZRgEIAEoCRIQCgh1c2VyX2FnZRgFIAEoBRITCgt1c2VyX2dlbmRlchgGIAEoCRIRCgl1c2VyX2NpdHkYByABKAkSEgoKdXNlcl9pbWFnZRgIIAEoCRIWCg5yZXNwb25zZV9jb3VudBgJIAEoCRIZChFyZWdpc3RyYXRpb25fZGF0ZRgKIAEoCRI9CgthbnN3ZXJfZGF0YRgLIAMoCzIoLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQW5zd2VyTGlzdBIXCg9pc19yZW1pbmRlcl9zZXQYDCABKAgSFgoOcmVtaW5kZXJfY291bnQYDSABKAUSHAoUcXVlc3Rpb25fcG9zdGVkX2RhdGUYDiABKAkSHAoUaXNfcXVlc3Rpb25fYW5zd2VyZWQYDyABKAgSGgoSaXNfcXVlc3Rpb25fcG9zdGVkGBAgASgIEhgKEGFncmVlX3BlcmNlbnRhZ2UYESABKAkSFwoPd2VsX2RvbmVfc3RyaW5nGBIgASgJEhgKEHJlc3BvbmRlcl9pbWFnZXMYEyADKAkSTAoTcmVtaW5kZXJfYXNrX3N0cmluZxgUIAEoCzIvLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUmVtaW5kZXJBc2tTdHJpbmcSFwoPdXNlcl9maXJzdF9uYW1lGBUgASgJEiMKG3F1ZXN0aW9uX3Bvc3RlZF9kYXRlX3N0cmluZxgWIAEoCRIgChhxdWVzdGlvbl9wb3N0ZWRfZW5kX2RhdGUYFyABKAkiWgoVVG9kYXlRdWVzdGlvblJlc3BvbnNlEkEKDXF1ZXN0aW9uX2RhdGEYASABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlF1ZXN0aW9uRGF0YSJDCgpBbnN3ZXJMaXN0EhEKCWFuc3dlcl9pZBgBIAEoCRIOCgZhbnN3ZXIYAiABKAkSEgoKcGVyY2VudGFnZRgDIAEoASJYChNBbGxRdWVzdGlvblJlc3BvbnNlEkEKDXF1ZXN0aW9uX2RhdGEYASADKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlF1ZXN0aW9uRGF0YSJbChFSZW1pbmRlckFza1N0cmluZxIXCg9yZW1pbmRlcl9zdHJpbmcYASABKAkSEgoKYXNrX3N0cmluZxgCIAMoCRIZChFjb21wbGV0aW9uX3N0cmluZxgDIAEoCSI9ChNTdWJtaXRBbnN3ZXJSZXF1ZXN0EhEKCWFuc3dlcl9pZBgBIAEoCRITCgtxdWVzdGlvbl9pZBgCIAEoCSJZChRTdWJtaXRBbnN3ZXJSZXNwb25zZRJBCg1xdWVzdGlvbl9kYXRhGAEgASgLMiouY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5RdWVzdGlvbkRhdGEiKQoVU3VibWl0UXVlc3Rpb25SZXF1ZXN0EhAKCHF1ZXN0aW9uGAEgASgJIhgKFlN1Ym1pdFF1ZXN0aW9uUmVzcG9uc2UiWwoWU3VibWl0UmVtaW5kZXJSZXNwb25zZRJBCg1xdWVzdGlvbl9kYXRhGAEgASgLMiouY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5RdWVzdGlvbkRhdGEiLAoVU3VibWl0UmVtaW5kZXJSZXF1ZXN0EhMKC2lzX3JlbWluZGVyGAEgASgIQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.identity.QuestionData
 */
export type QuestionData = Message<"com.stablemoney.api.identity.QuestionData"> & {
  /**
   * @generated from field: string question = 1;
   */
  question: string;

  /**
   * @generated from field: string question_id = 2;
   */
  questionId: string;

  /**
   * @generated from field: string perspective = 3;
   */
  perspective: string;

  /**
   * @generated from field: string user_name = 4;
   */
  userName: string;

  /**
   * @generated from field: int32 user_age = 5;
   */
  userAge: number;

  /**
   * @generated from field: string user_gender = 6;
   */
  userGender: string;

  /**
   * @generated from field: string user_city = 7;
   */
  userCity: string;

  /**
   * @generated from field: string user_image = 8;
   */
  userImage: string;

  /**
   * @generated from field: string response_count = 9;
   */
  responseCount: string;

  /**
   * @generated from field: string registration_date = 10;
   */
  registrationDate: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.AnswerList answer_data = 11;
   */
  answerData: AnswerList[];

  /**
   * @generated from field: bool is_reminder_set = 12;
   */
  isReminderSet: boolean;

  /**
   * @generated from field: int32 reminder_count = 13;
   */
  reminderCount: number;

  /**
   * @generated from field: string question_posted_date = 14;
   */
  questionPostedDate: string;

  /**
   * @generated from field: bool is_question_answered = 15;
   */
  isQuestionAnswered: boolean;

  /**
   * @generated from field: bool is_question_posted = 16;
   */
  isQuestionPosted: boolean;

  /**
   * @generated from field: string agree_percentage = 17;
   */
  agreePercentage: string;

  /**
   * @generated from field: string wel_done_string = 18;
   */
  welDoneString: string;

  /**
   * @generated from field: repeated string responder_images = 19;
   */
  responderImages: string[];

  /**
   * @generated from field: com.stablemoney.api.identity.ReminderAskString reminder_ask_string = 20;
   */
  reminderAskString?: ReminderAskString;

  /**
   * @generated from field: string user_first_name = 21;
   */
  userFirstName: string;

  /**
   * @generated from field: string question_posted_date_string = 22;
   */
  questionPostedDateString: string;

  /**
   * @generated from field: string question_posted_end_date = 23;
   */
  questionPostedEndDate: string;
};

/**
 * Describes the message com.stablemoney.api.identity.QuestionData.
 * Use `create(QuestionDataSchema)` to create a new message.
 */
export const QuestionDataSchema: GenMessage<QuestionData> = /*@__PURE__*/
  messageDesc(file_StableExchange, 0);

/**
 * @generated from message com.stablemoney.api.identity.TodayQuestionResponse
 */
export type TodayQuestionResponse = Message<"com.stablemoney.api.identity.TodayQuestionResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.QuestionData question_data = 1;
   */
  questionData?: QuestionData;
};

/**
 * Describes the message com.stablemoney.api.identity.TodayQuestionResponse.
 * Use `create(TodayQuestionResponseSchema)` to create a new message.
 */
export const TodayQuestionResponseSchema: GenMessage<TodayQuestionResponse> = /*@__PURE__*/
  messageDesc(file_StableExchange, 1);

/**
 * @generated from message com.stablemoney.api.identity.AnswerList
 */
export type AnswerList = Message<"com.stablemoney.api.identity.AnswerList"> & {
  /**
   * @generated from field: string answer_id = 1;
   */
  answerId: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;

  /**
   * @generated from field: double percentage = 3;
   */
  percentage: number;
};

/**
 * Describes the message com.stablemoney.api.identity.AnswerList.
 * Use `create(AnswerListSchema)` to create a new message.
 */
export const AnswerListSchema: GenMessage<AnswerList> = /*@__PURE__*/
  messageDesc(file_StableExchange, 2);

/**
 * @generated from message com.stablemoney.api.identity.AllQuestionResponse
 */
export type AllQuestionResponse = Message<"com.stablemoney.api.identity.AllQuestionResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.QuestionData question_data = 1;
   */
  questionData: QuestionData[];
};

/**
 * Describes the message com.stablemoney.api.identity.AllQuestionResponse.
 * Use `create(AllQuestionResponseSchema)` to create a new message.
 */
export const AllQuestionResponseSchema: GenMessage<AllQuestionResponse> = /*@__PURE__*/
  messageDesc(file_StableExchange, 3);

/**
 * @generated from message com.stablemoney.api.identity.ReminderAskString
 */
export type ReminderAskString = Message<"com.stablemoney.api.identity.ReminderAskString"> & {
  /**
   * @generated from field: string reminder_string = 1;
   */
  reminderString: string;

  /**
   * @generated from field: repeated string ask_string = 2;
   */
  askString: string[];

  /**
   * @generated from field: string completion_string = 3;
   */
  completionString: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ReminderAskString.
 * Use `create(ReminderAskStringSchema)` to create a new message.
 */
export const ReminderAskStringSchema: GenMessage<ReminderAskString> = /*@__PURE__*/
  messageDesc(file_StableExchange, 4);

/**
 * @generated from message com.stablemoney.api.identity.SubmitAnswerRequest
 */
export type SubmitAnswerRequest = Message<"com.stablemoney.api.identity.SubmitAnswerRequest"> & {
  /**
   * @generated from field: string answer_id = 1;
   */
  answerId: string;

  /**
   * @generated from field: string question_id = 2;
   */
  questionId: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitAnswerRequest.
 * Use `create(SubmitAnswerRequestSchema)` to create a new message.
 */
export const SubmitAnswerRequestSchema: GenMessage<SubmitAnswerRequest> = /*@__PURE__*/
  messageDesc(file_StableExchange, 5);

/**
 * @generated from message com.stablemoney.api.identity.SubmitAnswerResponse
 */
export type SubmitAnswerResponse = Message<"com.stablemoney.api.identity.SubmitAnswerResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.QuestionData question_data = 1;
   */
  questionData?: QuestionData;
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitAnswerResponse.
 * Use `create(SubmitAnswerResponseSchema)` to create a new message.
 */
export const SubmitAnswerResponseSchema: GenMessage<SubmitAnswerResponse> = /*@__PURE__*/
  messageDesc(file_StableExchange, 6);

/**
 * @generated from message com.stablemoney.api.identity.SubmitQuestionRequest
 */
export type SubmitQuestionRequest = Message<"com.stablemoney.api.identity.SubmitQuestionRequest"> & {
  /**
   * @generated from field: string question = 1;
   */
  question: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitQuestionRequest.
 * Use `create(SubmitQuestionRequestSchema)` to create a new message.
 */
export const SubmitQuestionRequestSchema: GenMessage<SubmitQuestionRequest> = /*@__PURE__*/
  messageDesc(file_StableExchange, 7);

/**
 * @generated from message com.stablemoney.api.identity.SubmitQuestionResponse
 */
export type SubmitQuestionResponse = Message<"com.stablemoney.api.identity.SubmitQuestionResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitQuestionResponse.
 * Use `create(SubmitQuestionResponseSchema)` to create a new message.
 */
export const SubmitQuestionResponseSchema: GenMessage<SubmitQuestionResponse> = /*@__PURE__*/
  messageDesc(file_StableExchange, 8);

/**
 * @generated from message com.stablemoney.api.identity.SubmitReminderResponse
 */
export type SubmitReminderResponse = Message<"com.stablemoney.api.identity.SubmitReminderResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.QuestionData question_data = 1;
   */
  questionData?: QuestionData;
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitReminderResponse.
 * Use `create(SubmitReminderResponseSchema)` to create a new message.
 */
export const SubmitReminderResponseSchema: GenMessage<SubmitReminderResponse> = /*@__PURE__*/
  messageDesc(file_StableExchange, 9);

/**
 * @generated from message com.stablemoney.api.identity.SubmitReminderRequest
 */
export type SubmitReminderRequest = Message<"com.stablemoney.api.identity.SubmitReminderRequest"> & {
  /**
   * @generated from field: bool is_reminder = 1;
   */
  isReminder: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.SubmitReminderRequest.
 * Use `create(SubmitReminderRequestSchema)` to create a new message.
 */
export const SubmitReminderRequestSchema: GenMessage<SubmitReminderRequest> = /*@__PURE__*/
  messageDesc(file_StableExchange, 10);

