// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Whitelist.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Whitelist.proto.
 */
export const file_Whitelist: GenFile = /*@__PURE__*/
  fileDesc("Cg9XaGl0ZWxpc3QucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiKwoRV2hpdGVsaXN0UmVzcG9uc2USFgoOaXNfd2hpdGVsaXN0ZWQYASABKAhCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.identity.WhitelistResponse
 */
export type WhitelistResponse = Message<"com.stablemoney.api.identity.WhitelistResponse"> & {
  /**
   * @generated from field: bool is_whitelisted = 1;
   */
  isWhitelisted: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.WhitelistResponse.
 * Use `create(WhitelistResponseSchema)` to create a new message.
 */
export const WhitelistResponseSchema: GenMessage<WhitelistResponse> = /*@__PURE__*/
  messageDesc(file_Whitelist, 0);

