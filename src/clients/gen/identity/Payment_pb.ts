// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Payment.proto (package com.stablemoney.api.payment, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Payment.proto.
 */
export const file_Payment: GenFile = /*@__PURE__*/
  fileDesc("Cg1QYXltZW50LnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLnBheW1lbnQi5wIKE1BheW1lbnRPcmRlclJlcXVlc3QSFAoMcmVmZXJlbmNlX2lkGAEgASgJEhcKD2Ftb3VudF9pbl9wYWlzYRgCIAEoAxI+Cgx1c2VyX2RldGFpbHMYAyABKAsyKC5jb20uc3RhYmxlbW9uZXkuYXBpLnBheW1lbnQuVXNlckRldGFpbHMSTwoWc291cmNlX2FjY291bnRfZGV0YWlscxgEIAEoCzIvLmNvbS5zdGFibGVtb25leS5hcGkucGF5bWVudC5CYW5rQWNjb3VudERldGFpbHMSVAobZGVzdGluYXRpb25fYWNjb3VudF9kZXRhaWxzGAUgASgLMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5wYXltZW50LkJhbmtBY2NvdW50RGV0YWlscxI6CgpvcmRlcl90eXBlGAYgASgOMiYuY29tLnN0YWJsZW1vbmV5LmFwaS5wYXltZW50Lk9yZGVyVHlwZSJRCgtVc2VyRGV0YWlscxINCgVlbWFpbBgBIAEoCRIUCgxwaG9uZV9udW1iZXIYAiABKAkSDwoHdXNlcl9pZBgDIAEoCRIMCgRuYW1lGAQgASgJIo4BChJCYW5rQWNjb3VudERldGFpbHMSFgoOYWNjb3VudF9udW1iZXIYASABKAkSDAoEaWZzYxgCIAEoCRIMCgRuYW1lGAMgASgJEkQKD2FkZHJlc3NfZGV0YWlscxgEIAEoCzIrLmNvbS5zdGFibGVtb25leS5hcGkucGF5bWVudC5BZGRyZXNzRGV0YWlscyKDAQoOQWRkcmVzc0RldGFpbHMSFQoNYWRkcmVzc19saW5lMRgBIAEoCRIVCg1hZGRyZXNzX2xpbmUyGAIgASgJEhUKDWFkZHJlc3NfbGluZTMYAyABKAkSDAoEY2l0eRgEIAEoCRINCgVzdGF0ZRgFIAEoCRIPCgdwaW5jb2RlGAYgASgJInMKFFBheW1lbnRPcmRlclJlc3BvbnNlEhIKCmJvb2tpbmdfaWQYASABKAkSGAoQZ2F0ZXdheV9vcmRlcl9pZBgCIAEoCRIXCg9nYXRld2F5X3Nka19rZXkYAyABKAkSFAoMZ2F0ZXdheV9uYW1lGAQgASgJKnkKCU9yZGVyVHlwZRIaChZPUkRFUl9UWVBFX1VOU1BFQ0lGSUVEEAASDwoLU0lNUExFX0ZMT1cQARIOCgpST1VURV9GTE9XEAISGAoUQVVUT19TRVRUTEVNRU5UX0ZMT1cQAxIVChFTVEFUSUNfUk9VVEVfRkxPVxAEQh8KG2NvbS5zdGFibGVtb25leS5hcGkucGF5bWVudFABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.payment.PaymentOrderRequest
 */
export type PaymentOrderRequest = Message<"com.stablemoney.api.payment.PaymentOrderRequest"> & {
  /**
   * @generated from field: string reference_id = 1;
   */
  referenceId: string;

  /**
   * @generated from field: int64 amount_in_paisa = 2;
   */
  amountInPaisa: bigint;

  /**
   * @generated from field: com.stablemoney.api.payment.UserDetails user_details = 3;
   */
  userDetails?: UserDetails;

  /**
   * @generated from field: com.stablemoney.api.payment.BankAccountDetails source_account_details = 4;
   */
  sourceAccountDetails?: BankAccountDetails;

  /**
   * @generated from field: com.stablemoney.api.payment.BankAccountDetails destination_account_details = 5;
   */
  destinationAccountDetails?: BankAccountDetails;

  /**
   * @generated from field: com.stablemoney.api.payment.OrderType order_type = 6;
   */
  orderType: OrderType;
};

/**
 * Describes the message com.stablemoney.api.payment.PaymentOrderRequest.
 * Use `create(PaymentOrderRequestSchema)` to create a new message.
 */
export const PaymentOrderRequestSchema: GenMessage<PaymentOrderRequest> = /*@__PURE__*/
  messageDesc(file_Payment, 0);

/**
 * @generated from message com.stablemoney.api.payment.UserDetails
 */
export type UserDetails = Message<"com.stablemoney.api.payment.UserDetails"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;

  /**
   * @generated from field: string phone_number = 2;
   */
  phoneNumber: string;

  /**
   * @generated from field: string user_id = 3;
   */
  userId: string;

  /**
   * @generated from field: string name = 4;
   */
  name: string;
};

/**
 * Describes the message com.stablemoney.api.payment.UserDetails.
 * Use `create(UserDetailsSchema)` to create a new message.
 */
export const UserDetailsSchema: GenMessage<UserDetails> = /*@__PURE__*/
  messageDesc(file_Payment, 1);

/**
 * @generated from message com.stablemoney.api.payment.BankAccountDetails
 */
export type BankAccountDetails = Message<"com.stablemoney.api.payment.BankAccountDetails"> & {
  /**
   * @generated from field: string account_number = 1;
   */
  accountNumber: string;

  /**
   * @generated from field: string ifsc = 2;
   */
  ifsc: string;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.payment.AddressDetails address_details = 4;
   */
  addressDetails?: AddressDetails;
};

/**
 * Describes the message com.stablemoney.api.payment.BankAccountDetails.
 * Use `create(BankAccountDetailsSchema)` to create a new message.
 */
export const BankAccountDetailsSchema: GenMessage<BankAccountDetails> = /*@__PURE__*/
  messageDesc(file_Payment, 2);

/**
 * @generated from message com.stablemoney.api.payment.AddressDetails
 */
export type AddressDetails = Message<"com.stablemoney.api.payment.AddressDetails"> & {
  /**
   * @generated from field: string address_line1 = 1;
   */
  addressLine1: string;

  /**
   * @generated from field: string address_line2 = 2;
   */
  addressLine2: string;

  /**
   * @generated from field: string address_line3 = 3;
   */
  addressLine3: string;

  /**
   * @generated from field: string city = 4;
   */
  city: string;

  /**
   * @generated from field: string state = 5;
   */
  state: string;

  /**
   * @generated from field: string pincode = 6;
   */
  pincode: string;
};

/**
 * Describes the message com.stablemoney.api.payment.AddressDetails.
 * Use `create(AddressDetailsSchema)` to create a new message.
 */
export const AddressDetailsSchema: GenMessage<AddressDetails> = /*@__PURE__*/
  messageDesc(file_Payment, 3);

/**
 * @generated from message com.stablemoney.api.payment.PaymentOrderResponse
 */
export type PaymentOrderResponse = Message<"com.stablemoney.api.payment.PaymentOrderResponse"> & {
  /**
   * @generated from field: string booking_id = 1;
   */
  bookingId: string;

  /**
   * @generated from field: string gateway_order_id = 2;
   */
  gatewayOrderId: string;

  /**
   * @generated from field: string gateway_sdk_key = 3;
   */
  gatewaySdkKey: string;

  /**
   * @generated from field: string gateway_name = 4;
   */
  gatewayName: string;
};

/**
 * Describes the message com.stablemoney.api.payment.PaymentOrderResponse.
 * Use `create(PaymentOrderResponseSchema)` to create a new message.
 */
export const PaymentOrderResponseSchema: GenMessage<PaymentOrderResponse> = /*@__PURE__*/
  messageDesc(file_Payment, 4);

/**
 * @generated from enum com.stablemoney.api.payment.OrderType
 */
export enum OrderType {
  /**
   * @generated from enum value: ORDER_TYPE_UNSPECIFIED = 0;
   */
  ORDER_TYPE_UNSPECIFIED = 0,

  /**
   * @generated from enum value: SIMPLE_FLOW = 1;
   */
  SIMPLE_FLOW = 1,

  /**
   * used by Unity
   *
   * @generated from enum value: ROUTE_FLOW = 2;
   */
  ROUTE_FLOW = 2,

  /**
   * used by Indusind
   *
   * @generated from enum value: AUTO_SETTLEMENT_FLOW = 3;
   */
  AUTO_SETTLEMENT_FLOW = 3,

  /**
   * @generated from enum value: STATIC_ROUTE_FLOW = 4;
   */
  STATIC_ROUTE_FLOW = 4,
}

/**
 * Describes the enum com.stablemoney.api.payment.OrderType.
 */
export const OrderTypeSchema: GenEnum<OrderType> = /*@__PURE__*/
  enumDesc(file_Payment, 0);

