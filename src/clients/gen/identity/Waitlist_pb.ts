// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Waitlist.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Waitlist.proto.
 */
export const file_Waitlist: GenFile = /*@__PURE__*/
  fileDesc("Cg5XYWl0bGlzdC5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSI6ChJBZGRSZWZlcnJlclJlcXVlc3QSEgoKcmVmZXJlcl9pZBgBIAEoCRIQCghtZXRhZGF0YRgCIAEoCSImChdBZGRSZWZlcnJlckJ5VXJsUmVxdWVzdBILCgN1cmwYASABKAkiNQoTQWRkUmVmZXJyZXJSZXNwb25zZRITCgZzdGF0dXMYASABKAlIAIgBAUIJCgdfc3RhdHVzIhcKFUdldFJlZmVycmFsVXJsUmVxdWVzdCIuChZHZXRSZWZlcnJhbFVybFJlc3BvbnNlEhQKDHJlZmVycmFsX3VybBgBIAEoCSIvChtHZXRXYWl0bGlzdFBvc2l0aW9uUmVzcG9uc2USEAoIcG9zaXRpb24YASABKAMiHAoaR2V0V2FpdGxpc3RQb3NpdGlvblJlcXVlc3QiHQobR2VuZXJhdGVSZWZlcnJhbExpbmtSZXF1ZXN0IjUKHEdlbmVyYXRlUmVmZXJyYWxMaW5rUmVzcG9uc2USFQoNcmVmZXJyYWxfbGluaxgBIAEoCUIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw");

/**
 * @generated from message com.stablemoney.api.identity.AddReferrerRequest
 */
export type AddReferrerRequest = Message<"com.stablemoney.api.identity.AddReferrerRequest"> & {
  /**
   * @generated from field: string referer_id = 1;
   */
  refererId: string;

  /**
   * @generated from field: string metadata = 2;
   */
  metadata: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AddReferrerRequest.
 * Use `create(AddReferrerRequestSchema)` to create a new message.
 */
export const AddReferrerRequestSchema: GenMessage<AddReferrerRequest> = /*@__PURE__*/
  messageDesc(file_Waitlist, 0);

/**
 * @generated from message com.stablemoney.api.identity.AddReferrerByUrlRequest
 */
export type AddReferrerByUrlRequest = Message<"com.stablemoney.api.identity.AddReferrerByUrlRequest"> & {
  /**
   * @generated from field: string url = 1;
   */
  url: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AddReferrerByUrlRequest.
 * Use `create(AddReferrerByUrlRequestSchema)` to create a new message.
 */
export const AddReferrerByUrlRequestSchema: GenMessage<AddReferrerByUrlRequest> = /*@__PURE__*/
  messageDesc(file_Waitlist, 1);

/**
 * @generated from message com.stablemoney.api.identity.AddReferrerResponse
 */
export type AddReferrerResponse = Message<"com.stablemoney.api.identity.AddReferrerResponse"> & {
  /**
   * @generated from field: optional string status = 1;
   */
  status?: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AddReferrerResponse.
 * Use `create(AddReferrerResponseSchema)` to create a new message.
 */
export const AddReferrerResponseSchema: GenMessage<AddReferrerResponse> = /*@__PURE__*/
  messageDesc(file_Waitlist, 2);

/**
 * @generated from message com.stablemoney.api.identity.GetReferralUrlRequest
 */
export type GetReferralUrlRequest = Message<"com.stablemoney.api.identity.GetReferralUrlRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.GetReferralUrlRequest.
 * Use `create(GetReferralUrlRequestSchema)` to create a new message.
 */
export const GetReferralUrlRequestSchema: GenMessage<GetReferralUrlRequest> = /*@__PURE__*/
  messageDesc(file_Waitlist, 3);

/**
 * @generated from message com.stablemoney.api.identity.GetReferralUrlResponse
 */
export type GetReferralUrlResponse = Message<"com.stablemoney.api.identity.GetReferralUrlResponse"> & {
  /**
   * @generated from field: string referral_url = 1;
   */
  referralUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GetReferralUrlResponse.
 * Use `create(GetReferralUrlResponseSchema)` to create a new message.
 */
export const GetReferralUrlResponseSchema: GenMessage<GetReferralUrlResponse> = /*@__PURE__*/
  messageDesc(file_Waitlist, 4);

/**
 * @generated from message com.stablemoney.api.identity.GetWaitlistPositionResponse
 */
export type GetWaitlistPositionResponse = Message<"com.stablemoney.api.identity.GetWaitlistPositionResponse"> & {
  /**
   * @generated from field: int64 position = 1;
   */
  position: bigint;
};

/**
 * Describes the message com.stablemoney.api.identity.GetWaitlistPositionResponse.
 * Use `create(GetWaitlistPositionResponseSchema)` to create a new message.
 */
export const GetWaitlistPositionResponseSchema: GenMessage<GetWaitlistPositionResponse> = /*@__PURE__*/
  messageDesc(file_Waitlist, 5);

/**
 * @generated from message com.stablemoney.api.identity.GetWaitlistPositionRequest
 */
export type GetWaitlistPositionRequest = Message<"com.stablemoney.api.identity.GetWaitlistPositionRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.GetWaitlistPositionRequest.
 * Use `create(GetWaitlistPositionRequestSchema)` to create a new message.
 */
export const GetWaitlistPositionRequestSchema: GenMessage<GetWaitlistPositionRequest> = /*@__PURE__*/
  messageDesc(file_Waitlist, 6);

/**
 * @generated from message com.stablemoney.api.identity.GenerateReferralLinkRequest
 */
export type GenerateReferralLinkRequest = Message<"com.stablemoney.api.identity.GenerateReferralLinkRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.GenerateReferralLinkRequest.
 * Use `create(GenerateReferralLinkRequestSchema)` to create a new message.
 */
export const GenerateReferralLinkRequestSchema: GenMessage<GenerateReferralLinkRequest> = /*@__PURE__*/
  messageDesc(file_Waitlist, 7);

/**
 * @generated from message com.stablemoney.api.identity.GenerateReferralLinkResponse
 */
export type GenerateReferralLinkResponse = Message<"com.stablemoney.api.identity.GenerateReferralLinkResponse"> & {
  /**
   * @generated from field: string referral_link = 1;
   */
  referralLink: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GenerateReferralLinkResponse.
 * Use `create(GenerateReferralLinkResponseSchema)` to create a new message.
 */
export const GenerateReferralLinkResponseSchema: GenMessage<GenerateReferralLinkResponse> = /*@__PURE__*/
  messageDesc(file_Waitlist, 8);

