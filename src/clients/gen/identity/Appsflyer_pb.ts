// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Appsflyer.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Appsflyer.proto.
 */
export const file_Appsflyer: GenFile = /*@__PURE__*/
  fileDesc("Cg9BcHBzZmx5ZXIucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiKAoZQXBwc0ZseWVyVXBkYXRlVHRsUmVxdWVzdBILCgN1cmwYASABKAkiHAoaQXBwc0ZseWVyVXBkYXRlVHRsUmVzcG9uc2VCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.identity.AppsFlyerUpdateTtlRequest
 */
export type AppsFlyerUpdateTtlRequest = Message<"com.stablemoney.api.identity.AppsFlyerUpdateTtlRequest"> & {
  /**
   * @generated from field: string url = 1;
   */
  url: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AppsFlyerUpdateTtlRequest.
 * Use `create(AppsFlyerUpdateTtlRequestSchema)` to create a new message.
 */
export const AppsFlyerUpdateTtlRequestSchema: GenMessage<AppsFlyerUpdateTtlRequest> = /*@__PURE__*/
  messageDesc(file_Appsflyer, 0);

/**
 * @generated from message com.stablemoney.api.identity.AppsFlyerUpdateTtlResponse
 */
export type AppsFlyerUpdateTtlResponse = Message<"com.stablemoney.api.identity.AppsFlyerUpdateTtlResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.AppsFlyerUpdateTtlResponse.
 * Use `create(AppsFlyerUpdateTtlResponseSchema)` to create a new message.
 */
export const AppsFlyerUpdateTtlResponseSchema: GenMessage<AppsFlyerUpdateTtlResponse> = /*@__PURE__*/
  messageDesc(file_Appsflyer, 1);

