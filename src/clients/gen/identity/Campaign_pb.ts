// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Campaign.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Campaign.proto.
 */
export const file_Campaign: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message com.stablemoney.api.identity.Address
 */
export type Address = Message<"com.stablemoney.api.identity.Address"> & {
  /**
   * @generated from field: string address_line1 = 1;
   */
  addressLine1: string;

  /**
   * @generated from field: optional string address_line2 = 2;
   */
  addressLine2?: string;

  /**
   * @generated from field: optional string address_line3 = 3;
   */
  addressLine3?: string;

  /**
   * @generated from field: string city_id = 4;
   */
  cityId: string;

  /**
   * @generated from field: string pin_code = 5;
   */
  pinCode: string;
};

/**
 * Describes the message com.stablemoney.api.identity.Address.
 * Use `create(AddressSchema)` to create a new message.
 */
export const AddressSchema: GenMessage<Address> = /*@__PURE__*/
  messageDesc(file_Campaign, 0);

/**
 * @generated from message com.stablemoney.api.identity.FundingCampaignMetadata
 */
export type FundingCampaignMetadata = Message<"com.stablemoney.api.identity.FundingCampaignMetadata"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.GiftOrderStatus order_status = 1;
   */
  orderStatus: GiftOrderStatus;

  /**
   * @generated from field: bool can_user_refer = 2;
   */
  canUserRefer: boolean;

  /**
   * @generated from field: int32 referrals_left_count = 3;
   */
  referralsLeftCount: number;

  /**
   * @generated from field: optional string tracking_link_url = 4;
   */
  trackingLinkUrl?: string;
};

/**
 * Describes the message com.stablemoney.api.identity.FundingCampaignMetadata.
 * Use `create(FundingCampaignMetadataSchema)` to create a new message.
 */
export const FundingCampaignMetadataSchema: GenMessage<FundingCampaignMetadata> = /*@__PURE__*/
  messageDesc(file_Campaign, 1);

/**
 * @generated from message com.stablemoney.api.identity.TShirtCampaignMetadata
 */
export type TShirtCampaignMetadata = Message<"com.stablemoney.api.identity.TShirtCampaignMetadata"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.GiftOrderStatus gift_order_status = 1;
   */
  giftOrderStatus: GiftOrderStatus;

  /**
   * @generated from field: optional com.stablemoney.api.identity.Address address = 2;
   */
  address?: Address;

  /**
   * @generated from field: optional string t_shirt_size = 3;
   */
  tShirtSize?: string;

  /**
   * @generated from field: int32 eligibility_referral_count = 4;
   */
  eligibilityReferralCount: number;

  /**
   * @generated from field: int32 current_referral_count = 5;
   */
  currentReferralCount: number;

  /**
   * @generated from field: string campaign_referral_link = 6;
   */
  campaignReferralLink: string;
};

/**
 * Describes the message com.stablemoney.api.identity.TShirtCampaignMetadata.
 * Use `create(TShirtCampaignMetadataSchema)` to create a new message.
 */
export const TShirtCampaignMetadataSchema: GenMessage<TShirtCampaignMetadata> = /*@__PURE__*/
  messageDesc(file_Campaign, 2);

/**
 * @generated from message com.stablemoney.api.identity.BondsReferralCampaignMetadata
 */
export type BondsReferralCampaignMetadata = Message<"com.stablemoney.api.identity.BondsReferralCampaignMetadata"> & {
  /**
   * @generated from field: string campaign_referral_link = 1;
   */
  campaignReferralLink: string;

  /**
   * @generated from field: string share_text = 2;
   */
  shareText: string;
};

/**
 * Describes the message com.stablemoney.api.identity.BondsReferralCampaignMetadata.
 * Use `create(BondsReferralCampaignMetadataSchema)` to create a new message.
 */
export const BondsReferralCampaignMetadataSchema: GenMessage<BondsReferralCampaignMetadata> = /*@__PURE__*/
  messageDesc(file_Campaign, 3);

/**
 * @generated from message com.stablemoney.api.identity.KbcReferralCampaignMetadata
 */
export type KbcReferralCampaignMetadata = Message<"com.stablemoney.api.identity.KbcReferralCampaignMetadata"> & {
  /**
   * @generated from field: string campaign_referral_link = 1;
   */
  campaignReferralLink: string;

  /**
   * @generated from field: string share_text = 2;
   */
  shareText: string;
};

/**
 * Describes the message com.stablemoney.api.identity.KbcReferralCampaignMetadata.
 * Use `create(KbcReferralCampaignMetadataSchema)` to create a new message.
 */
export const KbcReferralCampaignMetadataSchema: GenMessage<KbcReferralCampaignMetadata> = /*@__PURE__*/
  messageDesc(file_Campaign, 4);

/**
 * @generated from message com.stablemoney.api.identity.CampaignResponse
 */
export type CampaignResponse = Message<"com.stablemoney.api.identity.CampaignResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.CampaignType campaign_type = 1;
   */
  campaignType: CampaignType;

  /**
   * @generated from field: bool is_user_eligible = 2;
   */
  isUserEligible: boolean;

  /**
   * @generated from oneof com.stablemoney.api.identity.CampaignResponse.metadata
   */
  metadata: {
    /**
     * @generated from field: com.stablemoney.api.identity.FundingCampaignMetadata funding_campaign_metadata = 3;
     */
    value: FundingCampaignMetadata;
    case: "fundingCampaignMetadata";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.TShirtCampaignMetadata t_shirt_campaign_metadata = 4;
     */
    value: TShirtCampaignMetadata;
    case: "tShirtCampaignMetadata";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.BondsReferralCampaignMetadata bonds_referral_campaign_metadata = 5;
     */
    value: BondsReferralCampaignMetadata;
    case: "bondsReferralCampaignMetadata";
  } | {
    /**
     * @generated from field: com.stablemoney.api.identity.KbcReferralCampaignMetadata kbc_referral_campaign_metadata = 6;
     */
    value: KbcReferralCampaignMetadata;
    case: "kbcReferralCampaignMetadata";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.CampaignResponse.
 * Use `create(CampaignResponseSchema)` to create a new message.
 */
export const CampaignResponseSchema: GenMessage<CampaignResponse> = /*@__PURE__*/
  messageDesc(file_Campaign, 5);

/**
 * @generated from message com.stablemoney.api.identity.TShirtCampaignRequest
 */
export type TShirtCampaignRequest = Message<"com.stablemoney.api.identity.TShirtCampaignRequest"> & {
  /**
   * @generated from field: optional string t_shirt_size = 1;
   */
  tShirtSize?: string;

  /**
   * @generated from field: optional com.stablemoney.api.identity.Address address = 2;
   */
  address?: Address;
};

/**
 * Describes the message com.stablemoney.api.identity.TShirtCampaignRequest.
 * Use `create(TShirtCampaignRequestSchema)` to create a new message.
 */
export const TShirtCampaignRequestSchema: GenMessage<TShirtCampaignRequest> = /*@__PURE__*/
  messageDesc(file_Campaign, 6);

/**
 * @generated from message com.stablemoney.api.identity.PlaceOrderRequest
 */
export type PlaceOrderRequest = Message<"com.stablemoney.api.identity.PlaceOrderRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.CampaignType campaign_type = 1;
   */
  campaignType: CampaignType;

  /**
   * @generated from oneof com.stablemoney.api.identity.PlaceOrderRequest.metadata
   */
  metadata: {
    /**
     * @generated from field: com.stablemoney.api.identity.TShirtCampaignRequest t_shirt_campaign_request = 2;
     */
    value: TShirtCampaignRequest;
    case: "tShirtCampaignRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.PlaceOrderRequest.
 * Use `create(PlaceOrderRequestSchema)` to create a new message.
 */
export const PlaceOrderRequestSchema: GenMessage<PlaceOrderRequest> = /*@__PURE__*/
  messageDesc(file_Campaign, 7);

/**
 * @generated from message com.stablemoney.api.identity.PlaceOrderResponse
 */
export type PlaceOrderResponse = Message<"com.stablemoney.api.identity.PlaceOrderResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.GiftOrderStatus orderStatus = 1;
   */
  orderStatus: GiftOrderStatus;
};

/**
 * Describes the message com.stablemoney.api.identity.PlaceOrderResponse.
 * Use `create(PlaceOrderResponseSchema)` to create a new message.
 */
export const PlaceOrderResponseSchema: GenMessage<PlaceOrderResponse> = /*@__PURE__*/
  messageDesc(file_Campaign, 8);

/**
 * @generated from enum com.stablemoney.api.identity.CampaignType
 */
export enum CampaignType {
  /**
   * @generated from enum value: UNKNOWN_CAMPAIGN_TYPE = 0;
   */
  UNKNOWN_CAMPAIGN_TYPE = 0,

  /**
   * @generated from enum value: FUNDING_SWEETS_CAMPAIGN_TYPE = 1;
   */
  FUNDING_SWEETS_CAMPAIGN_TYPE = 1,

  /**
   * @generated from enum value: EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE = 2;
   */
  EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE = 2,

  /**
   * @generated from enum value: DEFAULT_REFERRAL_CAMPAIGN_TYPE = 3;
   */
  DEFAULT_REFERRAL_CAMPAIGN_TYPE = 3,

  /**
   * @generated from enum value: T_SHIRT_CAMPAIGN_TYPE = 4;
   */
  T_SHIRT_CAMPAIGN_TYPE = 4,

  /**
   * @generated from enum value: BONDS_REFERRAL_CAMPAIGN_TYPE = 5;
   */
  BONDS_REFERRAL_CAMPAIGN_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.CampaignType.
 */
export const CampaignTypeSchema: GenEnum<CampaignType> = /*@__PURE__*/
  enumDesc(file_Campaign, 0);

/**
 * @generated from enum com.stablemoney.api.identity.GiftOrderStatus
 */
export enum GiftOrderStatus {
  /**
   * @generated from enum value: UNKNOWN_GIFT_ORDER_STATUS = 0;
   */
  UNKNOWN_GIFT_ORDER_STATUS = 0,

  /**
   * @generated from enum value: ORDER_PLACED_GIFT_ORDER_STATUS = 1;
   */
  ORDER_PLACED_GIFT_ORDER_STATUS = 1,

  /**
   * @generated from enum value: FAILED_GIFT_ORDER_STATUS = 2;
   */
  FAILED_GIFT_ORDER_STATUS = 2,

  /**
   * @generated from enum value: INITIATED_GIFT_ORDER_STATUS = 3;
   */
  INITIATED_GIFT_ORDER_STATUS = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.GiftOrderStatus.
 */
export const GiftOrderStatusSchema: GenEnum<GiftOrderStatus> = /*@__PURE__*/
  enumDesc(file_Campaign, 1);

