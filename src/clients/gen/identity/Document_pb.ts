// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Document.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Document.proto.
 */
export const file_Document: GenFile = /*@__PURE__*/
  fileDesc("Cg5Eb2N1bWVudC5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSInChFTaGFyZVRleHRSZXNwb25zZRISCgpzaGFyZV90ZXh0GAEgASgJIi0KFFJlZmVycmFsTGlua1Jlc3BvbnNlEhUKDXJlZmVycmFsX2xpbmsYASABKAlCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.identity.ShareTextResponse
 */
export type ShareTextResponse = Message<"com.stablemoney.api.identity.ShareTextResponse"> & {
  /**
   * @generated from field: string share_text = 1;
   */
  shareText: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ShareTextResponse.
 * Use `create(ShareTextResponseSchema)` to create a new message.
 */
export const ShareTextResponseSchema: GenMessage<ShareTextResponse> = /*@__PURE__*/
  messageDesc(file_Document, 0);

/**
 * @generated from message com.stablemoney.api.identity.ReferralLinkResponse
 */
export type ReferralLinkResponse = Message<"com.stablemoney.api.identity.ReferralLinkResponse"> & {
  /**
   * @generated from field: string referral_link = 1;
   */
  referralLink: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ReferralLinkResponse.
 * Use `create(ReferralLinkResponseSchema)` to create a new message.
 */
export const ReferralLinkResponseSchema: GenMessage<ReferralLinkResponse> = /*@__PURE__*/
  messageDesc(file_Document, 1);

