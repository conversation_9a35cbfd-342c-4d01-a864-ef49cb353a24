// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Device.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Device.proto.
 */
export const file_Device: GenFile = /*@__PURE__*/
  fileDesc("CgxEZXZpY2UucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHki3AMKClVzZXJEZXZpY2USCgoCaWQYASABKAkSPQoLZGV2aWNlX3R5cGUYAiABKA4yKC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkRldmljZVR5cGUSEgoKb3NfdmVyc2lvbhgDIAEoCRINCgVtb2RlbBgEIAEoCRITCgthcHBfdmVyc2lvbhgFIAEoCRIYChBsYXN0X2FjdGl2ZV90aW1lGAYgASgJEhoKEm5vdGlmaWNhdGlvbl90b2tlbhgHIAEoCRINCgVhYV9pZBgIIAEoCRIRCgRpZGZ2GAkgASgJSACIAQESGQoMYXBwc2ZseWVyX2lkGAogASgJSAGIAQESHQoQYXBwX3ZlcnNpb25fbmFtZRgLIAEoCUgCiAEBEhAKA2FpZRgMIAEoCEgDiAEBEhAKA2F0dBgNIAEoA0gEiAEBEhYKCWFwcF9zdG9yZRgOIAEoCUgFiAEBEhwKD2FwcF9pbnN0YW5jZV9pZBgQIAEoCUgGiAEBQgcKBV9pZGZ2Qg8KDV9hcHBzZmx5ZXJfaWRCEwoRX2FwcF92ZXJzaW9uX25hbWVCBgoEX2FpZUIGCgRfYXR0QgwKCl9hcHBfc3RvcmVCEgoQX2FwcF9pbnN0YW5jZV9pZCJUChNVcGRhdGVEZXZpY2VSZXF1ZXN0Ej0KC3VzZXJfZGV2aWNlGAEgASgLMiguY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Vc2VyRGV2aWNlIhYKFFVwZGF0ZURldmljZVJlc3BvbnNlKlQKCkRldmljZVR5cGUSFwoTQ0xJRU5UX1RZUEVfVU5LTk9XThAAEgsKB0FORFJPSUQQARIHCgNJT1MQAhIHCgNXRUIQAxIOCgpNT0JJTEVfV0VCEARCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.identity.UserDevice
 */
export type UserDevice = Message<"com.stablemoney.api.identity.UserDevice"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: com.stablemoney.api.identity.DeviceType device_type = 2;
   */
  deviceType: DeviceType;

  /**
   * @generated from field: string os_version = 3;
   */
  osVersion: string;

  /**
   * @generated from field: string model = 4;
   */
  model: string;

  /**
   * @generated from field: string app_version = 5;
   */
  appVersion: string;

  /**
   * @generated from field: string last_active_time = 6;
   */
  lastActiveTime: string;

  /**
   * @generated from field: string notification_token = 7;
   */
  notificationToken: string;

  /**
   * @generated from field: string aa_id = 8;
   */
  aaId: string;

  /**
   * @generated from field: optional string idfv = 9;
   */
  idfv?: string;

  /**
   * @generated from field: optional string appsflyer_id = 10;
   */
  appsflyerId?: string;

  /**
   * @generated from field: optional string app_version_name = 11;
   */
  appVersionName?: string;

  /**
   * @generated from field: optional bool aie = 12;
   */
  aie?: boolean;

  /**
   * @generated from field: optional int64 att = 13;
   */
  att?: bigint;

  /**
   * @generated from field: optional string app_store = 14;
   */
  appStore?: string;

  /**
   * @generated from field: optional string app_instance_id = 16;
   */
  appInstanceId?: string;
};

/**
 * Describes the message com.stablemoney.api.identity.UserDevice.
 * Use `create(UserDeviceSchema)` to create a new message.
 */
export const UserDeviceSchema: GenMessage<UserDevice> = /*@__PURE__*/
  messageDesc(file_Device, 0);

/**
 * @generated from message com.stablemoney.api.identity.UpdateDeviceRequest
 */
export type UpdateDeviceRequest = Message<"com.stablemoney.api.identity.UpdateDeviceRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.UserDevice user_device = 1;
   */
  userDevice?: UserDevice;
};

/**
 * Describes the message com.stablemoney.api.identity.UpdateDeviceRequest.
 * Use `create(UpdateDeviceRequestSchema)` to create a new message.
 */
export const UpdateDeviceRequestSchema: GenMessage<UpdateDeviceRequest> = /*@__PURE__*/
  messageDesc(file_Device, 1);

/**
 * @generated from message com.stablemoney.api.identity.UpdateDeviceResponse
 */
export type UpdateDeviceResponse = Message<"com.stablemoney.api.identity.UpdateDeviceResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.UpdateDeviceResponse.
 * Use `create(UpdateDeviceResponseSchema)` to create a new message.
 */
export const UpdateDeviceResponseSchema: GenMessage<UpdateDeviceResponse> = /*@__PURE__*/
  messageDesc(file_Device, 2);

/**
 * @generated from enum com.stablemoney.api.identity.DeviceType
 */
export enum DeviceType {
  /**
   * @generated from enum value: CLIENT_TYPE_UNKNOWN = 0;
   */
  CLIENT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: ANDROID = 1;
   */
  ANDROID = 1,

  /**
   * @generated from enum value: IOS = 2;
   */
  IOS = 2,

  /**
   * @generated from enum value: WEB = 3;
   */
  WEB = 3,

  /**
   * @generated from enum value: MOBILE_WEB = 4;
   */
  MOBILE_WEB = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.DeviceType.
 */
export const DeviceTypeSchema: GenEnum<DeviceType> = /*@__PURE__*/
  enumDesc(file_Device, 0);

