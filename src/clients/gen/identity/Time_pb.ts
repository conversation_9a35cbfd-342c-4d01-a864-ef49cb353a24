// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Time.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Time.proto.
 */
export const file_Time: GenFile = /*@__PURE__*/
  fileDesc("CgpUaW1lLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5ImsKE0N1cnJlbnRUaW1lUmVzcG9uc2USGAoQY3VycmVudF90aW1lem9uZRgBIAEoCRIYChBjdXJyZW50X3RpbWVfaXNvGAIgASgJEiAKGGN1cnJlbnRfdGltZV9lcG9jaF9taWxsaRgDIAEoA0IgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw");

/**
 * @generated from message com.stablemoney.api.identity.CurrentTimeResponse
 */
export type CurrentTimeResponse = Message<"com.stablemoney.api.identity.CurrentTimeResponse"> & {
  /**
   * @generated from field: string current_timezone = 1;
   */
  currentTimezone: string;

  /**
   * @generated from field: string current_time_iso = 2;
   */
  currentTimeIso: string;

  /**
   * @generated from field: int64 current_time_epoch_milli = 3;
   */
  currentTimeEpochMilli: bigint;
};

/**
 * Describes the message com.stablemoney.api.identity.CurrentTimeResponse.
 * Use `create(CurrentTimeResponseSchema)` to create a new message.
 */
export const CurrentTimeResponseSchema: GenMessage<CurrentTimeResponse> = /*@__PURE__*/
  messageDesc(file_Time, 0);

