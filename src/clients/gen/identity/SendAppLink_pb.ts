// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file SendAppLink.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file SendAppLink.proto.
 */
export const file_SendAppLink: GenFile = /*@__PURE__*/
  fileDesc("ChFTZW5kQXBwTGluay5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSIqChJTZW5kQXBwTGlua1JlcXVlc3QSFAoMcGhvbmVfbnVtYmVyGAEgASgJIhUKE1NlbmRBcHBMaW5rUmVzcG9uc2VCJAogY29tLnN0YWJsZW1vbmV5LmFwaS5ub3RpZmljYXRpb25QAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.identity.SendAppLinkRequest
 */
export type SendAppLinkRequest = Message<"com.stablemoney.api.identity.SendAppLinkRequest"> & {
  /**
   * @generated from field: string phone_number = 1;
   */
  phoneNumber: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SendAppLinkRequest.
 * Use `create(SendAppLinkRequestSchema)` to create a new message.
 */
export const SendAppLinkRequestSchema: GenMessage<SendAppLinkRequest> = /*@__PURE__*/
  messageDesc(file_SendAppLink, 0);

/**
 * @generated from message com.stablemoney.api.identity.SendAppLinkResponse
 */
export type SendAppLinkResponse = Message<"com.stablemoney.api.identity.SendAppLinkResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.SendAppLinkResponse.
 * Use `create(SendAppLinkResponseSchema)` to create a new message.
 */
export const SendAppLinkResponseSchema: GenMessage<SendAppLinkResponse> = /*@__PURE__*/
  messageDesc(file_SendAppLink, 1);

