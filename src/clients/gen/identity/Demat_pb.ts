// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Demat.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Demat.proto.
 */
export const file_Demat: GenFile = /*@__PURE__*/
  fileDesc("CgtEZW1hdC5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSJBChlHZXRQcm92aWRlckRldGFpbHNSZXF1ZXN0EiQKHHBhcnRpYWxfZGVtYXRfYWNjb3VudF9udW1iZXIYASABKAkiVgofR2V0RGVtYXRQcm92aWRlckRldGFpbHNSZXNwb25zZRITCgtwcm92aWRlcl9pZBgBIAEoCRIMCgRuYW1lGAIgASgJEhAKCGljb25fdXJsGAMgASgJIjYKFkFkZERlbWF0QWNjb3VudFJlcXVlc3QSHAoUZGVtYXRfYWNjb3VudF9udW1iZXIYAyABKAkiGQoXQWRkRGVtYXRBY2NvdW50UmVzcG9uc2VCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.identity.GetProviderDetailsRequest
 */
export type GetProviderDetailsRequest = Message<"com.stablemoney.api.identity.GetProviderDetailsRequest"> & {
  /**
   * @generated from field: string partial_demat_account_number = 1;
   */
  partialDematAccountNumber: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GetProviderDetailsRequest.
 * Use `create(GetProviderDetailsRequestSchema)` to create a new message.
 */
export const GetProviderDetailsRequestSchema: GenMessage<GetProviderDetailsRequest> = /*@__PURE__*/
  messageDesc(file_Demat, 0);

/**
 * @generated from message com.stablemoney.api.identity.GetDematProviderDetailsResponse
 */
export type GetDematProviderDetailsResponse = Message<"com.stablemoney.api.identity.GetDematProviderDetailsResponse"> & {
  /**
   * @generated from field: string provider_id = 1;
   */
  providerId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string icon_url = 3;
   */
  iconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.GetDematProviderDetailsResponse.
 * Use `create(GetDematProviderDetailsResponseSchema)` to create a new message.
 */
export const GetDematProviderDetailsResponseSchema: GenMessage<GetDematProviderDetailsResponse> = /*@__PURE__*/
  messageDesc(file_Demat, 1);

/**
 * @generated from message com.stablemoney.api.identity.AddDematAccountRequest
 */
export type AddDematAccountRequest = Message<"com.stablemoney.api.identity.AddDematAccountRequest"> & {
  /**
   * @generated from field: string demat_account_number = 3;
   */
  dematAccountNumber: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AddDematAccountRequest.
 * Use `create(AddDematAccountRequestSchema)` to create a new message.
 */
export const AddDematAccountRequestSchema: GenMessage<AddDematAccountRequest> = /*@__PURE__*/
  messageDesc(file_Demat, 2);

/**
 * @generated from message com.stablemoney.api.identity.AddDematAccountResponse
 */
export type AddDematAccountResponse = Message<"com.stablemoney.api.identity.AddDematAccountResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.identity.AddDematAccountResponse.
 * Use `create(AddDematAccountResponseSchema)` to create a new message.
 */
export const AddDematAccountResponseSchema: GenMessage<AddDematAccountResponse> = /*@__PURE__*/
  messageDesc(file_Demat, 3);

