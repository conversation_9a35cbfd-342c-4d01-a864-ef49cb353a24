// @generated by protoc-gen-es v2.6.2 with parameter "target=ts,import_extension=js"
// @generated from file Algo360.proto (package com.stablemoney.api.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv2";

/**
 * Describes the file Algo360.proto.
 */
export const file_Algo360: GenFile = /*@__PURE__*/
  fileDesc("Cg1BbGdvMzYwLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5KtEBCg5BbGdvU3RhdHVzVHlwZRIVChFEQVRBX0lOR0VTVEVEX05FVxAAEh0KGURBVEFfSU5HRVNURURfSU5DUkVNRU5UQUwQARIcChhBTEdPMzYwX1BST0NFU1NfRklOSVNIRUQQAhIoCiRBTEdPMzYwX1BSSU9SSVRZX1ZBUklBQkxFU19QUk9DRVNTRUQQAxIjCh9BTEdPMzYwX0dBUF9WQVJJQUJMRVNfUFJPQ0VTU0VEEAQSHAoYVU5LTk9XTl9BTEdPX1NUQVRVU19UWVBFEAVCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM");

/**
 * @generated from enum com.stablemoney.api.identity.AlgoStatusType
 */
export enum AlgoStatusType {
  /**
   * @generated from enum value: DATA_INGESTED_NEW = 0;
   */
  DATA_INGESTED_NEW = 0,

  /**
   * @generated from enum value: DATA_INGESTED_INCREMENTAL = 1;
   */
  DATA_INGESTED_INCREMENTAL = 1,

  /**
   * @generated from enum value: ALGO360_PROCESS_FINISHED = 2;
   */
  ALGO360_PROCESS_FINISHED = 2,

  /**
   * @generated from enum value: ALGO360_PRIORITY_VARIABLES_PROCESSED = 3;
   */
  ALGO360_PRIORITY_VARIABLES_PROCESSED = 3,

  /**
   * @generated from enum value: ALGO360_GAP_VARIABLES_PROCESSED = 4;
   */
  ALGO360_GAP_VARIABLES_PROCESSED = 4,

  /**
   * @generated from enum value: UNKNOWN_ALGO_STATUS_TYPE = 5;
   */
  UNKNOWN_ALGO_STATUS_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.AlgoStatusType.
 */
export const AlgoStatusTypeSchema: GenEnum<AlgoStatusType> = /*@__PURE__*/
  enumDesc(file_Algo360, 0);

