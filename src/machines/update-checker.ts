import { assign, fromPromise, setup } from "xstate";
import { useMachine } from "@xstate/react";
import { useEffect } from "react";
import { useLocation } from "@tanstack/react-router";

type UpdateCheckerContext = {
  currentManifest?: string;
};

type UpdateCheckerEvents = { type: "CHECK" };

async function fetchManifest(): Promise<string> {
  const response = await fetch("/.vite/manifest.json", {
    cache: "no-cache",
    headers: {
      "Cache-Control": "no-cache",
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch manifest: ${response.status}`);
  }

  return response.text();
}

export const updateCheckerMachine = setup({
  types: {
    context: {} as UpdateCheckerContext,
    events: {} as UpdateCheckerEvents,
  },
  guards: {
    manifestChanged: ({ context }, params: { manifestContent: string }) => {
      if (!context.currentManifest) return false;
      // Don't trigger reload if manifest is empty (happens on fresh loads)
      if (params.manifestContent.trim() === "") return false;
      return context.currentManifest !== params.manifestContent;
    },
  },
  actors: {
    manifestFetcher: fromPromise(fetchManifest),
  },
}).createMachine({
  id: "updateChecker",
  initial: "idle",
  context: {
    currentManifest: undefined,
  },
  states: {
    idle: {
      on: {
        CHECK: "fetching",
      },
    },
    fetching: {
      invoke: {
        src: "manifestFetcher",
        onDone: [
          {
            target: "reloading",
            guard: {
              type: "manifestChanged",
              params: ({ event }) => ({ manifestContent: event.output }),
            },
          },
          {
            target: "idle",
            actions: assign({
              currentManifest: ({ event, context }) =>
                context.currentManifest ?? event.output,
            }),
          },
        ],
        onError: {
          target: "idle",
          actions: ({ event }) => {
            console.warn(
              "Failed to fetch manifest for update check:",
              event.error
            );
          },
        },
      },
    },
    reloading: {
      entry: () => {
        console.info("New deployment detected, reloading page...");
        window.location.reload();
      },
      type: "final",
    },
  },
});

export function useUpdateChecker() {
  const location = useLocation();
  const [state, send] = useMachine(updateCheckerMachine);

  useEffect(() => {
    // Trigger update check on path changes
    send({ type: "CHECK" });
  }, [location.pathname, send]);

  return {
    status: state.value as "idle" | "fetching" | "reloading",
    currentManifest: state.context.currentManifest,
    check: () => send({ type: "CHECK" }),
  };
}
