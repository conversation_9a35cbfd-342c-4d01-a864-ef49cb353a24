import { StrictMode } from "react";
import ReactDOM from "react-dom/client";
import { RouterProvider } from "@tanstack/react-router";
import { routerWithQueryClient } from "@tanstack/react-router-with-query";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createRouter } from "./router";
import * as Sentry from "@sentry/react";
import "./styles/app.css";

// Create Query Client and Router (router integrates QueryClient via router-with-query)
const queryClient = new QueryClient();
const router = routerWithQueryClient(createRouter(), queryClient);

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const appEnv = import.meta.env.VITE_APP_ENV;
if (appEnv && appEnv !== "development") {
  Sentry.init({
    dsn: "https://<EMAIL>/4509824847708160",
    integrations: [Sentry.tanstackRouterBrowserTracingIntegration(router)],
    tracesSampleRate: 1.0,
    sendDefaultPii: true,
    environment: appEnv || "development",
  });
}

const rootElement = document.getElementById("root")!;
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <RouterProvider router={router} />
      </QueryClientProvider>
    </StrictMode>
  );
}
