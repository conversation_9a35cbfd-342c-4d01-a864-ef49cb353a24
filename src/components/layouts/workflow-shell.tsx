import BackAppbarAction from "../functional/back-error-action";
import SupportAppbarAction from "../functional/support-appbar-action";
import AppBar from "../ui/app-bar/app-bar";

export default function WorkflowShell({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <AppBar
        scrollStyleOptions={{
          offsetStyles: {
            backgroundColor: {
              threshold: 20,
              before: "transparent",
              after: "var(--color-bg)",
            },
            borderBottomWidth: {
              threshold: 20,
              before: 0,
              after: "var(--border-w-sm)",
            },
          },
        }}
        left={<BackAppbarAction />}
        right={<SupportAppbarAction />}
      />
      {children}
    </>
  );
}
