import { normalizeProps, useMachine } from "@zag-js/react";
import * as toast from "@zag-js/toast";
import { useEffect, useId, useState } from "react";
import Toast from "./toast";
import { toaster } from "./store";
import { createPortal } from "react-dom";

export default function Toaster() {
  const service = useMachine(toast.group.machine, {
    id: useId(),
    store: toaster,
  });
  const api = toast.group.connect(service, normalizeProps);

  // Defer accessing document until after mount to keep SSR safe
  const [container, setContainer] = useState<HTMLElement | null>(null);
  useEffect(() => {
    setContainer(document.getElementById("toaster-root"));
  }, []);

  if (!container) return null;

  return createPortal(
    <div {...api.getGroupProps()}>
      {api
        .getToasts()
        .reverse()
        .map((toast, index) => (
          <Toast key={toast.id} actor={toast} parent={service} index={index} />
        ))}
    </div>,
    container
  );
}
