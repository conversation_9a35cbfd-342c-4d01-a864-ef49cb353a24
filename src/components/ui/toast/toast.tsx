import clsx from "clsx";
import type { ToastProps } from "./types";
import { normalizeProps, useMachine } from "@zag-js/react";
import * as toast from "@zag-js/toast";

export default function Toast({ actor, index, parent, className }: ToastProps) {
  const composedProps = { ...actor, index, parent };

  const service = useMachine(toast.machine, composedProps);
  const api = toast.connect(service, normalizeProps);
  return (
    <div
      className={clsx(
        "text-body1 flex items-center gap-2 rounded-xl border  p-4 justify-center",
        {
          "bg-[#3A2C15] text-white border-white-80": api.type === "info",
          "text-red border-red bg-white": api.type === "error",
        },
        className
      )}
      {...api.getRootProps()}
    >
      <div {...api.getDescriptionProps()}>{api.description}</div>
    </div>
  );
}
