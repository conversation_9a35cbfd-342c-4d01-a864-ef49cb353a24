import { useScrollStyle } from "@/hooks/scroll-styles";
import type { AppBarProps } from "./types";
import clsx from "clsx";
import useResizeObserver, {
  type UseResizeObserverCallback,
} from "@react-hook/resize-observer";
import { isFlutterWebView } from "@/utils/routing";

const onResize: UseResizeObserverCallback = ({ borderBoxSize }) => {
  let height = borderBoxSize?.[0]?.blockSize || 115;
  if (isFlutterWebView()) {
    height = 115;
  }
  document.documentElement.style.setProperty("--app-bar-height", `${height}px`);
};

export default function AppBar({
  left,
  right,
  children,
  scrollStyleOptions,
  className,
  ...rest
}: AppBarProps) {
  const ref = useScrollStyle(scrollStyleOptions);
  useResizeObserver(ref, onResize);
  return (
    <header
      ref={ref}
      className={clsx(
        "border-b-black-10 pt-safe sticky top-0 z-30 border-solid",
        className
      )}
      {...rest}
    >
      <div className="relative flex items-center justify-between gap-4 px-5 py-4">
        <div className="flex-1">{left}</div>
        <div>{right}</div>
        <h1
          className="title text-black-50 text-title-all-caps absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-center font-medium"
          style={{ maxWidth: "calc(100% - calc(18 * var(--spacing)))" }}
        >
          {children}
        </h1>
      </div>
    </header>
  );
}
