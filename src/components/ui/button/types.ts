import type { ReactNode, HTMLAttributes } from "react";

export type ButtonProps = {
  type?: "submit" | "reset" | "button";
  href?: string;
  replace?: boolean;
  target?: "_blank" | "_self" | "_parent" | "_top";
  loading?: boolean;
  hasShimmer?: boolean;
  disabled?: boolean;
  form?: string;
  size?: "medium" | "small";
  children: ReactNode;
  variant?: "primary" | "secondary";
  darkMode?: boolean;
} & HTMLAttributes<HTMLElement>;

export type LinkButtonProps = Omit<ButtonProps, "hasShimmer" | "size">;
