import { type ReactNode, type HTMLAttributes } from "react";
import { useScrollStyle, type ScrollStyleParams } from "@/hooks/scroll-styles";
import clsx from "clsx";
import { createPortal } from "react-dom";

type Props = HTMLAttributes<HTMLDivElement> & {
  children?: ReactNode;
  scrollStyleParams?: ScrollStyleParams;
  className?: string;
  style?: "transparent" | "primary";
};

export default function FloatingFooterContent({
  children,
  scrollStyleParams,
  className,
  style = "primary",
  ...props
}: Props) {
  const mobileRef = useScrollStyle(scrollStyleParams);
  const portalTarget =
    typeof document !== "undefined"
      ? document.querySelector("#floating-footer-content")
      : null;

  if (!portalTarget || !children) {
    return null;
  }

  return createPortal(
    <div
      ref={mobileRef as React.RefObject<HTMLDivElement>}
      className={clsx(
        "p-5 empty:hidden",
        style === "primary" &&
          "bg-[#16110A] border-white-20  border border-t-[1px]",
        style === "transparent" && "bg-transparent",
        className
      )}
      {...props}
    >
      {children}
    </div>,
    portalTarget
  );
}
