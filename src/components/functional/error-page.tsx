import { useState, useEffect, useCallback } from "react";
import Button from "@/components/ui/button/button";
import LinkButton from "@/components/ui/button/link-button";
import { getErrorMessage } from "@/utils/errors";
import errorIcon from "@/assets/icons/error-orange.svg";
import FloatingFooterContent from "../ui/floating-footer-content";
import { trackEvent } from "@/utils/analytics";
import { supportLink } from "@/config/support";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getUserContextQueryOptions } from "@/queries/context";

type ErrorPageProps = (
  | {
      error: unknown;
      title?: string;
      description?: string;
    }
  | {
      error?: never;
      title: string;
      description?: string;
    }
) & {
  onRetry?: () => void;
};

export default function ErrorPage({
  error,
  title,
  description,
  onRetry,
}: ErrorPageProps) {
  const [errorTitle, setTitle] = useState(title);
  const [errorDescription] = useState(description);
  const { data: contextQuery } = useSuspenseQuery(getUserContextQueryOptions());

  const reload = useCallback(
    function () {
      trackEvent("retry");
      if (onRetry) {
        onRetry();
        return;
      }
      window.location.reload();
    },
    [onRetry]
  );

  useEffect(() => {
    if (title) {
      if (title === "404 Page Not Found") {
        return;
      }
      trackEvent("error_page_viewed", {
        message: title,
        url: window.location.href,
      });
      return;
    }

    getErrorMessage(error).then((result) => {
      setTitle(result);
      trackEvent("error_page_viewed", {
        message: result,
        url: window.location.href,
      });
    });
  }, [error, title]);

  const buttons = (
    <div className="w-full space-y-3 text-center">
      <LinkButton
        href={supportLink(contextQuery?.props?.["invested"] === "true")}
        className="text-white"
      >
        Need help? Call us
      </LinkButton>
      <Button onClick={reload} variant="secondary">
        Retry
      </Button>
    </div>
  );

  return (
    <>
      <div className="app-bar-height-top-padding floating-footer-padding fixed inset-0 flex flex-col justify-center gap-7 md:contents">
        <img src={errorIcon} alt="error_icon" className="mx-auto mt-5" />
        <div className="space-y-3 px-5 text-center">
          <p className="text-heading3 text-white">{errorTitle}</p>
          {errorDescription && (
            <p className="text-body text-white">{errorDescription}</p>
          )}
        </div>
      </div>
      <FloatingFooterContent>{buttons}</FloatingFooterContent>
    </>
  );
}
