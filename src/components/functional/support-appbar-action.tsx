import React from "react";
import supportImage from "@/assets/icons/support.svg";
import { supportLink } from "@/config/support";
import { toaster } from "../ui/toast/store";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { useLanguage } from "@/contexts/language-context";
import { withInCallWindow } from "@/utils/time";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { getUserContextQueryOptions } from "@/queries/context";

type SupportAppbarActionProps = React.ButtonHTMLAttributes<HTMLButtonElement>;

export default function SupportAppbarAction(props: SupportAppbarActionProps) {
  const { language } = useLanguage();
  const { data: contextQuery } = useSuspenseQuery(getUserContextQueryOptions());
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const handleRequestCallBack = () => {
    const withinCallWindow = withInCallWindow();
    trackEvent("kbc_expert_call clicked", {
      language: getLanguageFromEnum(language),
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      call_mode: withinCallWindow ? "dial" : "callback",
    });

    if (withinCallWindow) {
      // During 8:00 AM - 11:59 PM IST, trigger phone dialer
      window.location.href = supportLink(
        contextQuery?.props?.["invested"] === "true"
      );
      return;
    }

    // Outside call window (12:00 AM - 7:59 AM IST), keep existing callback flow
    toaster.create({
      description:
        language === LanguageCode.EN
          ? "We've got your request. Our team will reach out by 9:00 AM."
          : "हमारी टीम सुबह 9:00 बजे तक आपसे संपर्क करेगी।",
      type: "info",
      duration: 3000,
    });
  };
  return (
    <button {...props} onClick={handleRequestCallBack}>
      <img decoding="sync" src={supportImage} alt="Call" className="size-9" />
    </button>
  );
}
