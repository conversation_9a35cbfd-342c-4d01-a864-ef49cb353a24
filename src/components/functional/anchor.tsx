import type { AnchorHTMLAttributes, <PERSON><PERSON>, MouseEvent } from "react";
import { trackEvent } from "@/utils/analytics";
import { Link, useNavigate } from "@tanstack/react-router";
import { toaster } from "../ui/toast/store";

type AnchorProps = AnchorHTMLAttributes<HTMLAnchorElement> & {
  ref?: Ref<HTMLAnchorElement>;
  replace?: boolean;
};

export default function Anchor({
  children,
  href,
  onClick,
  replace,
  ...props
}: AnchorProps) {
  const navigate = useNavigate();

  const destination = new URL(href!, window.location.origin);

  function handleClick(event: MouseEvent<HTMLAnchorElement>) {
    event.preventDefault();
    if (destination.pathname === "/show-toast") {
      const message = destination.searchParams.get("message");
      const type = destination.searchParams.get("type") ?? "info";
      trackEvent("toast_shown", {
        message: message,
        type: type,
      });
      toaster.create({
        description: message,
        type: type,
        duration: 2000,
      });
      onClick?.(event);
      return;
    }
    if (href) {
      navigate({ to: href, replace: replace });
    }
    onClick?.(event);
  }

  if (destination.host !== window.location.host) {
    return (
      <a
        href={href!}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
        onClick={onClick}
      >
        {children}
      </a>
    );
  }

  return (
    <Link to={href!} {...props} onClick={handleClick} replace={replace}>
      {children}
    </Link>
  );
}
