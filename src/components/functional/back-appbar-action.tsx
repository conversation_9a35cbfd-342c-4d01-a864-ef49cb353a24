import { type ButtonHTMLAttributes } from "react";
import backWhiteImage from "@/assets/icons/back-white.svg";
import backImage from "@/assets/icons/back.webp";
import { goBack } from "@/utils/routing";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useLanguage } from "@/contexts/language-context";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useLocation } from "@tanstack/react-router";

type BackAppbarActionProps = {
  opaque?: boolean;
} & ButtonHTMLAttributes<HTMLButtonElement>;
export default function BackAppbarAction({
  opaque = false,
  ...props
}: BackAppbarActionProps) {
  const { language } = useLanguage();
  const location = useLocation();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const handleBack = () => {
    goBack();
    trackEvent("kbc_back_button_clicked", {
      language: getLanguageFromEnum(language),
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      screen_name: location.pathname,
    });
  };
  return (
    <button
      {...props}
      onClick={handleBack}
      name="back"
      className="flex items-center justify-center"
    >
      {opaque ? (
        <img
          decoding="sync"
          src={backWhiteImage}
          alt="Back"
          className="h-9 w-9"
        />
      ) : (
        <img decoding="sync" src={backImage} alt="Back" className="h-9 w-9" />
      )}
    </button>
  );
}
