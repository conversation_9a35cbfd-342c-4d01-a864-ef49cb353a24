import { type ButtonHTMLAttributes } from "react";
import backWhiteImage from "@/assets/icons/back-white.svg";
import backImage from "@/assets/icons/back.webp";
import { goBack } from "@/utils/routing";

type BackAppbarActionProps = {
  opaque?: boolean;
} & ButtonHTMLAttributes<HTMLButtonElement>;
export default function BackAppbarAction({
  opaque = false,
  ...props
}: BackAppbarActionProps) {
  const handleBack = () => {
    goBack();
  };
  return (
    <button
      {...props}
      onClick={handleBack}
      name="back"
      className="flex items-center justify-center"
      type="button"
    >
      {opaque ? (
        <img
          decoding="sync"
          src={backWhiteImage}
          alt="Back"
          className="h-9 w-9"
        />
      ) : (
        <img decoding="sync" src={backImage} alt="Back" className="h-9 w-9" />
      )}
    </button>
  );
}
