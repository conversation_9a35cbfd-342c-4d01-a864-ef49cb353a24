import FloatingFooterContent from "@/components/ui/floating-footer-content";
import type { HTMLAttributes } from "react";

type FloatingGoldButtonProps = {
  children: React.ReactNode;
  style?: "transparent" | "primary";
} & HTMLAttributes<HTMLButtonElement>; // Add the necessary HTMLAttributes for the button element;
export default function FloatingGoldButton({
  children,
  style = "transparent",
  ...rest
}: FloatingGoldButtonProps) {
  return (
    <FloatingFooterContent style={style}>
      <button
        {...rest}
        className="rounded-xl text-center bg-linear-to-tr from-[#E0B96C] to-[#FFEAAF] py-3 w-full font-medium"
      >
        {children}
      </button>
    </FloatingFooterContent>
  );
}
