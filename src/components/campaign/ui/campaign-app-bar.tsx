import type { HTMLAttributes, ReactNode } from "react";
import type { ScrollStyleParams } from "@/hooks/scroll-styles";
import AppBar from "@/components/ui/app-bar/app-bar";

export type AppBarProps = {
  left?: ReactNode;
  right?: ReactNode;
  children?: ReactNode;
  scrollStyleOptions?: ScrollStyleParams<
    keyof CSSStyleDeclaration,
    keyof CSSStyleDeclaration
  >;
} & HTMLAttributes<HTMLElement>;

export default function CampaignAppBar({ ...rest }: AppBarProps) {
  return <AppBar {...rest} />;
}
