import clsx from "clsx";
import type { SurfaceProps } from "./types";

export default function Surface({
  elevation = "none",
  borderWidth = "sm",
  className,
  children,
  ...rest
}: SurfaceProps) {
  return (
    <div
      className={clsx(
        "overflow-hidden rounded-xl border",
        borderWidth === "md" ? "border" : "border-[0.5px]",
        "border-white-60 bg-linear-to-br from-[#36322C] to-[#1A160F]",
        className,
        { "shadow-md": elevation === "md" }
      )}
      {...rest}
    >
      {children}
    </div>
  );
}
