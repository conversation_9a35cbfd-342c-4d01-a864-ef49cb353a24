import React, { useEffect, useState } from "react";
import GoldenTick from "@/assets/icons/gold-tick.svg";
import GoldenError from "@/assets/icons/gold-error.svg";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";

type NextQuestionTimerProps = {
  onComplete: () => void;
  children: React.ReactNode;
  startCountdown?: boolean;
  isCorrect?: boolean;
  isNotLastQuestion?: boolean;
};

const NextQuestionTimer: React.FC<NextQuestionTimerProps> = ({
  onComplete,
  children,
  startCountdown = false,
  isCorrect = false,
  isNotLastQuestion = false,
}) => {
  const { language } = useLanguage();
  const [countdown, setCountdown] = useState(isNotLastQuestion ? 4 : 2);

  useEffect(() => {
    if (!startCountdown) {
      return;
    }

    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev - 1 === 0) {
          onComplete();
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [onComplete, startCountdown]);

  return (
    <div className="flex flex-col items-center text-center text-white gap-8">
      <div className="flex flex-col items-center gap-3">
        <img
          src={isCorrect ? GoldenTick : GoldenError}
          alt="Tick/Error"
          className="w-8 h-8"
        />
        <p className="text-heading4 text-white-80">{children}</p>
      </div>
      <div className="flex flex-col items-center gap-4">
        <div className="w-40 border-t border-1 border-white-20" />
        <div className="text-heading4 ">
          {isNotLastQuestion
            ? language === LanguageCode.EN
              ? `Next question in ${countdown}...`
              : `अगला प्रश्न ${countdown} सेकंड में...`
            : language === LanguageCode.EN
              ? `Submitting your responses...`
              : `आपके उत्तर जमा किए जा रहे हैं...`}
        </div>
      </div>
    </div>
  );
};

export default NextQuestionTimer;
