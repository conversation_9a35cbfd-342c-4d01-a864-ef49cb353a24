import { useState, useEffect } from "react";
import clsx from "clsx";
import { useLanguage } from "@/contexts/language-context";
import LinkButton from "@/components/ui/button/link-button";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import goldenTick from "@/assets/icons/gold-tick.svg";
import { trackEvent } from "@/utils/analytics";
import { getLanguageFromEnum } from "@/utils/events";
import {
  getDailyPollQueryOptions,
  getNotificationQueryOptions,
  getQuizStatusQueryOptions,
} from "@/queries/kbc";
import { queryClient } from "@/queries/client";
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { requestNotificationPermission } from "@/utils/routing";

type TimeLeft = {
  hours: number;
  minutes: number;
  seconds: number;
};

function PollGradientText({
  children,
  size = "medium",
  isSymbol = false,
}: {
  children: React.ReactNode;
  size: "small" | "medium";
  isSymbol?: boolean;
}) {
  return (
    <div
      className={clsx(
        "bg-linear-to-tr from-[#E0B96C] to-[#FFEAAF] text-transparent bg-clip-text text-center",
        {
          "text-[28px] tracking-[-0.5px] leading-[32px]": size === "medium",
          "text-[13px] tracking-[-0.2px]": size === "small",
          "w-7": !isSymbol,
        }
      )}
    >
      {children}
    </div>
  );
}

function PollWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div className="rounded-sm border border-white bg-linear-to-br from-white/14 to-white/6 px-2 py-1.5">
      {children}
    </div>
  );
}

export default function PollTimer() {
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { data: notificationPermissionState } = useQuery(
    getNotificationQueryOptions()
  );

  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    hours: 12,
    minutes: 0,
    seconds: 0,
  });

  const getISTTime = (): Date => {
    const now = new Date();
    const istTimeString = now.toLocaleString("en-US", {
      timeZone: "Asia/Kolkata",
    });
    return new Date(istTimeString);
  };

  const getNextPollTime = (): Date => {
    const now = getISTTime();
    const nextPoll = new Date(now);
    nextPoll.setHours(21, 0, 0, 0);

    if (now >= nextPoll) {
      nextPoll.setDate(nextPoll.getDate() + 1);
    }

    return nextPoll;
  };

  const calculateTimeLeft = (): TimeLeft => {
    const now = getISTTime();
    const nextPoll = getNextPollTime();
    const diff = nextPoll.getTime() - now.getTime();
    if (diff > 0) {
      return {
        hours: Math.floor(diff / (1000 * 60 * 60)),
        minutes: Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((diff % (1000 * 60)) / 1000),
      };
    }
    if (diff === 0) {
      queryClient.invalidateQueries(getQuizStatusQueryOptions()).then(() => {
        queryClient.invalidateQueries(
          getDailyPollQueryOptions({
            isEligible: quizStatus.nextStep?.isPollEligible,
          })
        );
      });
    }
    return { hours: 0, minutes: 0, seconds: 0 };
  };

  useEffect(() => {
    setTimeLeft(calculateTimeLeft());

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const [remindMePressed, setRemindMePressed] = useState(
    !!localStorage.getItem("kbc-poll-remind-pressed")
  );
  const formatTime = (val: number): string => val.toString().padStart(2, "0");
  const handleRemindMe = () => {
    trackEvent("kbc_poll_reminder_set", {
      language: getLanguageFromEnum(language),
      screen_name: location.pathname,
    });
    if (notificationPermissionState !== "granted") {
      requestNotificationPermission();
    }
    setRemindMePressed(true);
    localStorage.setItem("kbc-poll-remind-pressed", "true");
  };

  return (
    <div className="flex flex-col items-center gap-4">
      <div className="flex items-center space-x-2">
        <PollWrapper>
          <PollGradientText size="medium">
            {formatTime(timeLeft.hours)}
          </PollGradientText>
          <PollGradientText size="small">Hrs</PollGradientText>
        </PollWrapper>

        <PollGradientText size="medium" isSymbol>
          :
        </PollGradientText>

        <PollWrapper>
          <PollGradientText size="medium">
            {formatTime(timeLeft.minutes)}
          </PollGradientText>
          <PollGradientText size="small">Min</PollGradientText>
        </PollWrapper>

        <PollGradientText size="medium" isSymbol>
          :
        </PollGradientText>

        <PollWrapper>
          <PollGradientText size="medium">
            {formatTime(timeLeft.seconds)}
          </PollGradientText>
          <PollGradientText size="small">Sec</PollGradientText>
        </PollWrapper>
      </div>
      {remindMePressed ? (
        <div className="flex gap-1.5 text-white text-body1 items-center">
          <img src={goldenTick} className="size-3.5" />{" "}
          <span>
            {language === LanguageCode.EN
              ? "We’ll remind you at 9 PM"
              : "हम आपको रात 9 बजे याद दिलाएँगे"}
          </span>
        </div>
      ) : (
        <LinkButton darkMode onClick={handleRemindMe} className="translate-x-1">
          {language === LanguageCode.EN ? "Remind me" : "मुझे याद दिलाएँ"}
        </LinkButton>
      )}
    </div>
  );
}
