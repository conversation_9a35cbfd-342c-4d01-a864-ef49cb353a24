import exitButton from "@/assets/illustrations/mutual-funds/exit-button.svg";
import stayButton from "@/assets/illustrations/mutual-funds/stay-button.svg";
import { closeWebview } from "@/utils/routing";
import { useEffect } from "react";
import { trackEvent } from "@/utils/analytics";
import { useLocation } from "@tanstack/react-router";

type MutualFundsBlockerProps = {
  reset: () => void;
  question: number;
};

export function MutualFundsBlocker({
  reset,
  question,
}: MutualFundsBlockerProps) {
  const location = useLocation();
  useEffect(() => {
    trackEvent("mutual_funds_exit_confirmation_viewed", {
      question_number: question,
      screen_name: location.pathname,
    });
  }, [question, location.pathname]);
  const handleClose = () => {
    trackEvent("mutual_funds_exit_confirmation_clicked", {
      question_number: question,
      screen_name: location.pathname,
      cta: "exit",
    });
    closeWebview();
  };

  const handleContinue = () => {
    trackEvent("mutual_funds_exit_confirmation_clicked", {
      question_number: question,
      screen_name: location.pathname,
      cta: "continue",
    });
    reset();
  };
  return (
    <div className="fixed inset-0 z-50 bg-black-50  flex items-center justify-center px-5">
      <div className="bg-[#2B1F0E] rounded-xl border border-white p-5 text-white space-y-9">
        <div className="space-y-2 text-center">
          <div className="text-heading1">
            <p> Are you sure</p>
            <p> you want to exit? </p>
          </div>
          <p className="px-4 text-heading4 text-white-80">
            You will miss out on learning some interesting facts about Gold &
            Silver
          </p>
        </div>
        <div className="flex gap-3 justify-center">
          <button onClick={handleClose}>
            <img src={exitButton} alt="Exit" />
          </button>
          <button onClick={handleContinue}>
            <img src={stayButton} alt="Continue" />
          </button>
        </div>
      </div>
    </div>
  );
}
