import { useState, useEffect } from "react";
import clsx from "clsx";
import {
  getDailyPollQueryOptions,
  getQuizStatusQueryOptions,
} from "@/queries/kbc";
import { queryClient } from "@/queries/client";
import { useSuspenseQuery } from "@tanstack/react-query";

type TimeLeft = {
  hours: number;
  minutes: number;
  seconds: number;
};

function PollGradientText({
  children,
  size = "medium",
  isSymbol = false,
}: {
  children: React.ReactNode;
  size: "small" | "medium";
  isSymbol?: boolean;
}) {
  return (
    <div
      className={clsx(
        "bg-linear-to-tr from-[#E0B96C] to-[#FFEAAF] text-transparent bg-clip-text text-center ",
        {
          "text-[20px] tracking-[-0.2px] font-medium": size === "medium",
          "text-[10px]": size === "small",
          "w-5": !isSymbol,
        }
      )}
    >
      {children}
    </div>
  );
}

function PollWrapper({ children }: { children: React.ReactNode }) {
  return (
    <div className="rounded-sm border border-white bg-linear-to-br from-white/14 to-white/6 px-1.25 py-1.5">
      {children}
    </div>
  );
}

export default function ScorePollTimer() {
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    hours: 12,
    minutes: 0,
    seconds: 0,
  });

  const getISTTime = (): Date => {
    const now = new Date();
    const istTimeString = now.toLocaleString("en-US", {
      timeZone: "Asia/Kolkata",
    });
    return new Date(istTimeString);
  };

  const getNextPollTime = (): Date => {
    const now = getISTTime();
    const nextPoll = new Date(now);
    nextPoll.setHours(21, 0, 0, 0);

    if (now >= nextPoll) {
      nextPoll.setDate(nextPoll.getDate() + 1);
    }

    return nextPoll;
  };

  const calculateTimeLeft = (): TimeLeft => {
    const now = getISTTime();
    const nextPoll = getNextPollTime();
    const diff = nextPoll.getTime() - now.getTime();
    if (diff > 0) {
      return {
        hours: Math.floor(diff / (1000 * 60 * 60)),
        minutes: Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((diff % (1000 * 60)) / 1000),
      };
    }
    if (diff === 0) {
      queryClient.invalidateQueries(getQuizStatusQueryOptions()).then(() => {
        queryClient.invalidateQueries(
          getDailyPollQueryOptions({
            isEligible: quizStatus.nextStep?.isPollEligible,
          })
        );
      });
    }
    return { hours: 0, minutes: 0, seconds: 0 };
  };

  useEffect(() => {
    setTimeLeft(calculateTimeLeft());

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (val: number): string => val.toString().padStart(2, "0");

  return (
    <div className="flex items-center space-x-1">
      <PollWrapper>
        <PollGradientText size="medium">
          {formatTime(timeLeft.hours)}
        </PollGradientText>
        <PollGradientText size="small">Hrs</PollGradientText>
      </PollWrapper>

      <PollGradientText size="medium" isSymbol>
        :
      </PollGradientText>

      <PollWrapper>
        <PollGradientText size="medium">
          {formatTime(timeLeft.minutes)}
        </PollGradientText>
        <PollGradientText size="small">Min</PollGradientText>
      </PollWrapper>

      <PollGradientText size="medium" isSymbol>
        :
      </PollGradientText>

      <PollWrapper>
        <PollGradientText size="medium">
          {formatTime(timeLeft.seconds)}
        </PollGradientText>
        <PollGradientText size="small">Sec</PollGradientText>
      </PollWrapper>
    </div>
  );
}
