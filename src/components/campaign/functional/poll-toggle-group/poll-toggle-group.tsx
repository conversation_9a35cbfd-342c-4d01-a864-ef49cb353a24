import { normalizeProps, useMachine } from "@zag-js/react";
import * as toggle from "@zag-js/toggle-group";
import { useId } from "react";
import ToggleItem from "./poll-toggle-item";
import { getPrefixedLabel } from "@/utils/string";

type PollToggleGroupProps = {
  options: { label: string; value: string; percentage: number }[];
  onChange?: (details: toggle.ValueChangeDetails) => void;
  multiple?: boolean;
  deselectable?: boolean;
  disabled?: boolean;
  handleMultipleConfirmation?: (details: toggle.ValueChangeDetails) => void;
};
export function PollToggleGroup({
  options,
  onChange,
  multiple,
  deselectable,
  disabled,
}: PollToggleGroupProps) {
  const service = useMachine(toggle.machine, {
    id: useId(),
    orientation: "vertical",
    onValueChange: onChange,
    multiple: multiple,
    disabled: disabled,
    deselectable: deselectable,
  });
  const api = toggle.connect(service, normalizeProps);

  return (
    <div {...api.getRootProps()} className="space-y-3">
      {options.map((option, index) => (
        <ToggleItem
          key={option.value}
          {...api.getItemProps({ value: option.value })}
          percentage={option.percentage}
          showPercentage={disabled}
        >
          {getPrefixedLabel(option.label, index)}
        </ToggleItem>
      ))}
    </div>
  );
}
