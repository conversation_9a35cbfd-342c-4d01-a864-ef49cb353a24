import type { HTMLAttributes } from "react";
import clsx from "clsx";
import { useEffect, useState } from "react";

export type ToggleItemProps = {
  children: React.ReactNode;
  percentage?: number;
  showPercentage?: boolean;
} & HTMLAttributes<HTMLElement>;

export default function ToggleItem({
  className,
  children,
  percentage,
  showPercentage,
  ...props
}: ToggleItemProps) {
  const [animatedPercentage, setAnimatedPercentage] = useState(0);

  useEffect(() => {
    if (showPercentage && percentage) {
      const timer = setTimeout(() => {
        setAnimatedPercentage(percentage);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setAnimatedPercentage(0);
    }
  }, [showPercentage, percentage]);

  const classes = clsx(
    "cursor-pointer flex-shrink-0 py-3 px-4 text-white bg-linear-to-br from-white-20 to-white-0 backdrop-blur-xs  w-full rounded-xl border border-white text-left text-white relative overflow-hidden",
    className
  );
  return (
    <button className={classes} {...props}>
      {showPercentage && (
        <div
          className="absolute inset-0 bg-[#DEDEDE4D] rounded-xl transition-all duration-1000 ease-out"
          style={{ width: `${animatedPercentage}%` }}
        />
      )}
      <div className="flex justify-between gap-2 relative z-10">
        <span>{children}</span>
        {showPercentage && (
          <p className="text-heading4 text-white">{percentage?.toFixed(0)}%</p>
        )}
      </div>
    </button>
  );
}
