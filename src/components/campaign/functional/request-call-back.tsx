import Surface from "../ui/surface/surface";
import Avatar from "@/assets/illustrations/score/avatar.svg";
import RequestCallBackButton from "@/assets/illustrations/score/request-call-back-button.svg";
import RequestCallBackButtonHindi from "@/assets/illustrations/hindi/request-call-back-button.svg";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import LinkButton from "@/components/ui/button/link-button";
import { toaster } from "@/components/ui/toast/store";
import { useLanguage } from "@/contexts/language-context";
import { useLocation, useNavigate } from "@tanstack/react-router";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";

export default function RequestCallBack({
  isSkippable = false,
}: {
  isSkippable?: boolean;
}) {
  const navigate = useNavigate();
  const { language } = useLanguage();
  const location = useLocation();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const handleRequestCallBack = () => {
    toaster.create({
      description:
        language === LanguageCode.EN
          ? "Our team will reachout to you in sometime"
          : "हमारे विशेषज्ञ आपसे कुछ ही देर में संपर्क करेंगे",
      type: "info",
      duration: 2000,
    });
    trackEvent("kbc_expert_call clicked", {
      language: getLanguageFromEnum(language),
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    setTimeout(() => {
      navigate({ to: "/campaign/kbc/poll", replace: true });
    }, 3000);
  };
  return (
    <Surface>
      <div className="flex flex-col p-5 gap-5 items-center text-white text-center">
        <img src={Avatar} alt="avatar" className="size-18" />
        <div className="flex flex-col gap-5 items-center">
          <p className="text-body1">
            {language === LanguageCode.EN
              ? "You have unlocked exclusive Stable Money expert call to help you with your next FD"
              : "आपने अपनी अगली FD के लिए Stable Money के एक्सक्लूसिव एक्सपर्ट कॉल को अनलॉक कर लिया है"}
          </p>
          <button onClick={handleRequestCallBack}>
            {language === LanguageCode.EN ? (
              <img src={RequestCallBackButton} alt="" />
            ) : (
              <img src={RequestCallBackButtonHindi} alt="" />
            )}
          </button>
        </div>
        {isSkippable && (
          <LinkButton href={"/campaign/kbc/poll"} darkMode replace>
            {language === LanguageCode.EN ? "Skip" : "स्किप करें"}
          </LinkButton>
        )}
      </div>
    </Surface>
  );
}
