import exitButton from "@/assets/illustrations/exit-button.svg";
import continueButton from "@/assets/illustrations/continue-button.svg";
import { closeWebview } from "@/utils/routing";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { useEffect } from "react";
import { trackEvent } from "@/utils/analytics";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { useLocation } from "@tanstack/react-router";

type QuizBlockerProps = {
  reset: () => void;
  question: number;
};

export function QuizBlocker({ reset, question }: QuizBlockerProps) {
  const { language } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const location = useLocation();
  useEffect(() => {
    trackEvent("kbc_exit_confirmation_viewed", {
      language: getLanguageFromEnum(language),
      question_number: question,
      screen_name: location.pathname,
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
  });

  const handleClose = () => {
    trackEvent("kbc_exit_confirmation_clicked", {
      language: getLanguageFromEnum(language),
      question_number: question,
      screen_name: location.pathname,
      cta: "exit",
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    closeWebview();
  };

  const handleContinue = () => {
    trackEvent("kbc_exit_confirmation_clicked", {
      language: getLanguageFromEnum(language),
      question_number: question,
      screen_name: location.pathname,
      cta: "continue",
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
    });
    reset();
  };
  return (
    <div className="fixed inset-0 bg-black-50 backdrop-blur-xs flex items-center justify-center px-5">
      <div className="bg-[#2B1F0E] rounded-xl border border-white p-5 text-white space-y-9">
        <div className="space-y-2 text-center">
          <div className="text-heading1">
            {language === LanguageCode.EN ? (
              <>
                <p> Are you sure</p>
                <p> you want to quit? </p>
              </>
            ) : (
              <>
                <p>क्या आप सुनिश्चित हैं</p>
                <p>कि आप बाहर निकलना चाहते हैं?</p>
              </>
            )}
          </div>
          <p className="px-4 text-heading4 text-white-80">
            {language === LanguageCode.EN
              ? "You need to complete the contest to win prizes"
              : "पुरस्कार जीतने के लिए आपको प्रतियोगिता पूरी करनी होगी"}
          </p>
        </div>
        <div className="flex gap-3 justify-center">
          <button onClick={handleClose}>
            <img src={exitButton} alt="Exit" />
          </button>
          <button onClick={handleContinue}>
            <img src={continueButton} alt="Continue" />
          </button>
        </div>
      </div>
    </div>
  );
}
