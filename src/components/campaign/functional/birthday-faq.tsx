import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import LinkButton from "@/components/ui/button/link-button";
import Surface from "@/components/campaign/ui/surface/surface";
import { getFAQsQueryOptions } from "@/queries/business";
import { trackEvent } from "@/utils/analytics";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { useLanguage } from "@/contexts/language-context";
import { useLocation } from "@tanstack/react-router";
import { getKBCUserStatus, getLanguageFromEnum } from "@/utils/events";
import { isNotNullAndEmpty } from "@/utils/string";

export default function BirthdayFaqSupport() {
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const { language } = useLanguage();
  const location = useLocation();
  const query = useQuery(
    getFAQsQueryOptions({
      identifier: "BIRTHDAY_KBC_FAQ",
      namespace: "KBC",
    })
  );
  query.refetch();
  return (
    <>
      <QueryRenderer query={query}>
        {({ faqs }) => (
          <div className="w-full px-5 pb-6">
            <Surface elevation="md">
              <Accordion isDarkMode>
                {faqs.slice(0, 5).map((faq, index) => {
                  return (
                    <div key={faq.question} id={`faq-${index}`}>
                      <AccordionItem label={faq.question} isDarkMode>
                        <div
                          dangerouslySetInnerHTML={{
                            __html: isNotNullAndEmpty(faq.htmlAnswer)
                              ? faq.htmlAnswer
                              : faq.answer,
                          }}
                        />
                      </AccordionItem>
                    </div>
                  );
                })}
              </Accordion>
              <div className="px-5 pb-3">
                <div className="h-[1px] bg-white-10" />
              </div>
              <div className="pb-5 text-center">
                <LinkButton
                  className="text-white-50"
                  onClick={() => {
                    trackEvent("kbc_viewall_faq_clicked", {
                      screen_name: location.pathname,
                      language: getLanguageFromEnum(language),
                      user_status: getKBCUserStatus(
                        quizStatus?.nextStep?.nextStep
                      ),
                    });
                  }}
                  href="/campaign/kbc/birthday/faq"
                >
                  View all
                </LinkButton>
              </div>
            </Surface>
          </div>
        )}
      </QueryRenderer>
    </>
  );
}
