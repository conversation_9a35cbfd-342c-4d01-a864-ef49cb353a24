import type { HTMLAttributes } from "react";
import clsx from "clsx";

export type QuizToggleItemProps = {
  children: React.ReactNode;
  selected?: boolean;
  verdict?: boolean;
  isCorrect?: boolean;
  showResults?: boolean;
} & HTMLAttributes<HTMLElement>;

export default function QuizToggleItem({
  className,
  children,
  selected,
  verdict = false,
  isCorrect = false,
  showResults = false,
  ...props
}: QuizToggleItemProps) {
  const classes = clsx(
    "cursor-pointer flex-shrink-0  text-white w-full rounded-xl text-left text-white text-body1 relative",
    {
      "bg-linear-to-br from-white-20 to-white-0 backdrop-blur-xs border border-white py-3 px-4":
        !selected && !(showResults && isCorrect),
    },
    className
  );

  const getBackgroundColor = () => {
    if (selected) {
      return verdict ? "#48741D" : "#74271D";
    }
    if (showResults && isCorrect) {
      return "#48741D";
    }
    return undefined;
  };

  const getBorderGradient = () => {
    if (selected) {
      if (verdict)
        return "radial-gradient(108.64% 262.5% at 0% 0%, #FFBC2C 0%, rgba(255, 188, 44, 0.24) 50%, rgba(255, 188, 44, 0.44) 100%)";
      return "radial-gradient(108.64% 262.5% at 0% 0%, #FF482C 0%, rgba(255, 72, 44, 0.24) 50%, rgba(255, 72, 44, 0.44) 100%)";
    }
    if (showResults && isCorrect) {
      return "radial-gradient(108.64% 262.5% at 0% 0%, #FFBC2C 0%, rgba(255, 188, 44, 0.24) 50%, rgba(255, 188, 44, 0.44) 100%)";
    }
    return undefined;
  };

  const shouldShowGradientBorder = selected || (showResults && isCorrect);

  return (
    <button
      className={classes}
      {...props}
      style={{
        backgroundColor: getBackgroundColor(),
        ...(shouldShowGradientBorder && {
          background: `${getBorderGradient()} border-box`,
          border: "1px solid transparent",
        }),
      }}
    >
      {shouldShowGradientBorder && (
        <div
          className="rounded-xl flex items-center justify-start px-4 py-3"
          style={{
            backgroundColor: getBackgroundColor(),
          }}
        >
          {children}
        </div>
      )}
      {!shouldShowGradientBorder && children}
    </button>
  );
}
