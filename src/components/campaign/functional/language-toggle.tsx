import * as toggle from "@zag-js/toggle-group";
import { useMachine, normalizeProps } from "@zag-js/react";
import { useId } from "react";
import clsx from "clsx";
import engSelect from "@/assets/illustrations/language-switch-toggle/eng-select.svg?url";
import hiSelect from "@/assets/illustrations/language-switch-toggle/hi-select.svg?url";
import switchLanguage from "@/assets/illustrations/language-switch-toggle/switch-language.svg";
import { trackEvent } from "@/utils/analytics";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getQuizStatusQueryOptions } from "@/queries/kbc";
import { getKBCUserStatus } from "@/utils/events";
import { useLocation } from "@tanstack/react-router";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";

const LANG_OPTIONS = [
  { label: "En", value: "en" },
  { label: "ही", value: "hi" },
];

export default function LanguageToggle() {
  const { language, setLanguage } = useLanguage();
  const { data: quizStatus } = useSuspenseQuery(getQuizStatusQueryOptions());
  const location = useLocation();
  const handleChangeLanguage = (details: toggle.ValueChangeDetails) => {
    trackEvent("kbc_language_toggle", {
      language: details.value[0],
      user_status: getKBCUserStatus(quizStatus?.nextStep?.nextStep),
      screen_name: location.pathname,
    });
    setLanguage(details.value[0] === "en" ? LanguageCode.EN : LanguageCode.HI);
  };

  const service = useMachine(toggle.machine, {
    id: useId(),
    defaultValue: [language === LanguageCode.EN ? "en" : "hi"],
    onValueChange: handleChangeLanguage,
    deselectable: false,
  });
  const api = toggle.connect(service, normalizeProps);
  return (
    <div className="relative">
      <div {...api.getRootProps()}>
        {LANG_OPTIONS.map((option) => (
          <button
            key={option.value}
            {...api.getItemProps({ value: option.value })}
            className={clsx("flex items-center justify-center", {
              hidden: api.value[0] === option.value,
            })}
          >
            {option.value === "hi" ? (
              <img src={engSelect} alt="English" />
            ) : (
              <img src={hiSelect} alt="Hindi" />
            )}
          </button>
        ))}
      </div>
      <div className="absolute -bottom-4">
        <img src={switchLanguage} alt="Switch language" />
      </div>
    </div>
  );
}
