import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import { getFAQsQueryOptions } from "@/queries/business";
import { isNotNullAndEmpty } from "@/utils/string";

export default function AllBirthdayFaqs() {
  const query = useQuery(
    getFAQsQueryOptions({
      identifier: "BIRTHDAY_KBC_FAQ",
      namespace: "KBC",
    })
  );
  query.refetch();
  return (
    <QueryRenderer query={query}>
      {({ faqs }) => (
        <Accordion isDarkMode>
          {faqs.map((faq) => (
            <AccordionItem key={faq.question} label={faq.question} isDarkMode>
              <div
                dangerouslySetInnerHTML={{
                  __html: isNotNullAndEmpty(faq.htmlAnswer)
                    ? faq.htmlAnswer
                    : faq.answer,
                }}
              />
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </QueryRenderer>
  );
}
