import type { HTMLAttributes } from "react";
import clsx from "clsx";
import greenButtonBackground from "@/assets/illustrations/green-button-background.webp";
import redButtonBackground from "@/assets/illustrations/red-button-background.webp";

export type QuizToggleItemProps = {
  children: React.ReactNode;
  selected?: boolean;
  verdict?: boolean;
  isCorrect?: boolean;
  showResults?: boolean;
} & HTMLAttributes<HTMLElement>;

export default function QuizToggleItem({
  className,
  children,
  selected,
  verdict = false,
  isCorrect = false,
  showResults = false,
  ...props
}: QuizToggleItemProps) {
  const classes = clsx(
    "cursor-pointer flex-shrink-0 py-3 px-4 text-white w-full rounded-xl border border-white text-left text-white",
    {
      "bg-cover font-medium": selected || (showResults && isCorrect),
      "bg-linear-to-br from-white-20 to-white-0 backdrop-blur-xs": !selected,
    },
    className
  );

  const getBackgroundImage = () => {
    if (selected) {
      return verdict ? greenButtonBackground : redButtonBackground;
    }
    if (showResults && isCorrect) {
      return greenButtonBackground;
    }
    return undefined;
  };

  return (
    <button
      className={classes}
      {...props}
      style={{
        backgroundImage: getBackgroundImage()
          ? `url(${getBackgroundImage()})`
          : undefined,
      }}
    >
      {children}
    </button>
  );
}
