import { normalizeProps, useMachine } from "@zag-js/react";
import * as toggle from "@zag-js/toggle-group";
import { useId } from "react";
import QuizToggleItem from "./quiz-toggle-item";
import { getPrefixedLabel } from "@/utils/string";

type QuizToggleGroupProps = {
  options: { label: string; value: string; verdict?: boolean }[];
  onChange?: (details: toggle.ValueChangeDetails) => void;
  multiple?: boolean;
  deselectable?: boolean;
  disabled?: boolean;
  showResults?: boolean;
};
export function QuizToggleGroup({
  options,
  onChange,
  multiple,
  deselectable,
  disabled,
  showResults = false,
}: QuizToggleGroupProps) {
  const service = useMachine(toggle.machine, {
    id: useId(),
    orientation: "vertical",
    onValueChange: onChange,
    multiple: multiple,
    disabled: disabled,
    deselectable: deselectable,
  });
  const api = toggle.connect(service, normalizeProps);

  return (
    <div {...api.getRootProps()} className="space-y-3">
      {options.map((option, index) => (
        <QuizToggleItem
          key={option.value}
          {...api.getItemProps({ value: option.value })}
          selected={api.value.includes(option.value)}
          verdict={option.verdict}
          isCorrect={option.verdict}
          showResults={showResults}
        >
          {getPrefixedLabel(option.label, index)}
        </QuizToggleItem>
      ))}
    </div>
  );
}
