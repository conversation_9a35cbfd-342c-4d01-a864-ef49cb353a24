import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import { getFAQsQueryOptions } from "@/queries/business";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { isNotNullAndEmpty } from "@/utils/string";

export default function AllFaqs() {
  const { language } = useLanguage();
  const query = useQuery(
    getFAQsQueryOptions({
      identifier: "KBC_FAQ",
      namespace: "KBC",
      enabled: language === LanguageCode.EN,
    })
  );
  const hindiQuery = useQuery(
    getFAQsQueryOptions({
      identifier: "KBC_FAQ_HINDI",
      namespace: "KBC_HINDI",
      enabled: language === LanguageCode.HI,
    })
  );

  return (
    <QueryRenderer query={language === LanguageCode.EN ? query : hindiQuery}>
      {({ faqs }) => (
        <Accordion isDarkMode>
          {faqs.map((faq) => (
            <AccordionItem key={faq.question} label={faq.question} isDarkMode>
              <div
                dangerouslySetInnerHTML={{
                  __html: isNotNullAndEmpty(faq.htmlAnswer)
                    ? faq.htmlAnswer
                    : faq.answer,
                }}
              />
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </QueryRenderer>
  );
}
