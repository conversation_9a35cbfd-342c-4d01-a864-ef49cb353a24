import type { HTMLAttributes } from "react";
import buttonSelectedBackground from "@/assets/illustrations/gold-button-background.webp";
import clsx from "clsx";
import blackTick from "@/assets/icons/black-tick.svg";

export type ToggleItemProps = {
  children: React.ReactNode;
  selected?: boolean;
  multiple?: boolean;
} & HTMLAttributes<HTMLElement>;

export default function ToggleItem({
  className,
  children,
  selected,
  multiple,
  ...props
}: ToggleItemProps) {
  const classes = clsx(
    "cursor-pointer flex-shrink-0 py-3 px-4  w-full rounded-xl border border-white text-left ",
    {
      "bg-cover text-[#201B13] font-medium": selected,
      "bg-linear-to-br from-white-20 to-white-0 backdrop-blur-xs  text-white":
        !selected,
    },
    className
  );
  return (
    <button
      className={classes}
      style={
        selected
          ? { backgroundImage: `url(${buttonSelectedBackground})` }
          : undefined
      }
      {...props}
    >
      <div className="flex justify-between gap-2 ">
        {children}
        {selected && multiple && <img src={blackTick} alt="Black Tick " />}
      </div>
    </button>
  );
}
