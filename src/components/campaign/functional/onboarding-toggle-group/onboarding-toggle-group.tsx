import { normalizeProps, useMachine } from "@zag-js/react";
import * as toggle from "@zag-js/toggle-group";
import { useId } from "react";
import ToggleItem from "./onboarding-toggle-item";
import { getPrefixedLabel } from "@/utils/string";

type OnboardingToggleGroupProps = {
  options: { label: string; value: string }[];
  onChange?: (details: toggle.ValueChangeDetails) => void;
  multiple?: boolean;
  deselectable?: boolean;
  disabled?: boolean;
  handleMultipleConfirmation?: (details: toggle.ValueChangeDetails) => void;
};
export function OnboardingToggleGroup({
  options,
  onChange,
  multiple,
  deselectable,
  disabled,
}: OnboardingToggleGroupProps) {
  const service = useMachine(toggle.machine, {
    id: useId(),
    orientation: "vertical",
    onValueChange: onChange,
    multiple: multiple,
    disabled: disabled,
    deselectable: deselectable,
  });
  const api = toggle.connect(service, normalizeProps);

  return (
    <div {...api.getRootProps()} className="space-y-3 floating-footer-padding">
      {options.map((option, index) => (
        <ToggleItem
          key={option.value}
          {...api.getItemProps({ value: option.value })}
          selected={api.value.includes(option.value)}
          multiple={multiple}
        >
          {getPrefixedLabel(option.label, index)}
        </ToggleItem>
      ))}
    </div>
  );
}
