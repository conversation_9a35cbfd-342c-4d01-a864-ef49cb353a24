import React from "react";
import supportImage from "@/assets/icons/support.svg";
import { supportLink } from "@/config/support";
import { useLanguage } from "@/contexts/language-context";
import { trackEvent } from "@/utils/analytics";
import { getLanguageFromEnum } from "@/utils/events";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getUserContextQueryOptions } from "@/queries/context";
import { useLocation } from "@tanstack/react-router";

type SupportAppbarActionProps = React.ButtonHTMLAttributes<HTMLButtonElement>;

export default function SupportAppbarAction(props: SupportAppbarActionProps) {
  const { language } = useLanguage();
  const location = useLocation();
  const { data: contextQuery } = useSuspenseQuery(getUserContextQueryOptions());
  const handleRequestCallBack = () => {
    trackEvent("mutual_funds_expert_call clicked", {
      language: getLanguageFromEnum(language),
      screen_name: location.pathname,
    });
    window.location.href = supportLink(
      contextQuery?.props?.["invested"] === "true"
    );
  };
  return (
    <button {...props} onClick={handleRequestCallBack}>
      <img decoding="sync" src={supportImage} alt="Call" className="size-9" />
    </button>
  );
}
