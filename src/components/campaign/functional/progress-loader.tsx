import { useEffect, useState } from "react";
import backgroundImage from "@/assets/illustrations/background-image.webp";
import ProgressBar from "@/components/ui/progress-bar/progress-bar";
import { useLanguage } from "@/contexts/language-context";
import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import AppBar from "@/components/ui/app-bar/app-bar";
import appBarLogo from "@/assets/icons/appbar-logo.webp";

type OnboardingLoaderProps = {
  children?: React.ReactNode;
  backgroundColor?: string;
  showHaveAppBarAction?: boolean;
  duration?: number;
};

export default function ProgressLoader({
  children,
  backgroundColor,
  showHaveAppBarAction,
  duration = 3000,
}: OnboardingLoaderProps) {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const incrementInterval = duration / 98; // Divide duration by 98 steps

    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 98) {
          clearInterval(interval);
          return 98;
        }
        return prev + 1;
      });
    }, incrementInterval);

    return () => clearInterval(interval);
  }, [duration]);
  const { language } = useLanguage();
  return (
    <>
      {showHaveAppBarAction && (
        <AppBar>
          <img src={appBarLogo} alt="" width={92} />
        </AppBar>
      )}

      <div
        className="absolute inset-0 bg-cover bg-no-repeat bg-center w-full h-full z-50 flex items-center justify-center bg-[#141009]"
        style={
          backgroundColor
            ? { backgroundColor }
            : { backgroundImage: `url(${backgroundImage})` }
        }
      >
        {children}
        <div className="absolute bottom-12.5 left-12.5 right-12.5 space-y-5">
          <ProgressBar
            barColor="#FFFFFF"
            bgColor="#3f3f3f"
            progress={progress}
          />
          <div className="text-body1 text-center text-white-60">
            {language === LanguageCode.EN ? (
              <>
                <p> Do not close the app or</p>
                <p> tap the back button </p>
              </>
            ) : (
              <p>ऐप बंद न करें और बैक बटन न दबाएं</p>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
