import { goBack } from "@/utils/routing";
import { <PERSON> } from "@tanstack/react-router";

export function NotFound({ children }: { children?: React.ReactNode }) {
  return (
    <div className="space-y-2 p-2">
      <div className="text-white">
        {children || <p>The page you are looking for does not exist.</p>}
      </div>
      <p className="flex items-center gap-2 flex-wrap">
        <button
          onClick={goBack}
          className="bg-emerald-500 text-white px-2 py-1 rounded-sm uppercase  text-sm"
        >
          Go back
        </button>
        <Link
          to="/campaign/kbc"
          className="bg-cyan-600 text-white px-2 py-1 rounded-sm uppercase  text-sm"
        >
          Start Over
        </Link>
      </p>
    </div>
  );
}
