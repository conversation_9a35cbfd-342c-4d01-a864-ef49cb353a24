import { LanguageCode } from "@/clients/gen/business/campaign_pb";
import { createContext, useContext, useEffect, useState } from "react";

interface LanguageContextType {
  language: LanguageCode;
  setLanguage: (language: LanguageCode) => void;
}

const LanguageContext = createContext<LanguageContextType | null>(null);

const STORAGE_KEY = "preferredLanguage";

function getInitialLanguage(): LanguageCode {
  if (typeof window === "undefined") return LanguageCode.EN;

  try {
    const raw = localStorage.getItem(STORAGE_KEY);
    if (!raw) return LanguageCode.EN;
    const numValue = Number(raw);
    if (!Number.isNaN(numValue) && LanguageCode[numValue] !== undefined) {
      return numValue as LanguageCode;
    }

    if (LanguageCode[raw as keyof typeof LanguageCode] !== undefined) {
      return LanguageCode[raw as keyof typeof LanguageCode];
    }
  } catch (error) {
    console.error(error);
  }

  return LanguageCode.EN;
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<LanguageCode>(() =>
    getInitialLanguage()
  );

  useEffect(() => {
    if (typeof window === "undefined") return;
    try {
      const enumObj: { [key: number]: string } = LanguageCode;
      const stored =
        typeof language === "number" && enumObj[language]
          ? enumObj[language] // store enum key name if numeric enum
          : String(language); // store value as string otherwise
      window.localStorage.setItem(STORAGE_KEY, stored);
    } catch (error) {
      console.error(error);
    }
  }, [language]);

  return (
    <LanguageContext.Provider value={{ language, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    return { language: LanguageCode.EN, setLanguage: () => {} };
  }
  return context;
}
