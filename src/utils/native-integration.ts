import type { JsonValue } from "@bufbuild/protobuf";

/**
 * Check if Flutter WebView is available
 */
function isFlutterWebView(): boolean {
  return typeof window.flutter_inappwebview?.callHandler === "function";
}

/**
 * Analytics helper functions
 */
export const analytics = {
  isSupported(): boolean {
    return isFlutterWebView();
  },
  /**
   * Track an event via native analytics
   */
  track(event: string, properties: Record<string, JsonValue> = {}) {
    if (isFlutterWebView()) {
      return window.flutter_inappwebview!.callHandler(
        "trackAnalyticsEvent",
        event,
        properties
      );
    }
  },
};

/**
 * Authentication helper functions
 */
export const authentication = {
  /**
   * Check if authentication component is supported
   */
  isSupported(): boolean {
    return isFlutterWebView();
  },

  /**
   * Get native token
   */
  getToken(type: "access_token" | "refresh_token"): Promise<string | null> {
    if (isFlutterWebView()) {
      if (type === "access_token") {
        return window.flutter_inappwebview!.callHandler("getAccessToken");
      } else {
        return window.flutter_inappwebview!.callHandler("getRefreshToken");
      }
    }

    return Promise.resolve(null);
  },
};

/**
 * Share helper functions
 */
export const share = {
  /**
   * Check if share component is supported
   */
  isSupported(): boolean {
    return isFlutterWebView();
  },

  /**
   * Share text content
   */
  shareText(text: string) {
    if (isFlutterWebView()) {
      return window.flutter_inappwebview!.callHandler("share", text);
    }
  },
};
