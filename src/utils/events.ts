import { LanguageCode, Step } from "@/clients/gen/business/campaign_pb";

export const getKBCUserStatus = (step: Step | undefined): string => {
  switch (step) {
    case Step.ONBOARDING:
      return "KBC_NEW";
    case Step.QUIZ:
      return "KBC_ONBOARDED";
    case Step.POLL:
      return "KBC_CONTEST_ENTERED";
    default:
      return "KBC_NEW";
  }
};

export const getLanguageFromEnum = (language: LanguageCode): string => {
  switch (language) {
    case LanguageCode.EN:
      return "en";
    case LanguageCode.HI:
      return "hi";
    default:
      return "en";
  }
};
