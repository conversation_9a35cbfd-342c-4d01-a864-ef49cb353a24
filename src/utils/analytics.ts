import mixpanel from "mixpanel-browser";
import * as native from "./native-integration";
import { queryClient } from "@/queries/client";
import { getUserProfileQueryOptions } from "@/queries/profile";
import * as Sentry from "@sentry/react";
export function initAnalytics(token: string) {
  mixpanel.init(token, { autocapture: false, record_sessions_percent: 100 });
}

function getAnalyticsProperties() {
  const profile = queryClient.getQueryData(
    getUserProfileQueryOptions().queryKey
  );
  return {
    lifeTimeStatus: profile?.lifeTimeStatus ?? "Unknown",
    status: profile?.currentStatus ?? "Unknown",
    platform: window.flutter_inappwebview ? "PLATFORM_FLUTTER" : "PLATFORM_WEB",
  };
}

export function trackEvent(
  event: string,
  properties?: Record<string, unknown>
) {
  const analyticsProperties = {
    ...properties,
    ...getAnalyticsProperties(),
  };
  console.info(`Event tracked`, event, analyticsProperties);
  if (native.analytics.isSupported()) {
    return native.analytics.track(event, analyticsProperties);
  }
  Sentry.addBreadcrumb({
    type: "info",
    message: event,
    category: "analytics",
    data: analyticsProperties,
  });
  mixpanel.track(event, analyticsProperties);
  window.gtag?.("event", event, analyticsProperties);
}
