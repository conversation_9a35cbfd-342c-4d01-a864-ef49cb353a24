export function withInCallWindow() {
  // Determine current time in IST (Asia/Kolkata)
  const parts = new Intl.DateTimeFormat("en-US", {
    timeZone: "Asia/Kolkata",
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  }).formatToParts(new Date());
  const hourStr = parts.find((p) => p.type === "hour")?.value ?? "0";
  const hour = Number(hourStr);
  const withinCallWindow = hour >= 8 && hour <= 22; // 8:00 to 23:59 IST
  return withinCallWindow;
}

/**
 * Returns today's date in IST (Asia/Kolkata) formatted as DD-MM-YYYY.
 */
export function getTodayDateInIST_DDMMYYYY(): string {
  const now = new Date();
  const parts = new Intl.DateTimeFormat("en-GB", {
    timeZone: "Asia/Kolkata",
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  }).formatToParts(now);

  const day = parts.find((p) => p.type === "day")?.value ?? "";
  const month = parts.find((p) => p.type === "month")?.value ?? "";
  const year = parts.find((p) => p.type === "year")?.value ?? "";

  return `${day}-${month}-${year}`;
}

/**
 * Returns the KBC winner date in IST (Asia/Kolkata) formatted as DD-MM-YYYY.
 * The "day" runs from 9:00 PM to 9:00 PM IST.
 * - From today 9:00 PM IST to tomorrow 8:59:59 PM IST -> returns today's date
 * - Before 9:00 PM IST -> returns yesterday's date
 */
export function getKbcWinnerDate(): string {
  const now = new Date();

  // Get current hour in IST (24-hour format)
  const timeParts = new Intl.DateTimeFormat("en-US", {
    timeZone: "Asia/Kolkata",
    hour12: false,
    hour: "2-digit",
  }).formatToParts(now);
  const hourStr = timeParts.find((p) => p.type === "hour")?.value ?? "0";
  const hourIST = Number(hourStr);

  // If before 21:00 IST, target is yesterday; else today
  const target =
    hourIST < 21 ? new Date(now.getTime() - 24 * 60 * 60 * 1000) : now;

  const parts = new Intl.DateTimeFormat("en-GB", {
    timeZone: "Asia/Kolkata",
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  }).formatToParts(target);

  const day = parts.find((p) => p.type === "day")?.value ?? "";
  const month = parts.find((p) => p.type === "month")?.value ?? "";
  const year = parts.find((p) => p.type === "year")?.value ?? "";

  return `${day}-${month}-${year}`;
}

/**
 * Returns an array of KBC winner dates from the current winner date to 12-08-2025.
 * Each date follows the same DD-MM-YYYY format and KBC day logic as getKbcWinnerDate().
 */
export function getKbcAllWinnerDates(): string[] {
  const startDate = "12-08-2025";
  const endDate = getKbcWinnerDate();
  const [startDay, startMonth, startYear] = startDate.split("-").map(Number);
  const start = new Date(startYear, startMonth - 1, startDay);
  const [endDay, endMonth, endYear] = endDate.split("-").map(Number);
  const end = new Date(endYear, endMonth - 1, endDay);

  const dates: string[] = [];
  const current = new Date(start);
  while (current <= end) {
    const day = current.getDate().toString().padStart(2, "0");
    const month = (current.getMonth() + 1).toString().padStart(2, "0");
    const year = current.getFullYear().toString();
    dates.push(`${day}-${month}-${year}`);
    current.setDate(current.getDate() + 1);
  }

  return dates;
}
