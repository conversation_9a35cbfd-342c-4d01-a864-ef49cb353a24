import forge from "node-forge";

export function decryptAns(option: string): string {
  const privateKeyPem = `
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************    `.trim();
  const base64Encrypted = decodeURIComponent(option);
  const encryptedBytes = forge.util.decode64(base64Encrypted);
  const encryptedBinary = forge.util.createBuffer(encryptedBytes).getBytes();
  const privateKey = forge.pki.privateKeyFromPem(privateKeyPem);
  const decrypted = privateKey.decrypt(encryptedBinary, "RSAES-PKCS1-V1_5");
  return decrypted;
}
