export function isFlutterWebView() {
  return !!window.flutter_inappwebview;
}

export function goBack() {
  if (window.flutter_inappwebview) {
    const currentUrl = window.location.href;
    history.back();
    setTimeout(() => {
      if (window.location.href === currentUrl) {
        // The undefined values are required, without which the flutter navigation fails.
        return window.flutter_inappwebview?.callHandler(
          "navigate",
          "pop",
          undefined,
          undefined
        );
      }
    }, 100);
  } else {
    history.back();
  }
}

export function openTNC() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "push",
      "/scc-pdf/https%3A%2F%2Fassets.stablemoney.in%2FKBC%2520Contest%2520Terms%2520and%2520Conditions.pdf",
      "INAPP"
    );
  }
}

export function openSMS() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "replace",
      "/dynamic-tabs/search_and_filter",
      "INAPP"
    );
  }
}

export function closeWebview() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "pop",
      undefined,
      undefined
    );
  }
}

export function openAmazonVoucher() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "replace",
      "/dynamic/first_fd_reward_june_landing_page_v1",
      "INAPP"
    );
  }
}

export function openHighInterestFDPage() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "replace",
      "/collection/Collection_high_interest_fds",
      "INAPP"
    );
  }
}

export function openSuryoday() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "replace",
      "/fd-bank-details/b3798894-79f8-4582-ae86-812e842dda65",
      "INAPP"
    );
  }
}

export function getNotificationPermissionState() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "getNotificationPermissionState"
    );
  }
}

export function requestNotificationPermission() {
  if (window.flutter_inappwebview) {
    return window.flutter_inappwebview?.callHandler(
      "requestNotificationPermission"
    );
  }
}

export function navigate(path: string, e: Event) {
  if (window.flutter_inappwebview) {
    e.preventDefault();
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "push",
      path,
      "HOTWIRE"
    );
  }
}
