declare global {
  // Google Analytics gtag types
  type GtagCommand = "config" | "event" | "js" | "set" | "get";

  type GtagConfigParams = {
    page_title?: string;
    page_location?: string;
    page_path?: string;
    send_page_view?: boolean;
    custom_map?: Record<string, string>;
    [key: string]: unknown;
  };

  type GtagEventParams = {
    event_category?: string;
    event_label?: string;
    value?: number;
    currency?: string;
    transaction_id?: string;
    [key: string]: unknown;
  };

  interface Window {
    prerenderReady: boolean;

    // Google Analytics gtag function
    gtag?: {
      (command: "config", targetId: string, config?: GtagConfigParams): void;
      (
        command: "event",
        eventName: string,
        eventParams?: GtagEventParams
      ): void;
      (command: "js", date: Date): void;
      (command: "set", config: Record<string, UnknownAction>): void;
      (
        command: "get",
        targetId: string,
        fieldName: string,
        callback?: (value: unknown) => void
      ): void;
    };

    // Google Analytics dataLayer
    dataLayer?: unknwon[];

    // Flutter WebView InAppWebView types
    flutter_inappwebview?: FlutterInAppWebView;
  }

  interface FlutterInAppWebView {
    callHandler(
      handlerName: "share",
      text: string,
      imageUrl?: string
    ): Promise<void>;
    callHandler(
      handlerName: "navigate",
      presentation: FlutterPresentationType,
      path?: string,
      pathType?: FlutterPathType
    ): Promise<void>;
    callHandler(
      handlerName: "trackAnalyticsEvent",
      eventName: string,
      eventProperties?: Record<string, unknown>
    ): Promise<void>;
    callHandler(handlerName: "getAccessToken"): Promise<string>;
    callHandler(handlerName: "getRefreshToken"): Promise<string>;
    callHandler(
      handlerName: "changeStatusBar",
      darkMode: string
    ): Promise<void>;
    callHandler(handlerName: "requestNotificationPermission"): Promise<string>;
    callHandler(handlerName: "getNotificationPermissionState"): Promise<string>;
  }

  type FlutterHandlerName =
    | "share"
    | "navigate"
    | "trackAnalyticsEvent"
    | "getAccessToken"
    | "getRefreshToken"
    | "changeStatusBar";

  type FlutterPresentationType = "push" | "pop" | "replace" | "reset";

  type FlutterPathType =
    | "INAPP"
    | "INAPP_DASHBOARD"
    | "BOTTOMSHEET"
    | "UPSWING"
    | "EXTERNAL"
    | "DISMISS"
    | "HOTWIRE"
    | "UNKNOWN";

  type FlutterShareResponse = void;
  type FlutterNavigateResponse = void;
  type FlutterTrackAnalyticsResponse = void;
  type FlutterTokenResponse = string;
}

export {};
