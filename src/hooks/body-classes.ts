import { useEffect } from "react";
import { getClientOS } from "@/clients/http-context";
import { kebabCase } from "change-case";

/**
 * Hook to add OS name (ios/android) and hotwire-native classes to body element
 * This helps with platform-specific styling and feature detection
 */
export function useBodyClasses() {
  useEffect(() => {
    const body = document.body;
    const classes: string[] = [];

    if (window.flutter_inappwebview) {
      classes.push("flutter");
    }

    // Add OS-specific class
    const osName = getClientOS()?.toLowerCase();
    if (osName) {
      classes.push(kebabCase(osName));
    }

    // Add all classes to body
    if (classes.length > 0) {
      body.classList.add(...classes);
    }

    // Cleanup function to remove classes when component unmounts
    return () => {
      if (classes.length > 0) {
        body.classList.remove(...classes);
      }
    };
  }, []);
}

/**
 * Hook to add background image styles to body element
 */
export function useBodyBackground(backgroundImage: string) {
  useEffect(() => {
    const body = document.body;

    // Add background classes
    body.classList.add("bg-cover", "bg-no-repeat", "bg-center", "w-screen");

    // Set background image
    body.style.backgroundImage = `url(${backgroundImage})`;

    // Cleanup function - only remove what we added
    return () => {
      body.classList.remove(
        "bg-cover",
        "bg-no-repeat",
        "bg-center",
        "w-screen"
      );
      body.style.backgroundImage = "";
    };
  }, []);
}

export function useFloatingFooter() {
  useEffect(() => {
    const floatingFooter = document.getElementById("floating-footer")!;
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const height = Math.ceil(entry.borderBoxSize[0].blockSize);
        document.body.style.setProperty(
          "--floating-footer-height",
          `${height}px`
        );
      }
    });
    resizeObserver.observe(floatingFooter);
    return () => {
      resizeObserver.unobserve(floatingFooter);
    };
  }, []);
}
