import { useQuery } from "@tanstack/react-query";
import { authQueryOptions } from "@/queries/auth";
import { getUserProfileQueryOptions } from "@/queries/profile";
import * as Sentry from "@sentry/react";

export function useUserQuery() {
  const authTokenQuery = useQuery(authQueryOptions());
  const profileQuery = useQuery({
    ...getUserProfileQueryOptions(),
    enabled: !!authTokenQuery.data,
  });
  const userId = profileQuery.data?.data?.id;
  Sentry.setUser({ id: userId });
  if (userId) {
    import("mixpanel-browser").then(({ default: mixpanel }) => {
      mixpanel.identify(userId);
    });
  }
  return {
    profileQuery,
  };
}
