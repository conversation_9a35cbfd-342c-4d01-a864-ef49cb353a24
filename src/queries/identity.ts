import { CampaignResponseSchema } from "@/clients/gen/identity/Campaign_pb";
import { request } from "@/clients/identity-api";

export async function getCampaign(campaignName: string) {
  const responseData = await request({
    url: "/v1/campaign",
    method: "GET",
    params: { campaignType: campaignName },
    responseSchema: CampaignResponseSchema,
  });
  if (responseData.metadata.case === "kbcReferralCampaignMetadata") {
    return responseData.metadata.value;
  }
  throw new Error("Campaign not found");
}
