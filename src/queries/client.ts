import { UnauthenticatedException } from "@/exceptions/unauthenticated";
import { QueryClient } from "@tanstack/react-query";
import { HttpCallException } from "@/exceptions/http-call-exception";
import { getErrorMessage } from "@/utils/errors";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry(failureCount, error) {
        if (error instanceof UnauthenticatedException) {
          return false;
        }
        if (
          error instanceof HttpCallException &&
          [400, 401, 403, 404, 422, 429].includes(error.response.status)
        ) {
          return false;
        }
        return failureCount < 3;
      },
      retryDelay: 1000,
      throwOnError(error) {
        return error instanceof UnauthenticatedException;
      },
    },
    mutations: {
      onError: async (error) => {
        // Show error toast for all mutations
        const message = await getErrorMessage(error);
        console.error(message);
      },
    },
  },
});
