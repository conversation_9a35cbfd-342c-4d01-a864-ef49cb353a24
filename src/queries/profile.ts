import {
  UserDetailsResponseSchema,
  UserProfileResponseSchema,
} from "@/clients/gen/identity/Profile_pb";
import { getPage } from "@/clients/gen/personalization_api";
import { request } from "@/clients/identity-api";
import { queryOptions } from "@tanstack/react-query";

export async function getUser() {
  return request({
    url: `/v1/user`,
    method: "GET",
    responseSchema: UserDetailsResponseSchema,
  });
}

export function getUserQueryOptions() {
  return queryOptions({
    queryKey: ["user"],
    queryFn: getUser,
  });
}

export async function getUserProfile() {
  return request({
    url: `/v1/user/profile`,
    method: "GET",
    responseSchema: UserProfileResponseSchema,
  });
}

export function getUserProfileQueryOptions() {
  return queryOptions({
    queryKey: ["userProfile"],
    queryFn: getUserProfile,
  });
}

export function personalizationPageQuery(
  path: string,
  params: Record<string, unknown> = {}
) {
  return queryOptions({
    queryKey: ["personalization", "page", path, params],
    queryFn: () => getPage({ path, params: JSON.stringify(params) }),
  });
}
