import { request } from "@/clients/business-api";
import { create, toBinary } from "@bufbuild/protobuf";
import { queryOptions } from "@tanstack/react-query";
import {
  BusinessUnit,
  GetFaqsRequestSchema,
  GetFaqsResponseSchema,
} from "@/clients/gen/business/Faq_pb";

export async function getFAQs({
  namespace,
  identifier,
}: {
  namespace: string;
  identifier?: string;
}) {
  return request({
    method: "POST",
    url: "/v1/faq",
    data: toBinary(
      GetFaqsRequestSchema,
      create(GetFaqsRequestSchema, {
        namespace,
        identifier,
        businessUnit: BusinessUnit.ALPHA,
      })
    ),
    responseSchema: GetFaqsResponseSchema,
  });
}

export function getFAQsQueryOptions({
  namespace,
  identifier,
  enabled = false,
}: {
  namespace: string;
  identifier?: string;
  enabled?: boolean;
}) {
  return queryOptions({
    queryKey: ["faq", namespace, identifier],
    queryFn: () => getFAQs({ namespace, identifier }),
    enabled: enabled,
  });
}
