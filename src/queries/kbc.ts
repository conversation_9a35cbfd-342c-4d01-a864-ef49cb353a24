import {
  OnboardingSubmitRequestSchema,
  OnboardingSubmitResponseSchema,
  QuizResponseSchema,
  QuizStatusResponseSchema,
  SubmitQuizRequestSchema,
  SubmitQuizResponseSchema,
  type UserQuizAnswer,
} from "@/clients/gen/business/campaign_pb";
import { getDailyPoll } from "@/clients/gen/personalization_api";
import { request } from "@/clients/identity-api";
import { create, toBinary } from "@bufbuild/protobuf";
import { queryOptions } from "@tanstack/react-query";

export async function getQuizQuestions() {
  return request({
    url: `/v1/quiz`,
    method: "GET",
    responseSchema: QuizResponseSchema,
  });
}

export function getQuizQuestionsQueryOptions() {
  return queryOptions({
    queryKey: ["quizQuestions"],
    queryFn: () => getQuizQuestions(),
  });
}

export async function getQuizStatus() {
  return request({
    url: "/v1/quiz/status",
    method: "GET",
    responseSchema: QuizStatusResponseSchema,
  });
}

export function getQuizStatusQueryOptions() {
  return queryOptions({
    queryKey: ["quizStatus"],
    queryFn: getQuizStatus,
  });
}

function getNotificationPermissionState() {
  return window.flutter_inappwebview?.callHandler(
    "getNotificationPermissionState"
  );
}

export function getNotificationQueryOptions() {
  return queryOptions({
    queryKey: ["notificationPermission"],
    queryFn: getNotificationPermissionState,
  });
}

export async function submitQuiz(answers: UserQuizAnswer[]) {
  return request({
    url: "/v1/quiz",
    method: "POST",
    responseSchema: SubmitQuizResponseSchema,
    data: toBinary(
      SubmitQuizRequestSchema,
      create(SubmitQuizRequestSchema, {
        answers,
      })
    ),
  });
}

export function submitQuizQueryOptions({
  answers,
}: {
  answers: UserQuizAnswer[];
}) {
  return queryOptions({
    queryKey: ["submitQuiz", answers],
    queryFn: () => submitQuiz(answers),
  });
}

export async function quizOnboardingSubmit(
  onboardingAnswers: UserQuizAnswer[]
) {
  return request({
    url: "/v1/quiz/onboarding",
    method: "POST",
    responseSchema: OnboardingSubmitResponseSchema,
    data: toBinary(
      OnboardingSubmitRequestSchema,
      create(OnboardingSubmitRequestSchema, {
        answers: onboardingAnswers,
      })
    ),
  });
}

export function quizOnboardingSubmitQueryOptions({
  onboardingAnswers,
}: {
  onboardingAnswers: UserQuizAnswer[];
}) {
  return queryOptions({
    queryKey: ["quizOnboardingSubmit", onboardingAnswers],
    queryFn: () => quizOnboardingSubmit(onboardingAnswers),
  });
}

export function getDailyPollQueryOptions({
  isEligible = false,
}: {
  isEligible?: boolean;
}) {
  return queryOptions({
    queryKey: ["dailyPoll"],
    queryFn: getDailyPoll,
    enabled: isEligible,
  });
}
