import { QueryClient } from "@tanstack/react-query";
import { createRouter as createTanStackRouter } from "@tanstack/react-router";
import { routerWithQueryClient } from "@tanstack/react-router-with-query";
import { routeTree } from "./routeTree.gen";
import { initAnalytics } from "./utils/analytics";
import { BgImagePendingComponent } from "./components/campaign/functional/background-image-pending-component";
import ErrorBoundary from "./components/functional/error-boundary";
import { NotFound } from "./components/NotFound";
export function createRouter() {
  const queryClient = new QueryClient();
  initAnalytics(import.meta.env.VITE_MIXPANEL_TOKEN);
  return routerWithQueryClient(
    createTanStackRouter({
      routeTree,
      context: { queryClient },
      defaultPreload: "intent",
      defaultErrorComponent: ErrorBoundary,
      defaultNotFoundComponent: () => <NotFound />,
      defaultPendingComponent: BgImagePendingComponent,
      defaultPendingMinMs: 0,
      defaultPendingMs: 0,
      defaultViewTransition: true,
      scrollRestoration: true,
      disableGlobalCatchBoundary: true,
    }),
    queryClient
  );
}

declare module "@tanstack/react-router" {
  interface Register {
    router: ReturnType<typeof createRouter>;
  }
}
