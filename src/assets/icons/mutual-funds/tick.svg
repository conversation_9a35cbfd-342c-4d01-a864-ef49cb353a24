<svg width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-42" y="-42" width="118" height="118"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(21px);clip-path:url(#bgblur_0_2646_17518_clip_path);height:100%;width:100%"></div></foreignObject><circle data-figma-bg-blur-radius="42" cx="17" cy="17" r="16.5" fill="#48741D" stroke="url(#paint0_radial_2646_17518)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.0401 20.8469C15.9065 22.0189 14.0275 22.0191 12.8937 20.8471L10.5022 18.3751C9.94333 17.7975 9.9431 16.8808 10.5017 16.3029C11.0874 15.6968 12.0588 15.6966 12.6448 16.3025L17.0401 20.8469Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.358 21.5385C15.587 22.3356 14.3091 22.3356 13.5381 21.5385L12.875 20.8529L21.3335 12.1074C21.9192 11.5019 22.89 11.5019 23.4757 12.1074C24.0344 12.6851 24.0344 13.6017 23.4757 14.1793L16.358 21.5385Z" fill="white"/>
<defs>
<clipPath id="bgblur_0_2646_17518_clip_path" transform="translate(42 42)"><circle cx="17" cy="17" r="16.5"/>
</clipPath><radialGradient id="paint0_radial_2646_17518" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(1 1) rotate(45) scale(45.2548 83.9788)">
<stop stop-color="#FFBC2C"/>
<stop offset="0.500002" stop-color="#FFBC2C" stop-opacity="0.24"/>
<stop offset="1" stop-color="#FFBC2C" stop-opacity="0.44"/>
</radialGradient>
</defs>
</svg>
