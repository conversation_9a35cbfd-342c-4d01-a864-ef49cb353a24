name: Deployment

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      PROFILE:
        description: "The environment to deploy to"
        required: true
        default: staging
        type: choice
        options:
          - staging
          - production

jobs:
  deploy:
    name: Deploy to Cloudflare
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables based on profile
        run: |
          echo "VITE_GOOGLE_LOGIN_CLIENT_ID=${{ vars.VITE_GOOGLE_LOGIN_CLIENT_ID }}" >> $GITHUB_ENV
          echo "VITE_GOOGLE_LOGIN_CLIENT_SECRET=${{ vars.VITE_GOOGLE_LOGIN_CLIENT_SECRET }}" >> $GITHUB_ENV
          echo "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" >> $GITHUB_ENV
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            PROFILE="${{ github.event.inputs.PROFILE }}"
          else
            PROFILE="staging"
          fi

          if [ "$PROFILE" = "staging" ]; then
            echo "VITE_PERSONALIZATION_BASE_URL=${{ vars.VITE_PERSONALIZATION_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_IDENTITY_BASE_URL=${{ vars.VITE_IDENTITY_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_BUSINESS_BASE_URL=${{ vars.VITE_BUSINESS_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_CMS_BASE_URL=${{ vars.VITE_CMS_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_MIXPANEL_TOKEN=${{ vars.VITE_MIXPANEL_TOKEN_STAGING }}" >> $GITHUB_ENV
            echo "VITE_APP_ENV=staging" >> $GITHUB_ENV
            echo "DEPLOY_PROFILE=" >> $GITHUB_ENV
          elif [ "$PROFILE" = "production" ]; then
            echo "VITE_PERSONALIZATION_BASE_URL=${{ vars.VITE_PERSONALIZATION_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_IDENTITY_BASE_URL=${{ vars.VITE_IDENTITY_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_BUSINESS_BASE_URL=${{ vars.VITE_BUSINESS_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_CMS_BASE_URL=${{ vars.VITE_CMS_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_MIXPANEL_TOKEN=${{ vars.VITE_MIXPANEL_TOKEN_PROD }}" >> $GITHUB_ENV
            echo "VITE_APP_ENV=production" >> $GITHUB_ENV
            echo "DEPLOY_PROFILE=production" >> $GITHUB_ENV
          fi

      - name: Build project
        run: npm run build

      - name: Deploy with Wrangler
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: ${{ env.DEPLOY_PROFILE }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
