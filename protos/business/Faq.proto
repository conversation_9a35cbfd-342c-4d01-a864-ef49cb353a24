syntax = "proto3";

package com.stablemoney.api.business.faq;
option java_package = "com.stablemoney.api.business.faq";
option java_multiple_files = true;

message BankFaqResponse {
  repeated Faq bank_faq = 1;
}

message Faq {
  string question = 1;
  string answer = 2;
  string html_answer = 3;
  repeated string bank_faq_category_list = 4;
  string question_tag = 5;
  optional string faq_id = 6;
  int32 min_version_number = 7;
  int32 max_version_number = 8;
  optional string user_name = 9;
  optional string user_location = 10;
  optional string user_profile_url = 11;
  optional int32 upvotes = 12;
  optional int32 downvotes = 13;
}

message SupportFaqResponse {
  repeated Faq bank_faq = 1;
}

message SupportFaqV2 {
  Faq faq = 1;
  string support_faq_icon_url = 3;
}

message SupportFaqResponseV2 {
  string category_id = 1 ;
  string category_name = 2;
  string category_icon_url = 3;
  repeated SupportFaqV2 faqs = 4;
  string category_description = 5;
}

message SupportFaqCategoryResponseV2 {
  repeated SupportFaqResponseV2 category_support_faq = 1 ;
  string top_category_id = 2 ;
}

enum FaqType {
  UNKNOWN_FAQ_TYPE = 0;
  DEFAULT_FAQ_TYPE = 1;
  REWARD_FAQ_TYPE = 2;
  REFERRAL_FAQ_TYPE = 3;
  CATEGORY_FAQ_TYPE = 4;
}

message AskQuestionRequest {
  string question = 1;
}

enum BusinessUnit {
  UNKNOWN_BUSINESS_UNIT = 0;
  ALPHA = 1;
  FINSERV = 2;
  BROKING = 3;
}

message GetFaqsRequest {
  BusinessUnit business_unit = 1;
  string namespace = 2;
  string identifier = 3;
}

message GetFaqsResponse {
  repeated Faq faqs = 1;
}

message Cf{
  string cf_gold_member = 1;
  string cf_ticket_cohort = 2;
}
message CreateTicketRequest {
  string sub_category = 1;
  string subject = 2;
  string description = 3;
  repeated string attachments = 4;
  string category = 5;
  Cf cf = 6;
}
message Attachment {
  string id = 1;
  string name = 2;
  string href = 3;
  int32 size = 4;

}
message Ticket {
  string id = 1;
  string status_type = 2;
  string ticket_number = 3;
  string created_time = 4;
  string subject = 5;
  string description = 6;
}
message GetAllTicketsResponse {
  repeated Ticket tickets = 1;
}
enum AuthorType{
  END_USER = 0;
  AGENT = 1;
}
message Thread {
  string id = 1;
  string ticket_id = 2;
  AuthorType authorType = 3;
  string time = 4;
  string content = 5;
  bool hasCompleteContent = 6;
  int32 attachmentCount = 7;
  repeated Attachment attachments = 8;
}
message GetTicketThreads {
  repeated Thread threads = 1;
}
message ChatReply{
  string content = 1;
  repeated string attachmentIds= 3;
}