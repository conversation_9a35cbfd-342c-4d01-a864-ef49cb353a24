syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;


enum LanguageCode {
  UNKNOWN_LANGUAGE_CODE = 0;
  EN = 1;
  HI = 2;
}

enum Step {
  UNKNOWN_STATUS = 0;
  ONBOARDING = 1;
  QUIZ = 2;
  POLL = 3;
}

message OnboardingSubmitRequest {
  repeated UserQuizAnswer answers = 1;
}

message StepResponse {
  Step next_step = 1;
  bool is_poll_eligible = 2;
}

message OnboardingSubmitResponse {
  StepResponse next_step = 1;
}

message QuizStatusResponse {
  StepResponse next_step = 1;
}


message QuizResponse {
  repeated QuizQuestionGroup question_groups = 1;
}

message QuizQuestionGroup {
  string group_id = 1;
  map<int32, LocalizedQuestion> question = 2;
}

message LocalizedQuestion {
  string id = 1;
  string text = 2;
  string correct_option_id = 3;
  repeated QuizOption options = 4;
  string correct_option_explanation = 5;
  string incorrect_option_explanation = 6;
}

message QuizOption {
  string id = 1;
  string text = 2;
}

message GetCorrectOptionResponse {
  string correct_option_id = 1;
}

message UserQuizAnswer {
  string question_id = 1;
  string selected_option_id = 2;
  string selected_option_text = 3;
}

message SubmitQuizRequest {
  repeated UserQuizAnswer answers = 1;
}

message SubmitQuizResponse {
  int32 score = 1;
  StepResponse next_step = 2;
}