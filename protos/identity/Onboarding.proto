syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

import "Kyc.proto";

message UserOnboardingStepsResponse {
  repeated UserStateData data = 2;
}

message UserStateData {
  KycType onboarding_step = 1;
  OnBoardingStatus status = 2;
}

message OnboardingModuleStepsResponse {
  repeated OnboardingModuleSteps data = 1;
}

message OnboardingModuleSteps {
  KycType onboarding_step = 1;
  int32 kyc_sequence = 2;
  string module = 3;
  bool is_skippable = 4;
}

message OnboardingState {
  optional KycType next = 1;
}

message SkipOnboardingResponse {
}

enum OnboardingModule {
  ONBOARDING_MODULE_UNKNOWN = 0;
  APP_ONBOARDING = 1;
//  FIXED_DEPOSIT = 2; [Deprecated]
//  MUTUAL_FUND = 3; [Deprecated]
//  BOND_ONBOARDING = 4; [Deprecated]
}