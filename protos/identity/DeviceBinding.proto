syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

message DeviceBindingRequest {
  string device_id = 1;
  repeated SimData sim_data = 2 ;
}

message SimData {
  string sim_serial_number = 1;
  string sim_name = 2;
}


message DeviceBindingResponse {
  string message = 1;
  string request_id = 2;
  string mobile_number = 3;
}

message InboundSmsRequest{
  string longcode_number = 1;
  string customer_number = 2;
  string price = 8;
  string status = 9;
  string circle = 3;
  int32 company_id = 4;
  string message = 5;
  string keyword = 6;
  string received_at = 7;
}

message GupshupCallbackRequest {
  string keyword = 1;
  string phonecode = 2;
  string location = 3;
  string carrier = 4;
  string content = 5;
  string msisdn = 6;
  string timestamp = 7;
}

message DeviceBindingStatusResponse {
  DeviceBindingStatus status = 1;
  string message = 2;
}

enum DeviceBindingStatus {
  DEVICE_BINDING_STATUS_UNKNOWN = 0;
  DEVICE_BINDING_STATUS_PENDING = 1;
  DEVICE_BINDING_STATUS_APPROVED = 2;
  DEVICE_BINDING_STATUS_REJECTED = 3;
  REQUEST_ID_NOT_FOUND = 4;
}