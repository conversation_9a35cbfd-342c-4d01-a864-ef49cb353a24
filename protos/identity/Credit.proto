syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

enum CreditScoreStatus {
  UNKNOWN_CREDIT_SCORE_STATUS = 0;
  NOT_INITIATED_CREDIT_SCORE_STATUS = 1;
  ACTIVE_CREDIT_SCORE_STATUS = 2;
  CONSENT_REQUIRED_CREDIT_SCORE_STATUS = 3;
  NOT_FOUND_CREDIT_SCORE_STATUS = 4;
}

message CreditReportSummary {
  int32 credit_score = 1;
  int32 min_credit_score = 2;
  int32 max_credit_score = 3;
  string last_updated_date = 4;
  string summary_heading = 5;
  string summary_description = 6;
  string summary_logo_url = 7;
  double credit_score_percentage = 8;
  string heading = 9;
  string experian_logo_url = 10;
  bool refresh_button = 11;
  string refresh_button_cta = 12;
}

message SectionItem {
  string item_logo = 1;
  string item_title = 2;
  string item_description = 3;
  double item_value = 4;
  string item_value_str = 5;
  string item_value_description = 6;
}

message CreditReportSection {
  string section_title = 1;
  repeated SectionItem section_item = 2;
  string bottom_sheet_header = 3;
  string bottom_sheet_header_color = 4;
  string bottom_sheet_description = 5;
  string bottom_sheet_cta = 6;
  int32 size = 7;
}

message CreditScoreStatsDetailSection {
  optional string section_title = 1;
  repeated SectionItem section_item = 2;
}

message CreditScoreStatsDetails {
  repeated CreditScoreStatsDetailSection credit_score_stats_detail_sections = 1;
  string left_heading = 2;
  string right_heading = 3;
  string left_heading_value = 4;
  string right_heading_value = 5;
  string question = 6;
  string answer = 7;
  string cta_text = 8;
}

message CreditScoreStatItem {
  string stat_title = 1;
  string stat_impact = 2;
  string stat_value = 3;
  string stat_rating = 4;
  string stat_rating_color = 5;
  optional CreditScoreStatsDetails credit_score_stats_details = 6;
}

message CreditScoreStats {
  string heading = 1;
  string body = 2;
  repeated CreditScoreStatItem stats = 3;
  string icon_url = 4;
}

message CreditScoreStatsV2 {
  string heading = 1;
  string body = 2;
  repeated CreditScoreStatItem stats = 3;
  string icon_url = 4;
}

message CreditReport {
  CreditReportSummary credit_report_summary = 1;
  repeated CreditReportSection sections = 2;
  optional CreditScoreStats credit_score_stats = 3;
  string experian_url = 4;
  string unique_tr_id = 5;
  string experian_ref_id = 6;
  string experian_cta = 7;
  optional CreditScoreStatsV2 credit_score_stats_v2 = 8;
}

message CreditScoreDashboardResponse {
  CreditScoreStatus credit_score_status = 1;
  string section_title = 2;
  string section_body = 3;
  optional int32 credit_score = 4;
  optional string credit_score_description = 5;
  optional string credit_score_color = 6;
  optional string cta_text = 7;
  optional string last_updated_date = 8;
  optional CreditReport credit_report = 9;
}