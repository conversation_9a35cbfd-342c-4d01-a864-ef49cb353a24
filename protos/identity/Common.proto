syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;

enum MessageType {
  UNKNOWN_MESSAGE_TYPE = 0;
  PROMOTIONAL_MESSAGE = 1;
  TRANSACTIONAL_MESSAGE = 2;
  OTP_MESSAGE = 3;
}

enum ContentType {
  UNKNOWN_CONTENT_TYPE = 0;
  TEXT = 1;
  UNICODE = 2;
}

message ErrorResponse {
  string error_code = 1;
  string error_message = 2;
}

enum OptinStatus {
  UNKNOWN_OPTIN_STATUS = 0;
  OPTED_IN = 1;
  OPTED_OUT = 2;
}

enum WhatsappProviderType {
  UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0;
  GUPSHUP = 1;
}

enum AppConfigType {
  APP_CONFIG_TYPE_UNKNOWN = 0;
  BANK_VERIFICATION_RPD = 1;
  BANK_VERIFICATION_PD = 2;
  BASIC_DETAILS_QUESTIONS = 3;
  PAN_CVL_CHECK = 4;
  RPD_SUPPORTED_UPI_APPS = 5;
  CREDIT_REPORT = 6;
  MOBILE_RESEND = 7;
  MOBILE_ATTEMPT = 8;
  MOBILE_OTP_LENGTH = 9;
  EMAIL_OTP_LENGTH = 10;
  EMAIL_RESEND = 11;
  EMAIL_ATTEMPT = 12;
  ANDROID_APP_VERSION = 13;
  IOS_APP_VERSION = 14;
  WEB_APP_VERSION = 15;
  MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16;
  EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17;
  MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18;
  EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19;
  EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20;
  EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21;
  MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22;
  MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23;
  MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24;
  EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25;
  MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26;
  TOKEN_VALIDITY_IN_HOURS = 27;
  REFRESH_TOKEN_VALIDITY_IN_HOURS = 28;
  ENCRYPTION_PUBLIC_KEY = 29 ;
  ZOHO_CONFIG = 30;
  IN_APP_RATING_CONFIG = 31;
  GT_GRACE_PERIOD_IN_DAYS = 32;
  RSA_ENCRYPTION_PUBLIC_KEY = 33;
  LOCALIZATION_CALL = 34;
  INVESTIBLE_BANK_IDS = 35;
  BANK_SUPPORT_NUMBERS = 36;
  ENABLE_SENTRY = 37;
  GUPSHUP_SIM_BINDING = 38;
  SCC_DELIVERY_REQUEST_DOCUMENT = 39;
}

enum AppConfigValueType {
  APP_CONFIG_VALUE_TYPE_UNKNOWN = 0;
  TEXT_TYPE = 1;
  BOOLEAN_TYPE = 2;
  STRING_TYPE = 3;
  JSON_TYPE = 4;
  INTEGER_TYPE = 5;
}

message ConfigDataResponse {
  repeated ConfigData data = 1;
}

message ConfigData {
  AppConfigType config_name = 1;
  string config_value = 2;
  AppConfigValueType config_type = 3;
  int32 min_version = 4;
  int32 max_version = 5;
  string description = 6;
}

enum PushNotificationType {
  UNKNOWN_PUSH_NOTIFICATION_TYPE = 0;
  TD_BOOKED_COMPLETE = 1;
  PAYMENT_SUCCESS = 2;
  TD_RESUME = 3;
  PAYMENT_FAILURE = 4;
  VKYC_SUCCESS = 5;
  VKYC_REMINDER = 6;
  VKYC_RETRY = 7;
  TD_WITHDRAWAL_COMPLETE = 8;
  TD_REJECTED = 9;
  TD_MATURITY = 10;
  TD_BOOKED_ACK = 11;
  PROGRESS_SAVED = 12;
  INVEST_INSTANT_FDS = 13;
  TICKET_ADDED = 14;
  NTB_AUTH_ERROR = 15;
  ETB_AUTH_ERROR = 16;
  BINDING_SUCCESS = 17;
  VKYC_FAILURE = 18;
  TD_MATURITY_INSTRUCTION_UPDATE_DUE = 19;
  TD_RENEWAL_COMPLETE = 20;
}

message PaginationFilter {
  int32 page = 1;
  optional int32 size = 2;
}

message DataKey {
  enum VariableType {
    UNKNOWN = 0;
    STRING = 1;
    CURRENCY = 2;
    DATE = 3;
    DATE_TIME = 4;
    USER_FIRST_NAME = 5;
    SHORT_CURRENCY = 6;
    PERCENT = 7;
    PERCENT_2F = 8;
    NUMBER = 9;
    COUNT_DOWN = 10;
  }
  message Variable {
    string name = 1;
    VariableType type = 2;
    string value = 3;
  }
  string key = 1;
  repeated Variable context_variables = 3;
}

message PaginationResponse {
  bool hasNextPage = 1;
}

message PaginationRequest {
  int32 page = 1;
  int32  size = 2;
}