syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;


enum DeviceType {
  CLIENT_TYPE_UNKNOWN = 0;
  ANDROID = 1;
  IOS = 2;
  WEB = 3;
  MOBILE_WEB = 4;
}

message UserDevice {
  string id = 1 ;
  DeviceType device_type = 2;
  string os_version = 3;
  string model = 4;
  string app_version = 5;
  string last_active_time = 6;
  string notification_token = 7;
  string aa_id = 8;
  optional string idfv = 9;
  optional string appsflyer_id = 10;
  optional string app_version_name = 11;
  optional  bool aie = 12;
  optional int64 att = 13;
  optional string app_store = 14;
  optional string app_instance_id = 16;
}

message UpdateDeviceRequest {
  UserDevice user_device = 1;
}


message UpdateDeviceResponse {
}