syntax = "proto3";

package com.stablemoney.api.identity;
option java_package = "com.stablemoney.api.identity";
option java_multiple_files = true;


enum CampaignType {
  UNKNOWN_CAMPAIGN_TYPE = 0;
  FUNDING_SWEETS_CAMPAIGN_TYPE = 1;
  EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE = 2;
  DEFAULT_REFERRAL_CAMPAIGN_TYPE = 3;
  T_SHIRT_CAMPAIGN_TYPE = 4;
  BONDS_REFERRAL_CAMPAIGN_TYPE = 5;
}

enum GiftOrderStatus {
  UNKNOWN_GIFT_ORDER_STATUS = 0;
  ORDER_PLACED_GIFT_ORDER_STATUS = 1;
  FAILED_GIFT_ORDER_STATUS = 2;
  INITIATED_GIFT_ORDER_STATUS = 3;
}

message Address {
  string address_line1 = 1;
  optional string address_line2 = 2;
  optional string address_line3 = 3;
  string city_id = 4;
  string pin_code = 5;
}

message FundingCampaignMetadata {
  GiftOrderStatus order_status = 1;
  bool can_user_refer = 2;
  int32 referrals_left_count = 3;
  optional string tracking_link_url = 4;
}

message TShirtCampaignMetadata {
  GiftOrderStatus gift_order_status = 1;
  optional Address address = 2;
  optional string t_shirt_size = 3;
  int32 eligibility_referral_count = 4;
  int32 current_referral_count = 5;
  string campaign_referral_link = 6;
}

message BondsReferralCampaignMetadata {
  string campaign_referral_link = 1;
  string share_text = 2;
}

message KbcReferralCampaignMetadata {
  string campaign_referral_link = 1;
  string share_text = 2;
}

message CampaignResponse {
  CampaignType campaign_type = 1;
  bool is_user_eligible = 2;
  oneof metadata {
    FundingCampaignMetadata funding_campaign_metadata = 3;
    TShirtCampaignMetadata t_shirt_campaign_metadata = 4;
    BondsReferralCampaignMetadata bonds_referral_campaign_metadata = 5;
    KbcReferralCampaignMetadata kbc_referral_campaign_metadata = 6;
  }
}

message TShirtCampaignRequest {
  optional string t_shirt_size = 1;
  optional Address address = 2;
}

message PlaceOrderRequest {
  CampaignType campaign_type = 1;
  oneof metadata {
    TShirtCampaignRequest t_shirt_campaign_request = 2;
  }
}

message PlaceOrderResponse {
  GiftOrderStatus orderStatus = 1;
}